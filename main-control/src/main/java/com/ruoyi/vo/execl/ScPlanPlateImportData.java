package com.ruoyi.vo.execl;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * @author:psy
 * @create: 2024-02-28 10:57
 * @Description:
 */
@Data
public class ScPlanPlateImportData {

    @Excel(name = "总重量(kg)")
    private float plateTotalWeight;
    @Excel(name = "板材总数")
    private int plateTotalNum;
    @Excel(name = "套料图编号")
    private String drawCode;
    @Excel(name = "套料图路径(不填则默认当天同编码文件)")
    private String dxfFilePath;
    @Excel(name = "切割路径(不填则默认当天同编码文件)")
    private String ncFilePath;
    @Excel(name = "钢板长(mm)")
    private Integer length;
    @Excel(name = "钢板宽(mm)")
    private Integer width;
    @Excel(name = "钢板厚(mm)")
    private Integer thickness;
    @Excel(name = "材质")
    private String material;
    @Excel(name = "生产要求日期")
    private String requireTime;
    @Excel(name = "备注")
    private String remark;
}
