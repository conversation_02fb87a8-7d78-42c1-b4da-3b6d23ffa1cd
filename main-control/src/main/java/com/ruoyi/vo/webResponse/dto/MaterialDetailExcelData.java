package com.ruoyi.vo.webResponse.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @date 2024年10月17日 下午2:53
 */
@Data
public class MaterialDetailExcelData {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 采购单编号
     */
    private String upperIndex;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 批次
     */
    private String batch;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String produceDate;

    /**
     * 物料位置
     */
    private String materialLocation;

    /**
     * 0合格，1不合格
     */
    private Integer isCompliant;

    /**
     * 备注
     */
    private String remark;

    /**
     * 来源物料编码
     */
    private String orignMaterialCode;

    /**
     * 容器编码（出）
     */
    private String orignContainer;

    /**
     * 批次（出）
     */
    private String orignBatch;

    /**
     * 生产日期（出）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd",timezone="GMT+8")
    private String orignProduceDate;

    /**
     * 上位编号（出）
     */
    private String orignUpperIndex;

    /**
     * 目的物料编码
     */
    private String destMaterialCode;

    /**
     * 容器编码（入）
     */
    private String destContainer;

    /**
     * 批次（入）
     */
    private String destBatch;

    /**
     * 生产日期（入）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd",timezone="GMT+8")
    private String destProduceDate;

    /**
     * 上位编号（入）
     */
    private String destUpperIndex;
    /**
     * 来源物料名称
     */
    private String orignMaterialName;
    /**
     * 目的物料名称
     */
    private String destMaterialName;


}
