package com.ruoyi.vo.webResponse.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: lhb
 * @CreateDate: 2025/3/21 11:55
 * @Description: 工作站信息
 */
@Data
public class BasicWorkstationInfoDto {

    @Id
    private String id;

    /**
     * 工作站编码
     */
    private String workstationCode;

    /**
     * 工作站名称
     */
    private String workstationName;

    /**
     * 工作站地点
     */
    private String workstationAddress;

    /**
     * 所在车间编码
     */
    private String workshopCode;

    /**
     * 车间名称
     */
    private String workshopName;

    /**
     * 工序编码
     */
    private String processNo;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 是否启用 0 启用 1不启用
     */
    private Integer enableFlag;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
