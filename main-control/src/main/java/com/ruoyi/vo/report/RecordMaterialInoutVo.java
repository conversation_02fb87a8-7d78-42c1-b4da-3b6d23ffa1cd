package com.ruoyi.vo.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料进出记录查询实体
 */
@Data
public class RecordMaterialInoutVo {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 单据编码
     */
    @Excel(name = "单据编码", sort = 1, width = 20)
    private String boundIndex;

    /**
     * 单据类型，1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库 9物料调拨 10库存盘点 11仓库用料
     */
    @Excel(name = "单据类型", sort = 6, readConverterExp = "1=生产领料,2=生产补料,3=生产入库,4=生产退料,5=采购入库,6=采购退货出库,7=销售出库,8=销售退货入库,9=物料调拨,10=库存盘点,11=仓库用料")
    private Integer boundType;

    /**
     * 数据来源0页面，1MES，2ERP
     */
    @Excel(name = "数据来源", sort = 8, readConverterExp = "0=页面增加,1=MES,2=ERP", width = 10)
    private Integer dataOrigin;

    /**
     * 出入类型 0：入库，1：出库
     */
    @Excel(name = "出入类型", readConverterExp = "1=出库,0=入库", sort = 7, width = 10)
    private Integer inoutType;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码", sort = 2)
    private String materialCode;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称", sort = 3)
    private String materialName;

    /**
     * 物料数量
     */
    @Excel(name = "物料数量", sort = 4)
    private Integer totalNum;


    /**
     * 容器编码
     */
    @Excel(name = "容器编码", sort = 9)
    private String containerCode;

    /**
     * 批次
     */
    private String batch;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 上位编号
     */
    @Excel(name = "数据源单号", sort = 10)
    private String upperIndex;


    /**
     * 备注
     */
    @Excel(name = "备注", sort = 14)
    private String remark;

    /**
     * 记录日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "记录时间", width = 25, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 11)
    private Date recordDate;

    /**
     * 记录人
     */
    @Excel(name = "锁单人", sort = 12)
    private String recorder;

    /**
     * 锁单日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "锁单时间", width = 25, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 13)
    private Date lockTime;

    /**
     * 物料位置
     */
    @Excel(name = "位置", sort = 5)
    private String materialLocation;



    /**
     * 物料图片
     */
    private String materialImg;

}