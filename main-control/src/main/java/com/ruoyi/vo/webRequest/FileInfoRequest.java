package com.ruoyi.vo.webRequest;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
public class FileInfoRequest {
    @NotEmpty(message = "文件ID不能为空！")
    private String fileId;
    private String uploadUser;//上传者
    private String remark;//备注
    @NotEmpty(message = "数据主键ID不能为空！")
    private String dataPrimaryId;//关联数据表的主键id
    @NotEmpty(message = "文件名称不能为空！")
    private String fileName;//文件名称',
    @NotEmpty(message = "文件路径不能为空！")
    private String filePath;//文件路径
    private String recordTime;
}
