package com.ruoyi.vo.mes;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BasicProductInfoDto {
    /**
     * 主键id
     */
    private String id;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 分类编码
     */
    private String classifyCode;
    /**
     * 分类名称
     */
    private String classifyName;
    /**
     * 规格型号
     */
    private String specifications;
    /**
     * 采购单位
     */
    private String purchaseUnit;
    /**
     * 生产单位
     */
    private String produceUnit;
    /**
     * 产品图片
     */
    private String productImg;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 是否外购，0自产 1外购 2均可
     */
    private Integer isOutSource;
    /**
     * 是否锁定，0否，1是
     */
    private Integer isLock;
    /**
     * 保质期
     */
    private String expiryDate;
    /**
     * 最低库存
     */
    private int minInventory;
    /**
     * 最高库存
     */
    private int maxInventory;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateName;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
