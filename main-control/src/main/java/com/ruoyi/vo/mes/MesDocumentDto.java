package com.ruoyi.vo.mes;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * mes物料数据传输对象
 */
@Data
public class MesDocumentDto {
    /**
     * 单据编号
     */
    @NotBlank(message = "单据编号不能为空")
    private String transactionCode;

    /**
     * 交易类型 0:入库 1:出库 2：冻结
     */
    @NotNull(message = "交易类型不能为空")
    private Integer transactionType;
    /**
     * 单据类型:1:生产领料 2:生产补料 3:生产入库 4:生产退料 5:生产退库 6：冻结物料
     */
    @NotNull(message = "单据类型不能为空")
    private Integer businessType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receTime;

    /**
     * 物料明细列表
     */
    @NotNull(message = "物料详情不能为空")
    private List<DocumentDetailDto> details;
}