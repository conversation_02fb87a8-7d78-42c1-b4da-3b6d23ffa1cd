package com.ruoyi.vo.qc;

import com.ruoyi.domain.qc.QcTemplateDetail;
import com.ruoyi.domain.qc.QcTemplateMaterial;
import lombok.Data;

import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/27 10:11
 * @Description: 类描述
 */
@Data
public class QcTemplateAndDetailReq {

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 质检类型
     * 1：来料质检，2：工序质检 3：入库质检 4：出库质检
     */
    private Integer qcType;

    /**
     * 是否启用 0未启用 1启用
     */
    private Integer enableFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 模板明细集合
     */
    private List<QcTemplateDetail> templateDetails;
    /**
     * 物料模板集合
     */
    private List<QcTemplateMaterial> materialList;

}
