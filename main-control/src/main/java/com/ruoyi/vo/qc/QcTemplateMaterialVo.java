package com.ruoyi.vo.qc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/23 9:40
 * @Description: 类描述
 */

@Data
public class QcTemplateMaterialVo {

    private String id;

    /**
     * 检验模板编码
     */
    private String templateCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 最低检测数
     */
    private Integer quantityCheck;

    /**
     * 最大不合格数
     */
    private Integer quantityUnqualified;

    /**
     * 最大致命缺陷率
     */
    private Float crRate;

    /**
     * 最大严重缺陷率
     */
    private Float majRate;

    /**
     * 最大轻微缺陷率
     */
    private Float minRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specifications;
    /**
     * 物料材质
     */
    private String  texture;
    /**
     * 采购单位
     */
    private String purchaseUnit;

}
