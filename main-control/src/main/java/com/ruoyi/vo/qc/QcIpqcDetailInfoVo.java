package com.ruoyi.vo.qc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 11:04
 * @Description: 质检详情
 */
@Data
public class QcIpqcDetailInfoVo {

    private String id;
    /**
     * 质检编码
     */
    private String ipqcCode;

    /**
     * 模板详情ID
     */
    private String templateDetailId;

    /**
     * 检测项编码
     */
    private String itemCode;

    /**
     * 致命缺陷数量
     */
    private Integer crQuantity;

    /**
     * 严重缺陷数量
     */
    private Integer majQuantity;

    /**
     * 轻微缺陷数量
     */
    private Integer minQuantity;
    /**
     * 参考致命缺陷数量
     */
    private Integer egCrQuantity;

    /**
     * 参考严重缺陷数量
     */
    private Integer egMajQuantity;

    /**
     * 参考轻微缺陷数量
     */
    private Integer egMinQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    /**
     * 检测要求
     */
    private  String checkMethod;
    /**
     * 标准值
     */
    private  Float standerVal;
    /**
     * 单位
     */
    private  String unitOfMeasure;
    /**
     * 误差上限
     */
    private  String thresholdMax;
    /**
     * 误差下限
     */
    private  String thresholdMin;

    /**
     * 质检值
     */
    private String qcVal;

    /**
     * 质检项名称
     */
    private String itemName;

    /**
     * 质检工具
     */
    private String qcTool;

    /**
     * 质检值类型
     * 0 布尔类型 1数字类型 2string类型 3选项式类型 4文件类型 5图片类型
     */
    private Integer qcResultType;

    /**
     * 值属性
     */
    private String qcResultSpc;
    /**
     * 文件名
     */
    private String fileName;



}
