package com.ruoyi.vo.erp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * ERP分组查询响应的最外层对象
 */
@Data
public class ErpGroupQueryResponse {

    @JsonProperty("Result")
    private ResultData result;

    @Data
    public static class ResultData {
        @JsonProperty("ResponseStatus")
        private ResponseStatus responseStatus;
        @JsonProperty("Id")
        private int id;
        @JsonProperty("Result")
        private ErpGroupDataResult result;
    }

    @Data
    public static class ResponseStatus {
        @JsonProperty("ErrorCode")
        private int errorCode;
        @JsonProperty("IsSuccess")
        private boolean isSuccess;
        @JsonProperty("Errors")
        private List<Object> errors;
        @JsonProperty("SuccessEntitys")
        private List<Object> successEntitys;
        @JsonProperty("SuccessMessages")
        private List<Object> successMessages;
        @JsonProperty("MsgCode")
        private int msgCode;
    }
} 