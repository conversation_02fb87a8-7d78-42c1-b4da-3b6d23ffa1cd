package com.ruoyi.vo.erp.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @author:lhb
 * @create: 2023-09-23 09:32
 * @Description: Erp响应状态
 */
@Data
public class ErpResponseStatus  {

    @JsonProperty("ErrorCode")
    @SerializedName("ErrorCode")
    private String ErrorCode;

    @JsonProperty("IsSuccess")
    @SerializedName("IsSuccess")
    //是否成功
    private Boolean IsSuccess;

    @SerializedName("Errors")
    @JsonProperty("Errors")
    private List<ErpResponseErrors> Errors;

    @SerializedName("SuccessMessages")
    @JsonProperty("SuccessMessages")
    private List<ErpResponseSuccessEntitys> SuccessMessages;

    @SerializedName("MsgCode")
    @JsonProperty("MsgCode")
    private String MsgCode;


}
