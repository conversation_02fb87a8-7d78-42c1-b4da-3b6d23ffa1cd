package com.ruoyi.vo.erp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.ruoyi.vo.erp.deserializer.ErpIdDeserializer;
import lombok.Data;

/**
 * ERP物料分组信息VO, 精简后只包含ERP实际返回的字段
 */
@Data
public class ErpMaterialGroupVo {

    /**
     * ERP内部ID
     */
    @JsonProperty("Fid")
    @JsonDeserialize(using = ErpIdDeserializer.class)
    private Long fid;

    /**
     * 父级ID
     */
    @JsonProperty("FParentid")
//    @JsonDeserialize(using = ErpIdDeserializer.class)
    private Long parentId;

    /**
     * 编码
     */
    @JsonProperty("FNumber")
    private String number;

    /**
     * 名称
     */
    @JsonProperty("FName")
    private String name;
}