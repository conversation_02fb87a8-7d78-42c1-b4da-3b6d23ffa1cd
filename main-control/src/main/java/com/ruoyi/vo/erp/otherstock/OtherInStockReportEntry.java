package com.ruoyi.vo.erp.otherstock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 其他入库单明细实体
 * <AUTHOR>
 */
@Data
public class OtherInStockReportEntry {

    /**
     * 实体主键
     */
    @JsonProperty("FEntryID")
    private Integer fEntryID;

    /**
     * 入库类型
     */
    @JsonProperty("FInStockType")
    private String fInStockType;

    /**
     * 物料编码
     */
    @JsonProperty("FMATERIALID")
    private FNumber fMaterialId;

    /**
     * 辅助属性
     */
    @JsonProperty("FAuxPropId")
    private FAuxPropId fAuxPropId;

    /**
     * 单位
     */
    @JsonProperty("FUnitID")
    private FNumber fUnitId;

    /**
     * 收货仓库
     */
    @JsonProperty("FSTOCKID")
    private FNumber fStockId;

    /**
     * 仓位
     */
    @JsonProperty("FStockLocId")
    private FStockLocId fStockLocId;

    /**
     * 库存状态
     */
    @JsonProperty("FSTOCKSTATUSID")
    private FNumber fStockStatusId;

    /**
     * 批号
     */
    @JsonProperty("FLOT")
    private FNumber fLot;

    /**
     * 实收数量
     */
    @JsonProperty("FQty")
    private BigDecimal fQty;

    /**
     * 备注
     */
    @JsonProperty("FEntryNote")
    private String fEntryNote;

    /**
     * 源单编号
     */
    @JsonProperty("FSRCBILLNO")
    private String fSrcBillNo;

    /**
     * 源单类型
     */
    @JsonProperty("FSRCBILLTYPEID")
    private String fSrcBillTypeId;

    /**
     * BOM版本
     */
    @JsonProperty("FBOMID")
    private FNumber fBomId;

    /**
     * 生产日期
     */
    @JsonProperty("FPRODUCEDATE")
    private String fProduceDate;

    /**
     * 计划跟踪号
     */
    @JsonProperty("FMTONO")
    private String fMtoNo;

    /**
     * 辅单位
     */
    @JsonProperty("FExtAuxUnitId")
    private FNumber fExtAuxUnitId;

    /**
     * 实收数量（辅单位）
     */
    @JsonProperty("FExtAuxUnitQty")
    private BigDecimal fExtAuxUnitQty;

    /**
     * 项目编号
     */
    @JsonProperty("FProjectNo")
    private String fProjectNo;

    /**
     * 货主类型
     */
    @JsonProperty("FOWNERTYPEID")
    private String fOwnerTypeId;

    /**
     * 货主
     */
    @JsonProperty("FOWNERID")
    private FNumber fOwnerId;

    /**
     * 保管者类型
     */
    @JsonProperty("FKEEPERTYPEID")
    private String fKeeperTypeId;

    /**
     * 一级设计模块
     */
    @JsonProperty("F_PCZO_Base9")
    private FNumberBig fPczoBase9;

    /**
     * 二级设计模块
     */
    @JsonProperty("F_PCZO_Base10")
    private FNumberBig fPczoBase10;

    /**
     * 保管者
     */
    @JsonProperty("FKEEPERID")
    private FNumber fKeeperId;

    /**
     * 三级设计模块
     */
    @JsonProperty("F_PCZO_Base11")
    private FNumberBig fPczoBase11;

    /**
     * 入库日期
     */
    @JsonProperty("FInstockDate")
    private String fInstockDate;

    /**
     * 项目(明细)
     */
    @JsonProperty("F_BTXM")
    private FNumberBig fBtxm;

    /**
     * 项目阶段
     */
    @JsonProperty("F_PCZO_Assistant_tzk")
    private FNumber fPczoAssistantTzk;

    /**
     * 序列号子单据体
     */
    @JsonProperty("FSerialSubEntity")
    private List<FSerialSubEntity> fSerialSubEntity;

    /**
     * 辅助属性
     */
    @Data
    public static class FAuxPropId {
        @JsonProperty("FAUXPROPID__FF100001")
        private FNumber fauxPropIdFf100001;
    }

    /**
     * 仓位信息
     */
    @Data
    public static class FStockLocId {
        /**
         * 楼层
         */
        @JsonProperty("FSTOCKLOCID__FF100008")
        private FNumber fStockLocIdFf100008;

        /**
         * 货架
         */
        @JsonProperty("FSTOCKLOCID__FF100009")
        private FNumber fStockLocIdFf100009;

        /**
         * 货位号
         */
        @JsonProperty("FSTOCKLOCID__FF100010")
        private FNumber fStockLocIdFf100010;

        /**
         * 区域
         */
        @JsonProperty("FSTOCKLOCID__FF100012")
        private FNumber fStockLocIdFf100012;

        /**
         * 位置
         */
        @JsonProperty("FSTOCKLOCID__FF100013")
        private FNumber fStockLocIdFf100013;
    }

    /**
     * 序列号子单据体
     */
    @Data
    public static class FSerialSubEntity {
        /**
         * 实体主键
         */
        @JsonProperty("FDetailID")
        private Integer fDetailID;

        /**
         * 序列号
         */
        @JsonProperty("FSerialNo")
        private String fSerialNo;

        /**
         * 备注
         */
        @JsonProperty("FSerialNote")
        private String fSerialNote;
    }
}
