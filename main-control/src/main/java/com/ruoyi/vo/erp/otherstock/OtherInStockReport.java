package com.ruoyi.vo.erp.otherstock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.util.List;

/**
 * 其他入库单上报实体
 * <AUTHOR>
 */
@Data
public class OtherInStockReport {

    /**
     * 单据编号
     */
    @JsonProperty("FBillNo")
    private String fBillNo;

    /**
     * 单据类型
     */
    @JsonProperty("FBillTypeID")
    private FNumber fBillTypeID;

    /**
     * 库存组织
     */
    @JsonProperty("FStockOrgId")
    private FNumber fStockOrgId;

    /**
     * 库存方向
     */
    @JsonProperty("FStockDirect")
    private String fStockDirect;

    /**
     * 日期
     */
    @JsonProperty("FDate")
    private String fDate;

    /**
     * 供应商
     */
    @JsonProperty("FSUPPLIERID")
    private FNumber fSupplierId;

    /**
     * 部门
     */
    @JsonProperty("FDEPTID")
    private FNumber fDeptId;

    /**
     * 验收员
     */
    @JsonProperty("FACCEPTANCE")
    private FStaffNumber fAcceptance;

    /**
     * 仓管员
     */
    @JsonProperty("FSTOCKERID")
    private FNumber fStockerId;

    /**
     * 库存组
     */
    @JsonProperty("FSTOCKERGROUPID")
    private FNumber fStockerGroupId;

    /**
     * 货主类型(表头)
     */
    @JsonProperty("FOwnerTypeIdHead")
    private String fOwnerTypeIdHead;

    /**
     * 货主(表头)
     */
    @JsonProperty("FOwnerIdHead")
    private FNumber fOwnerIdHead;

    /**
     * 备注
     */
    @JsonProperty("FNOTE")
    private String fNote;

    /**
     * 序列号上传
     */
    @JsonProperty("FScanBox")
    private String fScanBox;

    /**
     * 入库类型
     */
    @JsonProperty("F_PCZO_Assistant")
    private FNumber fPczoAssistant;

    /**
     * 产品名称
     */
    @JsonProperty("F_PCZO_Base")
    private FNumberBig fPczoBase;

    /**
     * 项目(表头)
     */
    @JsonProperty("F_PCZO_Base1")
    private FNumberBig fPczoBase1;

    /**
     * 退货类型
     */
    @JsonProperty("F_PCZO_Combo_qtr")
    private String fPczoComboQtr;

    /**
     * 明细信息
     */
    @JsonProperty("FEntity")
    private List<OtherInStockReportEntry> fEntity;

    /**
     * FStaffNumber类型字段
     */
    @Data
    public static class FStaffNumber {
        @JsonProperty("FStaffNumber")
        private String fStaffNumber;

        public static FStaffNumber of(String staffNumber) {
            FStaffNumber fn = new FStaffNumber();
            fn.setFStaffNumber(staffNumber);
            return fn;
        }
    }
}
