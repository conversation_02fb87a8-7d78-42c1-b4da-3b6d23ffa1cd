package com.ruoyi.vo.erp.otherstock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.util.List;

/**
 * 其他出库单上报实体
 * <AUTHOR>
 */
@Data
public class OtherOutStockReport {

    /**
     * 单据编号
     */
    @JsonProperty("FBillNo")
    private String fBillNo;

    /**
     * 单据类型
     */
    @JsonProperty("FBillTypeID")
    private FNumberBig fBillTypeID;

    /**
     * 库存组织
     */
    @JsonProperty("FStockOrgId")
    private FNumber fStockOrgId;

    /**
     * 库存方向
     */
    @JsonProperty("FStockDirect")
    private String fStockDirect;

    /**
     * 日期
     */
    @JsonProperty("FDate")
    private String fDate;

    /**
     * 客户
     */
    @JsonProperty("FCustId")
    private FNumber fCustId;

    /**
     * 领料部门
     */
    @JsonProperty("FDeptId")
    private FNumber fDeptId;

    /**
     * 领料人
     */
    @JsonProperty("FPickerId")
    private FStaffNumber fPickerId;

    /**
     * 业务类型
     */
    @JsonProperty("FBizType")
    private String fBizType;

    /**
     * 货主类型(表头)
     */
    @JsonProperty("FOwnerTypeIdHead")
    private String fOwnerTypeIdHead;

    /**
     * 货主(表头)
     */
    @JsonProperty("FOwnerIdHead")
    private FNumber fOwnerIdHead;

    /**
     * 备注
     */
    @JsonProperty("FNote")
    private String fNote;

    /**
     * 序列号上传
     */
    @JsonProperty("FScanBox")
    private String fScanBox;

    /**
     * 业务类型
     */
    @JsonProperty("F_PCZO_Assistant")
    private FNumber fPczoAssistant;

    /**
     * 供应商
     */
    @JsonProperty("F_PCZO_Base")
    private FNumber fPczoBase;

    /**
     * 产品名称
     */
    @JsonProperty("F_PCZO_Base1")
    private FNumber fPczoBase1;

    /**
     * 交货地
     */
    @JsonProperty("F_PCZO_Combo_qtr")
    private String fPczoComboQtr;

    /**
     * 明细信息
     */
    @JsonProperty("FEntity")
    private List<OtherOutStockReportEntry> fEntity;

    /**
     * FStaffNumber类型字段
     */
    @Data
    public static class FStaffNumber {
        @JsonProperty("FStaffNumber")
        private String fStaffNumber;

        public static FStaffNumber of(String staffNumber) {
            FStaffNumber fn = new FStaffNumber();
            fn.setFStaffNumber(staffNumber);
            return fn;
        }
    }
}
