package com.ruoyi.vo.erp.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @author:lhb
 * @create: 2023-09-29 11:56
 * @Description: 下发业务操作
 */
@Data
public class SendYwOpter {

    @JsonProperty("CreateOrgId")
    private int CreateOrgId;
    /**
     * 单据编号
     */
    @JsonProperty("Numbers")
    private String[] Numbers;
    /**
     * 单据主键
     */
    @JsonProperty("Ids")
    private String Ids;
    /**
     * 单据内码与分录内码对应关系的集合
     */
    @JsonProperty("PkEntryIds")
    private List<PkEntryId> PkEntryIds;

}
