package com.ruoyi.vo.erp.response;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @create: 2025-07-18
 * @Description: ERP下推响应结果
 */
@Data
public class PushResponse {

    @SerializedName("Result")
    private PushResult result;

    @Data
    public static class PushResult {
        @SerializedName("ResponseStatus")
        private ResponseStatus responseStatus;
        @SerializedName("ConvertResponseStatus")
        private ResponseStatus convertResponseStatus;
    }

    @Data
    public static class ResponseStatus {
        @SerializedName("IsSuccess")
        private Boolean isSuccess;
        @SerializedName("Errors")
        private List<ErrorInfo> errors;
        @SerializedName("SuccessEntitys")
        private List<SuccessEntity> successEntitys;
        @SerializedName("SuccessMessages")
        private List<Object> successMessages;
        @SerializedName("MsgCode")
        private Integer msgCode;
        @SerializedName("ErrorCode")
        private Integer errorCode;
    }

    @Data
    public static class ErrorInfo {
        @SerializedName("FieldName")
        private String fieldName;
        @SerializedName("Message")
        private String message;
        @SerializedName("DIndex")
        private Integer dIndex;
    }

    @Data
    public static class SuccessEntity {
        @SerializedName("Id")
        private Long id;
        @SerializedName("Number")
        private String number;
        @SerializedName("DIndex")
        private Integer dIndex;
        @SerializedName("EntryIds")
        private EntryIds entryIds;
    }

    @Data
    public static class EntryIds {
        @SerializedName("FInStockEntry")
        private List<Long> fInStockEntry;
    }

    /**
     * 获取下推成功的单据编号列表
     * @return 单据编号列表
     */
    public List<String> getSuccessNumbers() {
        if (result != null && result.responseStatus != null && result.responseStatus.successEntitys != null) {
            return result.responseStatus.successEntitys.stream()
                    .map(SuccessEntity::getNumber)
                    .filter(number -> number != null && !number.trim().isEmpty())
                    .collect(java.util.stream.Collectors.toList());
        }
        return java.util.Collections.emptyList();
    }

    /**
     * 判断下推是否成功
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return result != null && 
               result.responseStatus != null && 
               Boolean.TRUE.equals(result.responseStatus.isSuccess);
    }

    /**
     * 获取错误信息
     * @return 错误信息列表
     */
    public List<ErrorInfo> getErrors() {
        if (result != null && result.responseStatus != null && result.responseStatus.errors != null) {
            return result.responseStatus.errors;
        }
        return java.util.Collections.emptyList();
    }

    /**
     * 获取错误消息字符串
     * @return 格式化的错误消息
     */
    public String getErrorMessages() {
        List<ErrorInfo> errors = getErrors();
        if (errors == null || errors.isEmpty()) {
            // 如果没有错误信息，检查是否有其他信息可以显示
            if (result != null && result.responseStatus != null) {
                return "操作失败，错误代码: " + result.responseStatus.getErrorCode();
            }
            return "未知错误";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < errors.size() && i < 5; i++) { // 只显示前5个错误
            ErrorInfo error = errors.get(i);
            if (error != null && error.getMessage() != null) {
                if (i > 0) {
                    sb.append("; ");
                }
                sb.append(error.getMessage());
            }
        }

        if (errors.size() > 5) {
            sb.append("; 等共").append(errors.size()).append("个错误");
        }

        return sb.length() > 0 ? sb.toString() : "解析错误信息失败";
    }
}
