package com.ruoyi.vo.erp.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * ERP ID字段自定义反序列化器
 * 处理ERP返回的ID字段可能是Long类型或复杂对象的情况
 */
public class ErpIdDeserializer extends JsonDeserializer<Long> {
    
    private static final Logger logger = LoggerFactory.getLogger(ErpIdDeserializer.class);

    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonNode node = p.getCodec().readTree(p);
        
        if (node == null || node.isNull()) {
            return null;
        }
        
        // 如果是数字类型，直接返回
        if (node.isNumber()) {
            return node.asLong();
        }
        
        // 如果是字符串类型，尝试解析为Long
        if (node.isTextual()) {
            try {
                String text = node.asText();
                if (text == null || text.trim().isEmpty()) {
                    return null;
                }
                return Long.parseLong(text.trim());
            } catch (NumberFormatException e) {
                logger.warn("无法将字符串 '{}' 转换为Long类型", node.asText());
                return null;
            }
        }
        
        // 如果是对象类型，尝试提取ID字段
        if (node.isObject()) {
            // 尝试常见的ID字段名
            String[] possibleIdFields = {"Id", "id", "ID", "FId", "Fid", "fid"};
            for (String fieldName : possibleIdFields) {
                JsonNode idNode = node.get(fieldName);
                if (idNode != null && !idNode.isNull()) {
                    if (idNode.isNumber()) {
                        return idNode.asLong();
                    } else if (idNode.isTextual()) {
                        try {
                            String text = idNode.asText();
                            if (text != null && !text.trim().isEmpty()) {
                                return Long.parseLong(text.trim());
                            }
                        } catch (NumberFormatException e) {
                            logger.warn("无法将对象中的字段 '{}' 值 '{}' 转换为Long类型", fieldName, idNode.asText());
                        }
                    }
                }
            }
            logger.warn("在对象中未找到有效的ID字段，对象内容: {}", node.toString());
            return null;
        }
        
        logger.warn("无法处理的ID字段类型: {}, 值: {}", node.getNodeType(), node.toString());
        return null;
    }
}
