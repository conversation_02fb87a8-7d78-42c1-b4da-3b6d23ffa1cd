package com.ruoyi.vo.erp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErpMaterialInfoVo {
    //实体主键
    @JsonProperty("FMATERIALID")
    private String fMaterialId;
    //物料名称
    @JsonProperty("FName")
    private String fName;
    //物料编码
    @JsonProperty("FNumber")
    private String fNumber;
    //物料分组  对应MES：物料大类、是否发货件
    @JsonProperty("FMaterialGroup")
    private String fMaterialGroup;
    //物料分组编码
    @JsonProperty("FBaseProperty")
    private String fBaseProperty;
    //规格型号
    @JsonProperty("FSpecification")
    private String fSpecification;
    //基本单位
    @JsonProperty("FBaseUnitId")
    private String fBaseUnitId;
    //采购单位
    @JsonProperty("FPurchaseUnitId")
    private String fPurchaseUnitId;
    //生产单位
    @JsonProperty("FProduceUnitId")
    private String fProduceUnitId;
    //物料属性
    @JsonProperty("FErpClsID")
    private String fErpClsId;
    //允许委外
    @JsonProperty("FIsSubContract")
    private String fIsSubContract;
    //允许生产
    @JsonProperty("FIsProduce")
    private String fIsProduce;
    //允许采购
    @JsonProperty("FIsPurchase")
    private String fIsPurchase;


}