package com.ruoyi.vo.erp.push;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @create: 2025-07-18
 * @Description: ERP下推请求参数
 */
@Data
public class PushRequest {

    /**
     * 单据内码集合，字符串类型，格式："Id1,Id2,..."（使用内码时必录）
     */
    private String ids;

    /**
     * 单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）
     */
    private List<String> numbers;

    /**
     * 分录内码，逗号分隔（分录下推时必录）
     * 注：按分录下推时，单据内码和编码不需要填,否则按整单下推
     */
    private String entryIds;

    /**
     * 转换规则内码，字符串类型（未启用默认转换规则时，则必录）
     */
    private String ruleId;

    /**
     * 目标单据类型内码，字符串类型（非必录）
     */
    private String targetBillTypeId;

    /**
     * 目标组织内码，整型（非必录）
     */
    private Integer targetOrgId;

    /**
     * 目标单据FormId，字符串类型，（启用默认转换规则时，则必录）
     */
    private String targetFormId;

    /**
     * 是否启用默认转换规则，布尔类型，默认false（非必录）
     */
    private Boolean isEnableDefaultRule;

    /**
     * 保存失败时是否暂存，布尔类型，默认false（非必录）
     * 注：暂存的单据是没有编码的
     */
    private Boolean isDraftWhenSaveFail;

    /**
     * 自定义参数，字典类型，格式："{key1:value1,key2:value2,...}"（非必录）
     * 注：传到转换插件的操作选项中，平台不会解析里面的值
     */
    private Map<String, Object> customParams;

    /**
     * 构造方法 - 设置默认值
     */
    public PushRequest() {
        this.ids = "";
        this.entryIds = "";
        this.targetBillTypeId = "";
        this.targetOrgId = 0;
        this.isEnableDefaultRule = false;
        this.isDraftWhenSaveFail = false;
    }

    /**
     * 创建采购订单下推采购入库单的请求
     * @param purchaseOrderNumbers 采购订单编码列表
     * @return PushRequest
     */
    public static PushRequest createPurchaseOrderToInStockRequest(List<String> purchaseOrderNumbers) {
        PushRequest request = new PushRequest();
        request.setNumbers(purchaseOrderNumbers);
        request.setRuleId("PUR_PurchaseOrder-STK_InStock");
        request.setTargetFormId("STK_InStock");
        return request;
    }
}
