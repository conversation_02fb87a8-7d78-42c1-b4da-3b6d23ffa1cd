package com.ruoyi.vo.erp.otherstock;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 其他出库单物料数据传输对象
 * <AUTHOR>
 */
@Data
public class OtherOutStockMaterialDto {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 实发数量
     */
    private BigDecimal quantity;

    /**
     * 批号
     */
    private String lotNumber;

    /**
     * 生产日期
     */
    private String produceDate;

    /**
     * 出库日期
     */
    private String outstockDate;

    /**
     * 仓库编码
     */
    private String stockCode;

    /**
     * 库存状态编码
     */
    private String stockStatusCode;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 位置编码
     */
    private String positionCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 辅助属性-尺码
     */
    private String auxPropSize;

    /**
     * 辅单位编码
     */
    private String extAuxUnitCode;

    /**
     * 辅单位数量
     */
    private BigDecimal extAuxUnitQty;
}
