package com.ruoyi.vo.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ERP上报主表查询VO
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@Data
public class ErpReportMainVo {
    
    /** 主键ID */
    private String id;
    
    /** 上报编号 */
    private String reportNo;
    
    /** 单据编码 */
    private String documentCode;
    
    /** 业务类型 */
    private Integer businessType;
    
    /** 业务类型名称 */
    private String businessTypeName;
    
    /** 事务类型 */
    private Integer transactionType;
    
    /** 事务类型名称 */
    private String transactionTypeName;
    
    /** 上报状态 */
    private Integer reportStatus;
    
    /** 上报状态名称 */
    private String reportStatusName;
    
    /** ERP返回的单据编号 */
    private String erpBillNo;
    
    /** 总数量 */
    private BigDecimal totalQuantity;
    
    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /** 上报时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /** 上报人 */
    private String reporter;
    
    /** 明细数量 */
    private Integer detailCount;
    
    /**
     * 获取业务类型名称
     */
    public String getBusinessTypeName() {
        if (businessType == null) {
            return "";
        }
        switch (businessType) {
            case 1: return "生产领料";
            case 2: return "生产补料";
            case 3: return "生产入库";
            case 4: return "生产退料";
            case 5: return "采购入库";
            case 6: return "采购退货出库";
            case 7: return "销售出库";
            case 8: return "销售退货入库";
            case 9: return "物料调拨";
            case 10: return "库存盘点";
            case 11: return "仓库用料";
            default: return "未知类型";
        }
    }
    
    /**
     * 获取事务类型名称
     */
    public String getTransactionTypeName() {
        if (transactionType == null) {
            return "";
        }
        switch (transactionType) {
            case 0: return "入库";
            case 1: return "出库";
            default: return "未知类型";
        }
    }
    
    /**
     * 获取上报状态名称
     */
    public String getReportStatusName() {
        if (reportStatus == null) {
            return "";
        }
        switch (reportStatus) {
            case 0: return "待上报";
            case 1: return "上报成功";
            case 2: return "上报失败";
            default: return "未知状态";
        }
    }
}
