package com.ruoyi.vo.erp.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * ERP单据查询请求体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErpQueryReq {
    //单据类型
    private String formId;
    //查询字段
    private String fieldKeys;
    //过滤条件
    private String filterString;
    //排序条件
    private String orderString;
    //查询数量
    private int topRowCount;
    //查询起始行
    private int startRow;
    //查询限制数量
    private int limit;

}
