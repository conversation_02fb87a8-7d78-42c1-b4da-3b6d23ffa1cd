package com.ruoyi.vo.erp.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author:lhb
 * @create: 2023-09-21 17:26
 */
@Data
public class ErpTransferReportCommon {

    /**
     * 需要更新的字段，数组类型，格式：[key1,key2,...] （非必录）注（更新单据体字段得加上单据体key）
     */
    @JsonProperty("NeedUpDateFields")
    private String[] NeedUpDateFields;
    /**
     * 需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
     */
    @JsonProperty("NeedReturnFields")
    private String[] NeedReturnFields;
    /**
     * 是否删除已存在的分录，布尔类型，默认true（非必录）
     */
    @JsonProperty("IsDeleteEntry")
    private String IsDeleteEntry;
    /**
     * 表单所在的子系统内码，字符串类型（非必录）
     */
    @JsonProperty("SubSystemId")
    private String SubSystemId;
    /**
     * 是否验证所有的基础资料有效性，布尔类，默认false（非必录）
     */
    @JsonProperty("IsVerifyBaseDataField")
    private String IsVerifyBaseDataField;
    /**
     * 是否批量填充分录，默认true（非必录）
     */
    @JsonProperty("IsEntryBatchFill")
    private String IsEntryBatchFill;
    /**
     * 是否验证标志，布尔类型，默认true（非必录）
     */
    @JsonProperty("ValidateFlag")
    private String ValidateFlag;
    /**
     * 是否用编码搜索基础资料，布尔类型，默认true（非必录）
     */
    @JsonProperty("NumberSearch")
    private String NumberSearch;
    /**
     * 是否自动调整JSON字段顺序，布尔类型，默认false（非必录）
     */
    @JsonProperty("IsAutoAdjustField")
    private String IsAutoAdjustField;
    /**
     * 交互标志集合，字符串类型，分号分隔，格式："flag1;flag2;..."（非必录） 例如（允许负库存标识：STK_InvCheckResult）
     */
    @JsonProperty("InterationFlags")
    private String InterationFlags;
    /**
     * 是否允许忽略交互，布尔类型，默认true（非必录）
     */
    @JsonProperty("IgnoreInterationFlag")
    private String IgnoreInterationFlag;
    /**
     * 表单数据包，JSON类型（必录）
     */
    @JsonProperty("Model")
    private Object Model;

    public static  ErpTransferReportCommon getDefault(Object model){
        ErpTransferReportCommon erpTransferReportCommon = new ErpTransferReportCommon();
        erpTransferReportCommon.setNeedUpDateFields(new String[]{});
        erpTransferReportCommon.setNeedReturnFields(new String[]{});
        erpTransferReportCommon.setIsDeleteEntry("true");
        erpTransferReportCommon.setSubSystemId("");
        erpTransferReportCommon.setIsVerifyBaseDataField("false");
        erpTransferReportCommon.setIsEntryBatchFill("true");
        erpTransferReportCommon.setValidateFlag("true");
        erpTransferReportCommon.setNumberSearch("true");
        erpTransferReportCommon.setIsAutoAdjustField("true");
        erpTransferReportCommon.setInterationFlags("");
        erpTransferReportCommon.setIgnoreInterationFlag("");
        erpTransferReportCommon.setModel(model);
        return erpTransferReportCommon;
    }

}
