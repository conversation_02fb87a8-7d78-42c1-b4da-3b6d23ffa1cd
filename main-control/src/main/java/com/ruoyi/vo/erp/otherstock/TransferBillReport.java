package com.ruoyi.vo.erp.otherstock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import com.ruoyi.vo.erp.common.FNumberBig;
import lombok.Data;

import java.util.List;

/**
 * 直接调拨单上报实体
 * <AUTHOR>
 */
@Data
public class TransferBillReport {

    /**
     * 单据编号
     */
    @JsonProperty("FBillNo")
    private String fBillNo;

    /**
     * 单据类型
     */
    @JsonProperty("FBillTypeID")
    private FNumberBig fBillTypeID;

    /**
     * 业务类型
     */
    @JsonProperty("FBizType")
    private String fBizType;

    /**
     * 调拨方向
     */
    @JsonProperty("FTransferDirect")
    private String fTransferDirect;

    /**
     * 调拨业务类型
     */
    @JsonProperty("FTransferBizType")
    private String fTransferBizType;

    /**
     * 结算组织
     */
    @JsonProperty("FSettleOrgId")
    private FNumber fSettleOrgId;

    /**
     * 销售组织
     */
    @JsonProperty("FSaleOrgId")
    private FNumber fSaleOrgId;

    /**
     * 调出库存组织
     */
    @JsonProperty("FStockOutOrgId")
    private FNumber fStockOutOrgId;

    /**
     * 调入库存组织
     */
    @JsonProperty("FStockOrgId")
    private FNumber fStockOrgId;

    /**
     * 调出货主类型(表头)
     */
    @JsonProperty("FOwnerTypeOutIdHead")
    private String fOwnerTypeOutIdHead;

    /**
     * 调出货主(表头)
     */
    @JsonProperty("FOwnerOutIdHead")
    private FNumber fOwnerOutIdHead;

    /**
     * 调入货主类型(表头)
     */
    @JsonProperty("FOwnerTypeIdHead")
    private String fOwnerTypeIdHead;

    /**
     * 调入货主(表头)
     */
    @JsonProperty("FOwnerIdHead")
    private FNumber fOwnerIdHead;

    /**
     * 是否含税
     */
    @JsonProperty("FIsIncludedTax")
    private Boolean fIsIncludedTax;

    /**
     * 价格不含税
     */
    @JsonProperty("FIsPriceExcludeTax")
    private Boolean fIsPriceExcludeTax;

    /**
     * 汇率类型
     */
    @JsonProperty("FExchangeTypeId")
    private FNumberBig fExchangeTypeId;

    /**
     * 结算币别
     */
    @JsonProperty("FSETTLECURRID")
    private FNumberBig fSettleCurrId;

    /**
     * 汇率
     */
    @JsonProperty("FExchangeRate")
    private Double fExchangeRate;

    /**
     * 本位币
     */
    @JsonProperty("FBaseCurrId")
    private FNumber fBaseCurrId;

    /**
     * 日期
     */
    @JsonProperty("FDate")
    private String fDate;

    /**
     * 供应商
     */
    @JsonProperty("FSUPPLIERID")
    private FNumberBig fSupplierId;

    /**
     * 客户
     */
    @JsonProperty("FCustID")
    private FNumberBig fCustId;

    /**
     * 明细信息
     */
    @JsonProperty("FBillEntry")
    private List<TransferBillReportEntry> fBillEntry;
}