package com.ruoyi.vo.erp.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @author:lhb
 * @create: 2023-09-23 09:29
 * @Description: erp响应结果
 */
@Data
public class ErpResponseResult {

    @JsonProperty("ResponseStatus")
    @SerializedName("ResponseStatus")
    private ErpResponseStatus ResponseStatus;

    @JsonProperty("Id")
    @SerializedName("Id")
    private String Id;

    @JsonProperty("Number")
    @SerializedName("Number")
    private String Number;

    @JsonProperty("NeedReturnData")
    @SerializedName("NeedReturnData")
    private List<ErpResponseNeedReturnData> NeedReturnData;
}
