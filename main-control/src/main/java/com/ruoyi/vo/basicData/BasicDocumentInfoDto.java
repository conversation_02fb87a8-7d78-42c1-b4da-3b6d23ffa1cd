package com.ruoyi.vo.basicData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class BasicDocumentInfoDto {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 单据编号
     */
    private String transactionCode;

    /**
     * 单据分类 0入库 1:出库
     */
    private Integer transactionType;
    /**
     * 单据类型:1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库
     */
    private Integer businessType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receTime;

    /**
     * 供销编码
     */
    private String supplySalesCode;

    /**
     * 是否锁定0未锁定，1已锁定
     */
    private Integer isLock;

    /**
     * 物料明细列表
     */
    private List<DocumentDetailDto> details;
}