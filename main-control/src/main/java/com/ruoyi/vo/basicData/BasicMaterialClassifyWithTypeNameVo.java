package com.ruoyi.vo.basicData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @CreateDate: 2025/07/11
 * @Description: 物料分类信息（包含类型中文名称）
 */
@Data
public class BasicMaterialClassifyWithTypeNameVo {
    
    private String id;
    
    /**
     * 类型值
     */
    private String type;
    
    /**
     * 类型中文名称（从映射表获取）
     */
    private String typeName;
    
    /**
     * 分类编码
     */
    private String classifyCode;
    
    /**
     * 分类名称
     */
    private String classifyName;
    
    /**
     * 父id
     */
    private String parentId;
    
    /**
     * 分类描述
     */
    private String classifyDes;
    
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
