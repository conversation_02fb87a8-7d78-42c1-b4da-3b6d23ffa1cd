package com.ruoyi.vo.basicData;

import lombok.Data;

import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/11 10:03
 * @Description: 物料BOM新增清单
 */
@Data
public class BomInfoAddDto {
    private String id;
    /**
     * 序号（BOM序号）
     */
    private String treeNum;
    /**
     * 父节点id
     */
    private String parentId;
    /**
     * 层级
     */
    private int level;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 可替代物料，多个以逗号分隔；
     */
    private String replaceCode;
    /**
     * 消耗量
     */
    private float materialNum;
    /**
     * 备注
     */
    private String remark;

    private List<BomInfoAddDto> children;
}
