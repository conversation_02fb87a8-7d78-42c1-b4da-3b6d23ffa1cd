package com.ruoyi.vo.basicData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: psy
 * @CreateDate: 2025/06/11 10:13
 * @Description: 物料BOM数据，WEB展示
 */
@Data
public class BomInfoDto {
    private String id;
    private String versionId;
    private String companyId;
    /**
     * 所属物料
     */
    private String belongMaterialCode;
    /**
     * 序号（BOM序号）
     */
    private String treeNum;
    /**
     * 父节点id
     */
    private String parentId;
    /**
     * 层级
     */
    private int level;
    /**
     * 物料（原材料/半成品/材料）编码
     */
    private String materialCode;
    private String materialName;
    private String materialImg;
    /**
     * 分类名称
     */
    private String classifyName;
    /**
     * 物料类型 0 原材料，1半成品
     */
    private Integer materialSort;
    /**
     * 规格型号
     */
    private String specifications;
    /**
     * 可替代物料，多个以逗号分隔；
     */
    private String replaceCode;
    /**
     * 消耗量
     */
    private float materialOldNum;
    private float materialNum;
    /**
     * 备注
     */
    private String remark;

    private String createName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
