package com.ruoyi.vo.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class RecordAllotDetailVo {
    private String id;

    /**
     * 物料名称（入）
     */
    private String destMaterialName;

    /**
     * 规格型号（入）
     */
    private String destMaterialSpec;

    /**
     * 仓库名称（入）
     */
    private String destWarehouse;

    /**
     * 批次（入）
     */
    private String destBatch;

    /**
     * 容器编码（入）
     */
    private String destContainer;

    /**
     * 目的物料编码
     */
    private String destMaterialCode;

    /**
     * 目的物料图片
     */
    private String destMaterialImg;

    /**
     * 生产日期（入）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date destProduceDate;

    /**
     * 上位单号（入）
     */
    private String destUpperIndex;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 物料名称（出）
     */
    private String orignMaterialName;

    /**
     * 规格型号（出）
     */
    private String orignMaterialSpec;

    /**
     * 仓库名称（出）
     */
    private String orignWarehouse;

    /**
     * 批次（出)
     */
    private String orignBatch;

    /**
     * 容器编码（出）
     */
    private String orignContainer;

    /**
     * 来源物料编码
     */
    private String orignMaterialCode;

    /**
     * 来源物料图片
     */
    private String orignMaterialImg;

    /**
     * 生产日期（出）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date orignProduceDate;

    /**
     * 上位单号（出）
     */
    private String orignUpperIndex;

    /**
     * 备注
     */
    private String remark;

}

