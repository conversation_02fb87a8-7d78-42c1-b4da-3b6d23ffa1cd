package com.ruoyi.vo.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 出入库物料明细
 * <AUTHOR>
 * @date 2024年09月01日 下午4:29
 */
@Data
public class MaterialDetailDto {
    /**
     * 单据编码
     */
    private String boundIndex;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 批次
     */
    private String batch;

    /**
     * 备注
     */
    private String remark;

    /**
     * 采购单编号
     */
    private String upperIndex;

    /**
     * 批次id
     */
    private String inventoryId;


}
