package com.ruoyi.vo.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年09月09日 下午6:08
 */
@Data
public class RecordInoutDetailVo {

    private String id;

    /**
     * 单据编码
     */
    private String boundIndex;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料图片
     */
    private String materialImg;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 规格型号
     */
    private String specifications;

    /**
     * 采购单编号
     */
    private String upperIndex;

    /**
     * 批次
     */
    private String batch;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单据类型，0：采购入库单，1：采购退货单，2：生产领料出库单，3：生产退料入库单，4：销售出库单，5：销售退货单，6：仓库用料单
     */
    private Integer boundType;

    /**
     * 0合格，1不合格
     */
    private Integer isCompliant;

    /**
     * 可用数量
     */
    private Integer availNum;

    /**
     * 库位名称
     */
    private String positionName;

    /**
     * 层级名称
     */
    private String levelName;

    /**
     * 货架名称
     */
    private String shelfName;

}
