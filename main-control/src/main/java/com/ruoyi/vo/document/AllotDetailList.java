package com.ruoyi.vo.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年09月20日 上午11:31
 */
@Data
public class AllotDetailList {
    /**
     * 批次（入）
     */
    private String destBatch;

    /**
     * 容器编码（入）
     */
    private String destContainer;

    /**
     * 目的物料编码
     */
    private String destMaterialCode;

    /**
     * 生产日期（入）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date destProduceDate;

    /**
     * 上位单号（入）
     */
    private String destUpperIndex;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 批次（出)
     */
    private String orignBatch;

    /**
     * 容器编码（出）
     */
    private String orignContainer;

    /**
     * 来源物料编码
     */
    private String orignMaterialCode;

    /**
     * 生产日期（出）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date orignProduceDate;

    /**
     * 上位单号（出）
     */
    private String orignUpperIndex;

    /**
     * 备注
     */
    private String remark;


    /**
     * 批次id
     */
    private String inventoryId;

}

