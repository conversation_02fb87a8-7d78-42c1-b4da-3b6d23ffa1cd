package com.ruoyi.vo.warehouse;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * 库位复制实体
 */
@Data
public class BasicWarehouseLocationDto {
    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 父部节点id
     */
    private String parentId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 货架类型
     * 0型材货架，1板材货架，2物料货架
     */
    private Integer locationType;

    /**
     * 状态
     */
    private Integer state;

    @TableField("`top`")
    private float top;
    @TableField("`left`")
    private float left;
    private float width;
    private float height;

    /**
     * 备注
     */
    private List<BasicWarehouseLocationDto> children;
}
