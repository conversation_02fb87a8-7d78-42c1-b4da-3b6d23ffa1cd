package com.ruoyi.vo.warehouse;
import lombok.Data;

/**
 * 物料BOM基本信息
 */
@Data
public class BasicMaterialBomVo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 物料（原材料/半成品/材料）编码
     */
    private String materialCode;

    /**
     * 物料所需数量
     */
    private Integer materialNum;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料图片
     */
    private String materialImg;

    /**
     * 分类编码
     */
    private String materialType;

    /**
     * 物料分类0 原材料，1半成品，2 成品，3其它
     */
    private Integer materialSort;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 父节点id
     */
    private String parentId;

    /**
     * 备注
     */
    private String remark;

}
