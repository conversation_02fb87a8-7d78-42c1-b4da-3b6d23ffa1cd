package com.ruoyi.vo.warehouse;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料容器信息
 */
@Data
public class MaterialContainerInfoDto {
    /**
     * 批次id
     */
    private String inventoryId;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料图片
     */
    private String materialImg;

    /**
     * 分类编码
     */
    private String classifyCode;

    /**
     * 物料分类0 原材料，1半成品，2 成品，3其它
     */
    private Integer materialSort;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 库位
     */
    private String positionName;

    /**
     * 层级
     */
    private String levelName;

    /**
     * 货架
     */
    private String shelfName;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 冻结数量
     */
    private Integer freezeNum;

    /**
     * 可用数量
     */
    private Integer availNum;

    /**
     * 入库日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inDate;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 批次
     */
    private String batch;

    /**
     * 上位编号
     */
    private String upperIndex;

    /**
     * 是否隔离 0正常，1隔离
     */
    private Integer isIsolation;

    /**
     * 质检次数
     */
    private Integer checkNum;

    /**
     * 备注
     */
    private String remark;


}
