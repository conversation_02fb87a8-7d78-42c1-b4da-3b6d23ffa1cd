package com.ruoyi.vo.warehouse;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 物料预警信息
 */
@Data
public class BasicMaterialAlertInfo {

    /**
     * 物料编码
     */
    @Excel(name = "物料编码", sort = 1)
    private String materialCode;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称", sort = 2)
    private String materialName;

    /**
     * 分类编码
     */
    @Excel(name = "分类编码", sort = 3)
    private String classifyCode;


    /**
     * 分类名称
     */
    @Excel(name = "分类名称", sort = 4)
    private String classifyName;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    private String specifications;

    /**
     * 物料单位
     */
    @Excel(name = "物料单位")
    private String produceUnit;

    /**
     * 最低库存
     */
    @Excel(name = "最低库存")
    private Integer minInventory;

    /**
     * 最高库存
     */
    @Excel(name = "最高库存")
    private Integer maxInventory;

    /**
     * 物料数量
     */
    @Excel(name = "物料数量")
    private Integer materialNum;

    /**
     * 有效期，以月为单位
     */
    @Excel(name = "有效期(月)")
    private Integer expiryDate;

    /**
     *预警类型
     * 0:缺储预警,1:超储预警,2:临期预警
     */
    @Excel(name = "预警类型", readConverterExp = "0=缺储预警,1=超储预警,2=临期预警", sort = 4)
    private Integer alertType;

    /**
     * 物料图片
     */
    private String materialImg;
}
