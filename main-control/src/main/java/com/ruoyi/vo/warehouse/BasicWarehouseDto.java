package com.ruoyi.vo.warehouse;
import lombok.Data;

/**
 * 仓库基本信息
 */
@Data
public class BasicWarehouseDto {

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库地址
     */
    private String warehouseAddress;

    /**
     * 仓库责任人
     */
    private String manager;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 仓库类型：0平面库，1货架
     */
    private int warehouseType;

    /**
     * 仓库状态，0：启用，1：停用
     */
    private int state;

    /**
     * 备注
     */
    private String remark;

}
