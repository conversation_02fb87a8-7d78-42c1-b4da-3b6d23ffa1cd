package com.ruoyi.vo.warehouse;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料过期预警信息
 */
@Data
public class MaterialAlertInfo {

    /**
     * 物料编码
     */
    @Excel(name = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称")
    private String materialName;

    /**
     * 过期天数
     */
    @Excel(name = "过期天数")
    private Integer daysOverdue;

    /**
     * 分类编码
     */
    @Excel(name = "分类编码")
    private String classifyCode;

    /**
     * 分类名称
     */
    @Excel(name = "分类名称")
    private String classifyName;

    /**
     * 物料类型
     */
    @Excel(name = "物料类型", readConverterExp = "0=原材料,1=半成品,2=成品,3=其它")
    private Integer materialSort;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    private String specifications;

    /**
     * 物料单位
     */
    @Excel(name = "物料单位")
    private String produceUnit;

    /**
     * 物料数量
     */
    @Excel(name = "物料数量")
    private Integer materialNum;

    /**
     * 有效期，以月为单位
     */
    @Excel(name = "有效期(月)")
    private Integer expiryDate;

    /**
     * 容器编码
     */
    @Excel(name = "容器编码")
    private String containerCode;

    /**
     * 入库日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "入库日期", dateFormat = "yyyy-MM-dd")
    private Date inDate;

    /**
     * 入库编码
     */
    @Excel(name = "入库编码")
    private String upperIndex;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "生产日期", dateFormat = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 批次
     */
    @Excel(name = "批次")
    private String batch;

    /**
     * 物料图片
     */
    private String materialImg;

    /**
     * 仓库名称
     */
    @Excel(name = "物料位置", sort = 4)
    private String materialLocation;


}
