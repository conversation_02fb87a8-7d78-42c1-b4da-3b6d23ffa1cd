package com.ruoyi.vo.bill;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 简单生产领料单明细VO
 * <AUTHOR>
 */
@Data
public class SimpleProductionPickingMaterialDetailVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 关联主表ID
     */
    private String pickingId;

    /**
     * 分录ID
     */
    private String entryId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 库存状态ID
     */
    private String stockStatusId;



    /**
     * 申请数量
     */
    private BigDecimal appQty;

    /**
     * 实发数量
     */
    private BigDecimal actualQty;

    /**
     * 备注
     */
    private String entryMemo;

    /**
     * 单位ID
     */
    private String unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 主库存单位ID
     */
    private String stockUnitId;


}
