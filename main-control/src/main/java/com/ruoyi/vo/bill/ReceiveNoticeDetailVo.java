package com.ruoyi.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收料通知单明细VO
 * <AUTHOR>
 */
@Data
public class ReceiveNoticeDetailVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 关联主表ID
     */
    private String noticeId;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String materialModel;

    /**
     * 收料单位ID
     */
    private String unitId;

    /**
     * 收料单位名称
     */
    private String unitName;

    /**
     * 实到数量
     */
    private BigDecimal actReceiveQty;

    /**
     * 预计到货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date preDeliveryDate;

    /**
     * 供应商交货数量
     */
    private BigDecimal supDelQty;

    /**
     * 计价单位ID
     */
    private String priceUnitId;

    /**
     * 计价单位名称
     */
    private String priceUnitName;

    /**
     * 计价数量
     */
    private BigDecimal priceUnitQty;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 仓位ID
     */
    private String stockLocId;

    /**
     * 仓位名称
     */
    private String stockLocName;

    /**
     * 库存单位ID
     */
    private String stockUnitId;

    /**
     * 库存单位名称
     */
    private String stockUnitName;

    /**
     * 库存单位数量
     */
    private BigDecimal stockQty;

    /**
     * 订单数量
     */
    private BigDecimal poQty;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
