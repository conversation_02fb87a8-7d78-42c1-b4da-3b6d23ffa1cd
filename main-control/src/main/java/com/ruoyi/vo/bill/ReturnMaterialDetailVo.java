package com.ruoyi.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退料申请单明细VO
 * <AUTHOR>
 */
@Data
public class ReturnMaterialDetailVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 关联主表ID
     */
    private String appId;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String uom;

    /**
     * 申请退料数量
     */
    private BigDecimal mrAppQty;

    /**
     * 备注
     */
    private String noteM;

    /**
     * 库存单位ID
     */
    private String unitId;

    /**
     * 库存单位名称
     */
    private String unitName;

    /**
     * 计价单位ID
     */
    private String priceUnitIdF;

    /**
     * 计价单位名称
     */
    private String priceUnitNameF;

    /**
     * 计价数量
     */
    private BigDecimal priceQtyF;

    /**
     * 补料数量
     */
    private BigDecimal replenishQty;

    /**
     * 采购单位ID
     */
    private String purUnitId;

    /**
     * 采购单位名称
     */
    private String purUnitName;

    /**
     * 采购数量
     */
    private BigDecimal purQty;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 仓位ID
     */
    private String stockLocId;

    /**
     * 仓位名称
     */
    private String stockLocName;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
