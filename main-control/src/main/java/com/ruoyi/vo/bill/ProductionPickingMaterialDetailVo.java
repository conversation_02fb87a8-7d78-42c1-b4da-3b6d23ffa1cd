package com.ruoyi.vo.bill;

import com.ruoyi.domain.bill.ProductionPickingMaterialDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 生产领料单明细视图对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductionPickingMaterialDetailVo extends ProductionPickingMaterialDetail {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 基本单位名称
     */
    private String baseUnitName;

    /**
     * 主库存单位名称
     */
    private String stockUnitName;

    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 仓位名称
     */
    private String stockLocName;

    /**
     * 库存状态名称
     */
    private String stockStatusName;

    /**
     * 车间名称
     */
    private String workshopName;
}
