package com.ruoyi.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 收料通知单VO
 * <AUTHOR>
 */
@Data
public class ReceiveNoticeVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 收料日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveDate;

    /**
     * 单据类型ID
     */
    private String billTypeId;

    /**
     * 单据类型名称
     */
    private String billTypeName;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 收料部门ID
     */
    private String receiveDeptId;

    /**
     * 收料部门名称
     */
    private String receiveDeptName;

    /**
     * 采购员ID
     */
    private String purchaserId;

    /**
     * 采购员名称
     */
    private String purchaserName;

    /**
     * 备注
     */
    private String note;

    /**
     * 验收方式
     */
    private String accType;

    /**
     * 验收方式名称
     */
    private String accTypeName;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
