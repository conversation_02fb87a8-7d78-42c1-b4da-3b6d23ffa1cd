package com.ruoyi.vo.bill;

import com.ruoyi.domain.bill.ProductionInStockDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 生产入库单明细视图对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductionInStockDetailVo extends ProductionInStockDetail {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 基本单位名称
     */
    private String baseUnitName;

    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 库存状态名称
     */
    private String stockStatusName;


}
