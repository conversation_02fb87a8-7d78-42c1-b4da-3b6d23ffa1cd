package com.ruoyi.vo.bill;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 简单生产入库单明细VO
 * <AUTHOR>
 */
@Data
public class SimpleProductionInStockDetailVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 关联主表ID
     */
    private String inStockId;

    /**
     * 分录ID
     */
    private String entryId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 入库类型
     */
    private String inStockType;

    /**
     * 单位ID
     */
    private String unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 应收数量
     */
    private BigDecimal mustQty;

    /**
     * 基本单位应收数量
     */
    private BigDecimal baseMustQty;

    /**
     * 实收数量
     */
    private BigDecimal realQty;

    /**
     * 基本单位实收数量
     */
    private BigDecimal baseRealQty;

    /**
     * 货主类型ID
     */
    private String ownerTypeId;

    /**
     * 货主ID
     */
    private String ownerId;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 生产车间ID
     */
    private String workshopId;

    /**
     * 备注
     */
    private String memo;

    /**
     * 库存状态ID
     */
    private String stockStatusId;

    /**
     * 保管者类型ID
     */
    private String keeperTypeId;

    /**
     * 保管者ID
     */
    private String keeperId;
}
