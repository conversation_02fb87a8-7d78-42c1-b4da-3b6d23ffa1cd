package com.ruoyi.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 退货通知单VO
 * <AUTHOR>
 */
@Data
public class ReturnNoticeVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;



    /**
     * 退货客户ID
     */
    private String retCustId;

    /**
     * 退货客户名称
     */
    private String retCustName;

    /**
     * 单据类型ID
     */
    private String billTypeId;

    /**
     * 单据类型名称
     */
    private String billTypeName;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 退货原因名称
     */
    private String returnReasonName;

    /**
     * 收货方地址
     */
    private String receiveAddress;

    /**
     * 交货地点ID
     */
    private String headLocId;

    /**
     * 交货地点名称
     */
    private String headLocName;

    /**
     * 备注
     */
    private String description;

    /**
     * 收货人姓名
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkPhone;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
