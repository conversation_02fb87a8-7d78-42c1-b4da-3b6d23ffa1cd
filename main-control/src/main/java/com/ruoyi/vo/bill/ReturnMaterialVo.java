package com.ruoyi.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 退料申请单VO
 * <AUTHOR>
 */
@Data
public class ReturnMaterialVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 单据类型ID
     */
    private String billTypeId;

    /**
     * 单据类型名称
     */
    private String billTypeName;

    /**
     * 申请日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date appDate;

    /**
     * 退料类型
     */
    private String rmType;

    /**
     * 退料类型名称
     */
    private String rmTypeName;

    /**
     * 退料地点
     */
    private String rmLoc;

    /**
     * 退料地点名称
     */
    private String rmLocName;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 退料方式
     */
    private String rmMode;

    /**
     * 退料方式名称
     */
    private String rmModeName;

    /**
     * 补料方式
     */
    private String replenishMode;

    /**
     * 补料方式名称
     */
    private String replenishModeName;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 退料原因
     */
    private String rmReason;

    /**
     * 申请人ID
     */
    private String applicantId;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 申请部门ID
     */
    private String appDeptId;

    /**
     * 申请部门名称
     */
    private String appDeptName;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
