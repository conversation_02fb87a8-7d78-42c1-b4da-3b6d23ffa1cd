package com.ruoyi.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退货通知单明细VO
 * <AUTHOR>
 */
@Data
public class ReturnNoticeDetailVo {

    /**
     * 实体主键
     */
    private String id;

    /**
     * 关联主表ID
     */
    private String noticeId;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String materialModel;

    /**
     * 销售数量
     */
    private BigDecimal qty;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 基本单位名称
     */
    private String baseUnitName;

    /**
     * 备注
     */
    private String entryDescription;

    /**
     * 订单单号
     */
    private String orderNo;

    /**
     * 批号
     */
    private String lot;

    /**
     * 退货类型
     */
    private String rmType;

    /**
     * 退货类型名称
     */
    private String rmTypeName;

    /**
     * 退货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    /**
     * 销售单位ID
     */
    private String unitId;

    /**
     * 销售单位名称
     */
    private String unitName;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
