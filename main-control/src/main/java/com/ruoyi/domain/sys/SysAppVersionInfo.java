package com.ruoyi.domain.sys;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [系统APP版本信息]
 * @date 2024/11/25 13:55
 */
@Data
@TableName("sys_app_version")
public class SysAppVersionInfo {
    private String id;
    private String app_name;
    private String app_edition;
    private String app_edition_num;
    private String upt_info;
    private String apk_path;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date upt_time;
    private String apk_name;
    private String file_id;
}
