package com.ruoyi.domain.sys;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author:psy
 * @create: 2024-05-26 09:13
 * @Description: 接口交互日志记录
 */
@Data
@TableName("sys_inter_log")
public class SysInterLog {

    private String id;
    private String taskNo;

    /**
     * 请求描述
     */
    private String reqDesc;
    /**
     * 请求接口内容
     */
    private String request;
    /**
     * 请求时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date reqTime;
    /**
     * 响应描述
     */
    private String resDesc;
    /**
     * 响应接口内容
     */
    private String response;
    /**
     * 响应时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date resTime;
    /**
     * 记录时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date recordTime;
    /**
     * 信号类型
     */
    private Integer signalType;
    /**
     * 产线模块
     */
    private String productId;
    /**
     * 0自动触发，1界面点击
     */
    private Integer isWeb;
}
