package com.ruoyi.domain.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 系统配置参数
 * 通过指定参数类型，可获取该分类下的所有数据用作选择
 */
@Data
@TableName("common_classify_config")
public class ClassifyConfig {
    private String id;
    /**
     * 参数类型，0：:计量单位
     */
    private int classifyType;
    private String classifyVal;
    /*
     * 是否有效，0：无效，1：有效
     */
    private int isActive;
    private String remark;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
