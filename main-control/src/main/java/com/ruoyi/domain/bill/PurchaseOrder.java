package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Objects;

/**
 * 采购订单
 */
@Data
@ToString
@TableName("purchase_order")
public class PurchaseOrder {
    /** 实体主键 */
    @TableId(type = IdType.INPUT)
    private String id;
    /** 单据编号 */
    private String billNo;
    /** 采购日期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date purchaseDate;
    /** 供应商ID */
    private String supplierId;
    /** 采购部门ID */
    private String purchaseDeptId;
    /** 采购员ID */
    private String purchaserId;
    /** 业务类型 */
    private String businessType;
    /** 供货方ID */
    private String providerId;
    /** 供货方联系人ID */
    private String providerContactId;
    /** 结算方ID */
    private String settleId;
    /** 收款方ID */
    private String chargeId;
    /** 创建人 */
    private String createName;
    /** 更新人 */
    private String updateName;
    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /** 更新时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PurchaseOrder that = (PurchaseOrder) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(billNo, that.billNo) &&
                Objects.equals(purchaseDate, that.purchaseDate) &&
                Objects.equals(supplierId, that.supplierId) &&
                Objects.equals(purchaseDeptId, that.purchaseDeptId) &&
                Objects.equals(purchaserId, that.purchaserId) &&
                Objects.equals(createName, that.createName) &&
                Objects.equals(businessType, that.businessType) &&
                Objects.equals(providerId, that.providerId) &&
                Objects.equals(providerContactId, that.providerContactId) &&
                Objects.equals(settleId, that.settleId) &&
                Objects.equals(chargeId, that.chargeId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, billNo, purchaseDate, supplierId, purchaseDeptId, purchaserId, createName, businessType, providerId, providerContactId, settleId, chargeId);
    }
}