package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 生产入库单明细表
 * <AUTHOR>
 */
@Data
@TableName("production_in_stock_detail")
@EqualsAndHashCode(callSuper = false)
public class ProductionInStockDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String inStockId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 入库类型
     */
    private String inStockType;

    /**
     * 单位ID
     */
    private String unitId;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 应收数量
     */
    private BigDecimal mustQty;

    /**
     * 基本单位应收数量
     */
    private BigDecimal baseMustQty;

    /**
     * 实收数量
     */
    private BigDecimal realQty;

    /**
     * 基本单位库存实收数量
     */
    private BigDecimal baseRealQty;

    /**
     * 货主类型ID
     */
    private String ownerTypeId;

    /**
     * 货主ID
     */
    private String ownerId;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 生产订单编号
     */
    private String moBillNo;

    /**
     * 生产订单内码
     */
    private String moId;

    /**
     * 生产订单分录内码
     */
    private String moEntryId;

    /**
     * 生产订单行号
     */
    private String moEntrySeq;

    /**
     * 备注
     */
    private String memo;

    /**
     * 库存状态ID
     */
    private String stockStatusId;

    /**
     * 保管者类型ID
     */
    private String keeperTypeId;

    /**
     * 保管者ID
     */
    private String keeperId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductionInStockDetail that = (ProductionInStockDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(inStockId, that.inStockId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(specification, that.specification) &&
                Objects.equals(productType, that.productType) &&
                Objects.equals(inStockType, that.inStockType) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(baseUnitId, that.baseUnitId) &&
                (mustQty != null && that.mustQty != null ? mustQty.compareTo(that.mustQty) == 0 : Objects.equals(mustQty, that.mustQty)) &&
                (baseMustQty != null && that.baseMustQty != null ? baseMustQty.compareTo(that.baseMustQty) == 0 : Objects.equals(baseMustQty, that.baseMustQty)) &&
                (realQty != null && that.realQty != null ? realQty.compareTo(that.realQty) == 0 : Objects.equals(realQty, that.realQty)) &&
                (baseRealQty != null && that.baseRealQty != null ? baseRealQty.compareTo(that.baseRealQty) == 0 : Objects.equals(baseRealQty, that.baseRealQty)) &&
                Objects.equals(ownerTypeId, that.ownerTypeId) &&
                Objects.equals(ownerId, that.ownerId) &&
                Objects.equals(stockId, that.stockId) &&
                Objects.equals(moBillNo, that.moBillNo) &&
                Objects.equals(moId, that.moId) &&
                Objects.equals(moEntryId, that.moEntryId) &&
                Objects.equals(moEntrySeq, that.moEntrySeq) &&
                Objects.equals(memo, that.memo) &&
                Objects.equals(stockStatusId, that.stockStatusId) &&
                Objects.equals(keeperTypeId, that.keeperTypeId) &&
                Objects.equals(keeperId, that.keeperId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, inStockId, materialId, materialName, specification, productType, inStockType,
                unitId, baseUnitId, mustQty, baseMustQty, realQty, baseRealQty, ownerTypeId, ownerId, stockId,
                moBillNo, moId, moEntryId, moEntrySeq, memo, stockStatusId, keeperTypeId, keeperId);
    }
}
