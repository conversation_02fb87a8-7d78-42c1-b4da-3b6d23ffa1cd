package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 收料通知单明细表
 * <AUTHOR>
 */
@Data
@TableName("receive_notice_detail")
@EqualsAndHashCode(callSuper = false)
public class ReceiveNoticeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String noticeId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String materialModel;

    /**
     * 收料单位ID
     */
    private String unitId;

    /**
     * 实到数量
     */
    private BigDecimal actReceiveQty;

    /**
     * 预计到货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date preDeliveryDate;

    /**
     * 供应商交货数量
     */
    private BigDecimal supDelQty;

    /**
     * 计价单位ID
     */
    private String priceUnitId;

    /**
     * 计价数量
     */
    private BigDecimal priceUnitQty;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 仓位ID
     */
    private String stockLocId;

    /**
     * 库存单位ID
     */
    private String stockUnitId;

    /**
     * 库存单位数量
     */
    private BigDecimal stockQty;

    /**
     * 订单数量
     */
    private BigDecimal poQty;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReceiveNoticeDetail that = (ReceiveNoticeDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(noticeId, that.noticeId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(materialModel, that.materialModel) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(actReceiveQty, that.actReceiveQty) &&
                Objects.equals(preDeliveryDate, that.preDeliveryDate) &&
                Objects.equals(supDelQty, that.supDelQty) &&
                Objects.equals(priceUnitId, that.priceUnitId) &&
                Objects.equals(priceUnitQty, that.priceUnitQty) &&
                Objects.equals(stockId, that.stockId) &&
                Objects.equals(stockLocId, that.stockLocId) &&
                Objects.equals(stockUnitId, that.stockUnitId) &&
                Objects.equals(stockQty, that.stockQty) &&
                Objects.equals(poQty, that.poQty);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, noticeId, materialId, materialName, materialModel, unitId, 
                actReceiveQty, preDeliveryDate, supDelQty, priceUnitId, priceUnitQty, 
                stockId, stockLocId, stockUnitId, stockQty, poQty);
    }
}
