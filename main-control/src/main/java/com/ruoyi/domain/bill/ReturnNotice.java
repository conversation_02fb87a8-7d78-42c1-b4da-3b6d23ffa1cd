package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 退货通知单主表
 * <AUTHOR>
 */
@Data
@TableName("return_notice")
@EqualsAndHashCode(callSuper = false)
public class ReturnNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /**
     * 销售组织ID
     */
    private String saleOrgId;

    /**
     * 库存组织ID
     */
    private String retOrgId;

    /**
     * 库存部门ID
     */
    private String retDeptId;

    /**
     * 库存组ID
     */
    private String stockerGroupId;

    /**
     * 仓管员ID
     */
    private String stockerId;

    /**
     * 退货客户ID
     */
    private String retCustId;

    /**
     * 单据类型ID
     */
    private String billTypeId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 收货方地址
     */
    private String receiveAddress;

    /**
     * 交货地点ID
     */
    private String headLocId;

    /**
     * 备注
     */
    private String description;

    /**
     * 收货人姓名
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkPhone;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReturnNotice that = (ReturnNotice) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(billNo, that.billNo) &&
                Objects.equals(date, that.date) &&
                Objects.equals(saleOrgId, that.saleOrgId) &&
                Objects.equals(retOrgId, that.retOrgId) &&
                Objects.equals(retDeptId, that.retDeptId) &&
                Objects.equals(stockerGroupId, that.stockerGroupId) &&
                Objects.equals(stockerId, that.stockerId) &&
                Objects.equals(retCustId, that.retCustId) &&
                Objects.equals(billTypeId, that.billTypeId) &&
                Objects.equals(businessType, that.businessType) &&
                Objects.equals(returnReason, that.returnReason) &&
                Objects.equals(receiveAddress, that.receiveAddress) &&
                Objects.equals(headLocId, that.headLocId) &&
                Objects.equals(description, that.description) &&
                Objects.equals(linkMan, that.linkMan) &&
                Objects.equals(linkPhone, that.linkPhone);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, billNo, date, saleOrgId, retOrgId, retDeptId, stockerGroupId, 
                stockerId, retCustId, billTypeId, businessType, returnReason, receiveAddress, 
                headLocId, description, linkMan, linkPhone);
    }
}
