package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 生产领料单明细表
 * <AUTHOR>
 */
@Data
@TableName("production_picking_material_detail")
@EqualsAndHashCode(callSuper = false)
public class ProductionPickingMaterialDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String pickingId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 仓位ID
     */
    private String stockLocId;

    /**
     * 库存状态ID
     */
    private String stockStatusId;

    /**
     * 生产订单编号
     */
    private String moBillNo;

    /**
     * 生产订单分录内码
     */
    private String moEntryId;

    /**
     * 用料清单分录内码
     */
    private String ppBomEntryId;

    /**
     * 货主类型ID
     */
    private String ownerTypeId;

    /**
     * 申请数量
     */
    private BigDecimal appQty;

    /**
     * 实发数量
     */
    private BigDecimal actualQty;

    /**
     * 备注
     */
    private String entryMemo;

    /**
     * 生产订单内码
     */
    private String moId;

    /**
     * 生产订单行号
     */
    private String moEntrySeq;

    /**
     * 单位ID
     */
    private String unitId;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 主库存单位ID
     */
    private String stockUnitId;

    /**
     * 保管者类型ID
     */
    private String keeperTypeId;

    /**
     * 保管者ID
     */
    private String keeperId;

    /**
     * 货主ID
     */
    private String ownerId;

    /**
     * 产品货主类型ID
     */
    private String parentOwnerTypeId;

    /**
     * 产品货主ID
     */
    private String parentOwnerId;

    /**
     * 车间ID
     */
    private String entryWorkshopId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductionPickingMaterialDetail that = (ProductionPickingMaterialDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(pickingId, that.pickingId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(specification, that.specification) &&
                Objects.equals(stockId, that.stockId) &&
                Objects.equals(stockLocId, that.stockLocId) &&
                Objects.equals(stockStatusId, that.stockStatusId) &&
                Objects.equals(moBillNo, that.moBillNo) &&
                Objects.equals(moEntryId, that.moEntryId) &&
                Objects.equals(ppBomEntryId, that.ppBomEntryId) &&
                Objects.equals(ownerTypeId, that.ownerTypeId) &&
                (appQty != null && that.appQty != null ? appQty.compareTo(that.appQty) == 0 : Objects.equals(appQty, that.appQty)) &&
                (actualQty != null && that.actualQty != null ? actualQty.compareTo(that.actualQty) == 0 : Objects.equals(actualQty, that.actualQty)) &&
                Objects.equals(entryMemo, that.entryMemo) &&
                Objects.equals(moId, that.moId) &&
                Objects.equals(moEntrySeq, that.moEntrySeq) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(baseUnitId, that.baseUnitId) &&
                Objects.equals(stockUnitId, that.stockUnitId) &&
                Objects.equals(keeperTypeId, that.keeperTypeId) &&
                Objects.equals(keeperId, that.keeperId) &&
                Objects.equals(ownerId, that.ownerId) &&
                Objects.equals(parentOwnerTypeId, that.parentOwnerTypeId) &&
                Objects.equals(parentOwnerId, that.parentOwnerId) &&
                Objects.equals(entryWorkshopId, that.entryWorkshopId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, pickingId, materialId, materialName, specification, stockId, stockLocId, stockStatusId,
                moBillNo, moEntryId, ppBomEntryId, ownerTypeId, appQty, actualQty, entryMemo, moId, moEntrySeq,
                unitId, baseUnitId, stockUnitId, keeperTypeId, keeperId, ownerId, parentOwnerTypeId, parentOwnerId, entryWorkshopId);
    }
}
