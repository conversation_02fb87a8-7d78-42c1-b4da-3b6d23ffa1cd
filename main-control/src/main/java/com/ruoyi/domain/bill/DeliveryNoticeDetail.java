package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 发货通知单明细表
 * <AUTHOR>
 */
@Data
@TableName("delivery_notice_detail")
@EqualsAndHashCode(callSuper = false)
public class DeliveryNoticeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String noticeId;

    /**
     * 物料ID
     */
    private String materialId;
    
    /**
     * 父项物料ID
     */
    private String parentMaterialId;

    /**
     * 销售单位ID
     */
    private String unitId;

    /**
     * 销售数量
     */
    private BigDecimal qty;

    /**
     * 交货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;
    
    /**
     * 物料类别
     */
    private String materialType;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DeliveryNoticeDetail that = (DeliveryNoticeDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(noticeId, that.noticeId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(parentMaterialId, that.parentMaterialId) &&
                Objects.equals(unitId, that.unitId) &&
                (qty != null && that.qty != null ? qty.compareTo(that.qty) == 0 : Objects.equals(qty, that.qty)) &&
                Objects.equals(deliveryDate, that.deliveryDate) &&
                Objects.equals(materialType, that.materialType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, noticeId, materialId, parentMaterialId, unitId, qty, deliveryDate, materialType);
    }
}