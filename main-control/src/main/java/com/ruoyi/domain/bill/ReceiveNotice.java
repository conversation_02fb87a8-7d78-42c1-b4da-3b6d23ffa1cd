package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 收料通知单主表
 * <AUTHOR>
 */
@Data
@TableName("receive_notice")
@EqualsAndHashCode(callSuper = false)
public class ReceiveNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 收料组织ID
     */
    private String stockOrgId;

    /**
     * 收料日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveDate;

    /**
     * 单据类型ID
     */
    private String billTypeId;

    /**
     * 货主类型ID
     */
    private String ownerTypeIdHead;

    /**
     * 货主ID
     */
    private String ownerIdHead;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 收料部门ID
     */
    private String receiveDeptId;

    /**
     * 采购组织ID
     */
    private String purOrgId;

    /**
     * 采购部门ID
     */
    private String purDeptId;

    /**
     * 采购员ID
     */
    private String purchaserId;

    /**
     * 备注
     */
    private String note;

    /**
     * 验收方式
     */
    private String accType;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReceiveNotice that = (ReceiveNotice) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(billNo, that.billNo) &&
                Objects.equals(stockOrgId, that.stockOrgId) &&
                Objects.equals(receiveDate, that.receiveDate) &&
                Objects.equals(billTypeId, that.billTypeId) &&
                Objects.equals(ownerTypeIdHead, that.ownerTypeIdHead) &&
                Objects.equals(ownerIdHead, that.ownerIdHead) &&
                Objects.equals(supplierId, that.supplierId) &&
                Objects.equals(receiveDeptId, that.receiveDeptId) &&
                Objects.equals(purOrgId, that.purOrgId) &&
                Objects.equals(purDeptId, that.purDeptId) &&
                Objects.equals(purchaserId, that.purchaserId) &&
                Objects.equals(note, that.note) &&
                Objects.equals(accType, that.accType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, billNo, stockOrgId, receiveDate, billTypeId, ownerTypeIdHead, 
                ownerIdHead, supplierId, receiveDeptId, purOrgId, purDeptId, purchaserId, note, accType);
    }
}
