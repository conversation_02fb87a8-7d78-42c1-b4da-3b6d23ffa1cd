package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 简单生产领料单明细表
 * <AUTHOR>
 */
@Data
@TableName("simple_production_picking_material_detail")
@EqualsAndHashCode(callSuper = false)
public class SimpleProductionPickingMaterialDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String pickingId;

    /**
     * 分录ID
     */
    private String entryId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 库存状态ID
     */
    private String stockStatusId;

    /**
     * 货主类型ID
     */
    private String ownerTypeId;

    /**
     * 申请数量
     */
    private BigDecimal appQty;

    /**
     * 实发数量
     */
    private BigDecimal actualQty;

    /**
     * 备注
     */
    private String entryMemo;

    /**
     * 单位ID
     */
    private String unitId;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 主库存单位ID
     */
    private String stockUnitId;

    /**
     * 保管者类型ID
     */
    private String keeperTypeId;

    /**
     * 保管者ID
     */
    private String keeperId;

    /**
     * 货主ID
     */
    private String ownerId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SimpleProductionPickingMaterialDetail that = (SimpleProductionPickingMaterialDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(pickingId, that.pickingId) &&
                Objects.equals(entryId, that.entryId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(specification, that.specification) &&
                Objects.equals(stockId, that.stockId) &&
                Objects.equals(stockStatusId, that.stockStatusId) &&
                Objects.equals(ownerTypeId, that.ownerTypeId) &&
                (appQty == that.appQty || (appQty != null && appQty.compareTo(that.appQty) == 0)) &&
                (actualQty == that.actualQty || (actualQty != null && actualQty.compareTo(that.actualQty) == 0)) &&
                Objects.equals(entryMemo, that.entryMemo) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(baseUnitId, that.baseUnitId) &&
                Objects.equals(stockUnitId, that.stockUnitId) &&
                Objects.equals(keeperTypeId, that.keeperTypeId) &&
                Objects.equals(keeperId, that.keeperId) &&
                Objects.equals(ownerId, that.ownerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, pickingId, entryId, materialId, materialName, specification, stockId, 
                stockStatusId, ownerTypeId, appQty, actualQty, entryMemo, unitId, baseUnitId, 
                stockUnitId, keeperTypeId, keeperId, ownerId);
    }
}
