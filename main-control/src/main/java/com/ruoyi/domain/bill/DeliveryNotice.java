package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 发货通知单主表
 * <AUTHOR>
 */
@Data
@TableName("delivery_notice")
@EqualsAndHashCode(callSuper = false)
public class DeliveryNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 销售员ID
     */
    private String salesmanId;

    /**
     * 销售部门ID
     */
    private String saleDeptId;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 交货方式
     */
    private String deliveryWay;

    /**
     * 单据日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date noticeDate;

    /**
     * 结算币别ID
     */
    private String settleCurrId;

    /**
     * 仓管员ID
     */
    private String stockerId;

    /**
     * 收货方ID
     */
    private String receiverId;

    /**
     * 结算方ID
     */
    private String settleId;

    /**
     * 收货方联系人ID
     */
    private String receiverContactId;

    /**
     * 付款方ID
     */
    private String payerId;

    /**
     * 收货方地址
     */
    private String receiveAddress;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DeliveryNotice that = (DeliveryNotice) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(billNo, that.billNo) &&
                Objects.equals(salesmanId, that.salesmanId) &&
                Objects.equals(saleDeptId, that.saleDeptId) &&
                Objects.equals(customerId, that.customerId) &&
                Objects.equals(deliveryWay, that.deliveryWay) &&
                Objects.equals(noticeDate, that.noticeDate) &&
                Objects.equals(settleCurrId, that.settleCurrId) &&
                Objects.equals(stockerId, that.stockerId) &&
                Objects.equals(receiverId, that.receiverId) &&
                Objects.equals(settleId, that.settleId) &&
                Objects.equals(receiverContactId, that.receiverContactId) &&
                Objects.equals(payerId, that.payerId) &&
                Objects.equals(receiveAddress, that.receiveAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, billNo, salesmanId, saleDeptId, customerId, deliveryWay, noticeDate, settleCurrId, stockerId, receiverId, settleId, receiverContactId, payerId, receiveAddress);
    }
}