package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 生产领料单主表
 * <AUTHOR>
 */
@Data
@TableName("production_picking_material")
@EqualsAndHashCode(callSuper = false)
public class ProductionPickingMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 备注
     */
    private String description;

    /**
     * 日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date pickingDate;

    /**
     * 生产组织ID
     */
    private String prdOrgId;

    /**
     * 发料组织ID
     */
    private String stockOrgId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductionPickingMaterial that = (ProductionPickingMaterial) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(billNo, that.billNo) &&
                Objects.equals(description, that.description) &&
                Objects.equals(pickingDate, that.pickingDate) &&
                Objects.equals(prdOrgId, that.prdOrgId) &&
                Objects.equals(stockOrgId, that.stockOrgId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, billNo, description, pickingDate, prdOrgId, stockOrgId);
    }
}
