package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 退料申请单明细表
 * <AUTHOR>
 */
@Data
@TableName("return_material_detail")
@EqualsAndHashCode(callSuper = false)
public class ReturnMaterialDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String appId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String uom;

    /**
     * 申请退料数量
     */
    private BigDecimal mrAppQty;

    /**
     * 备注
     */
    private String noteM;

    /**
     * 库存单位ID
     */
    private String unitId;

    /**
     * 计价单位ID
     */
    private String priceUnitIdF;

    /**
     * 计价数量
     */
    private BigDecimal priceQtyF;

    /**
     * 补料数量
     */
    private BigDecimal replenishQty;

    /**
     * 采购单位ID
     */
    private String purUnitId;

    /**
     * 采购数量
     */
    private BigDecimal purQty;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 仓位ID
     */
    private String stockLocId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReturnMaterialDetail that = (ReturnMaterialDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(appId, that.appId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(uom, that.uom) &&
                Objects.equals(mrAppQty, that.mrAppQty) &&
                Objects.equals(noteM, that.noteM) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(priceUnitIdF, that.priceUnitIdF) &&
                Objects.equals(priceQtyF, that.priceQtyF) &&
                Objects.equals(replenishQty, that.replenishQty) &&
                Objects.equals(purUnitId, that.purUnitId) &&
                Objects.equals(purQty, that.purQty) &&
                Objects.equals(stockId, that.stockId) &&
                Objects.equals(stockLocId, that.stockLocId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, appId, materialId, materialName, uom, mrAppQty, 
                noteM, unitId, priceUnitIdF, priceQtyF, replenishQty, 
                purUnitId, purQty, stockId, stockLocId);
    }
}
