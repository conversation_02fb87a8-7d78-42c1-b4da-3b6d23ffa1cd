package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 生产补料单明细表
 * <AUTHOR>
 */
@Data
@TableName("production_feed_material_detail")
@EqualsAndHashCode(callSuper = false)
public class ProductionFeedMaterialDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String feedId;

    /**
     * 仓库ID
     */
    private String stockId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 仓位ID
     */
    private String stockLocId;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 生产订单编号
     */
    private String moBillNo;

    /**
     * 生产订单分录内码
     */
    private String moEntryId;

    /**
     * 用料清单分录内码
     */
    private String ppBomEntryId;

    /**
     * 货主类型ID
     */
    private String ownerTypeId;

    /**
     * 申请数量
     */
    private BigDecimal appQty;

    /**
     * 实发数量
     */
    private BigDecimal actualQty;

    /**
     * 备注
     */
    private String entryDescription;

    /**
     * 报废数量
     */
    private BigDecimal scrapQty;

    /**
     * 车间ID
     */
    private String entryWorkshopId;

    /**
     * 保管者类型ID
     */
    private String keeperTypeId;

    /**
     * 保管者ID
     */
    private String keeperId;

    /**
     * 货主ID
     */
    private String ownerId;

    /**
     * 系统源单类型
     */
    private String entrySrcBillType;

    /**
     * 系统源单编号
     */
    private String entrySrcBillNo;

    /**
     * 产品货主类型ID
     */
    private String parentOwnerTypeId;

    /**
     * 产品货主ID
     */
    private String parentOwnerId;

    /**
     * 单位ID
     */
    private String unitId;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 主库存单位ID
     */
    private String stockUnitId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductionFeedMaterialDetail that = (ProductionFeedMaterialDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(feedId, that.feedId) &&
                Objects.equals(stockId, that.stockId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(specification, that.specification) &&
                Objects.equals(stockLocId, that.stockLocId) &&
                Objects.equals(produceDate, that.produceDate) &&
                Objects.equals(moBillNo, that.moBillNo) &&
                Objects.equals(moEntryId, that.moEntryId) &&
                Objects.equals(ppBomEntryId, that.ppBomEntryId) &&
                Objects.equals(ownerTypeId, that.ownerTypeId) &&
                (appQty != null && that.appQty != null ? appQty.compareTo(that.appQty) == 0 : Objects.equals(appQty, that.appQty)) &&
                (actualQty != null && that.actualQty != null ? actualQty.compareTo(that.actualQty) == 0 : Objects.equals(actualQty, that.actualQty)) &&
                Objects.equals(entryDescription, that.entryDescription) &&
                (scrapQty != null && that.scrapQty != null ? scrapQty.compareTo(that.scrapQty) == 0 : Objects.equals(scrapQty, that.scrapQty)) &&
                Objects.equals(entryWorkshopId, that.entryWorkshopId) &&
                Objects.equals(keeperTypeId, that.keeperTypeId) &&
                Objects.equals(keeperId, that.keeperId) &&
                Objects.equals(ownerId, that.ownerId) &&
                Objects.equals(entrySrcBillType, that.entrySrcBillType) &&
                Objects.equals(entrySrcBillNo, that.entrySrcBillNo) &&
                Objects.equals(parentOwnerTypeId, that.parentOwnerTypeId) &&
                Objects.equals(parentOwnerId, that.parentOwnerId) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(baseUnitId, that.baseUnitId) &&
                Objects.equals(stockUnitId, that.stockUnitId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, feedId, stockId, materialId, materialName, specification, stockLocId, produceDate,
                moBillNo, moEntryId, ppBomEntryId, ownerTypeId, appQty, actualQty, entryDescription, scrapQty,
                entryWorkshopId, keeperTypeId, keeperId, ownerId, entrySrcBillType, entrySrcBillNo,
                parentOwnerTypeId, parentOwnerId, unitId, baseUnitId, stockUnitId);
    }
}
