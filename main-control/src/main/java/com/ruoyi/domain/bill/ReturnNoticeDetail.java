package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 退货通知单明细表
 * <AUTHOR>
 */
@Data
@TableName("return_notice_detail")
@EqualsAndHashCode(callSuper = false)
public class ReturnNoticeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 关联主表ID
     */
    private String noticeId;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String materialModel;

    /**
     * 销售数量
     */
    private BigDecimal qty;

    /**
     * 基本单位ID
     */
    private String baseUnitId;

    /**
     * 备注
     */
    private String entryDescription;

    /**
     * 订单单号
     */
    private String orderNo;

    /**
     * 批号
     */
    private String lot;

    /**
     * 退货类型
     */
    private String rmType;

    /**
     * 退货日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    /**
     * 销售单位ID
     */
    private String unitId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReturnNoticeDetail that = (ReturnNoticeDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(noticeId, that.noticeId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(materialModel, that.materialModel) &&
                Objects.equals(qty, that.qty) &&
                Objects.equals(baseUnitId, that.baseUnitId) &&
                Objects.equals(entryDescription, that.entryDescription) &&
                Objects.equals(orderNo, that.orderNo) &&
                Objects.equals(lot, that.lot) &&
                Objects.equals(rmType, that.rmType) &&
                Objects.equals(deliveryDate, that.deliveryDate) &&
                Objects.equals(unitId, that.unitId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, noticeId, materialId, materialName, materialModel, qty, 
                baseUnitId, entryDescription, orderNo, lot, rmType, deliveryDate, unitId);
    }
}
