package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 生产入库单主表
 * <AUTHOR>
 */
@Data
@TableName("production_in_stock")
@EqualsAndHashCode(callSuper = false)
public class ProductionInStock implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 备注
     */
    private String description;

    /**
     * 日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inStockDate;

    /**
     * 生产组织ID
     */
    private String prdOrgId;

    /**
     * 入库组织ID
     */
    private String stockOrgId;

    /**
     * 货主ID
     */
    private String ownerId0;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductionInStock that = (ProductionInStock) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(billNo, that.billNo) &&
                Objects.equals(description, that.description) &&
                Objects.equals(inStockDate, that.inStockDate) &&
                Objects.equals(prdOrgId, that.prdOrgId) &&
                Objects.equals(stockOrgId, that.stockOrgId) &&
                Objects.equals(ownerId0, that.ownerId0);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, billNo, description, inStockDate, prdOrgId, stockOrgId, ownerId0);
    }
}
