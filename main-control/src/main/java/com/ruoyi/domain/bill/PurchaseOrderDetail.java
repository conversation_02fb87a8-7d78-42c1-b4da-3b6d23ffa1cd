package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 采购订单明细
 */
@Data
@TableName("purchase_order_detail")
public class PurchaseOrderDetail {

    /** 实体主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 采购订单ID */
    private String orderId;

    /** 物料ID */
    private String materialId;

    /** 采购单位ID */
    private String unitId;

    /** 计价单位ID */
    private String priceUnitId;

    /** 采购数量 */
    private BigDecimal qty;

    /** 产品类型 */
    private String rowType;

    /** 备注 */
    private String entryNote;

    /** 交货日期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    /** 库存单位ID */
    private String stockUnitId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PurchaseOrderDetail that = (PurchaseOrderDetail) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(materialId, that.materialId) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(priceUnitId, that.priceUnitId) &&
                (qty == that.qty || (qty != null && qty.compareTo(that.qty) == 0)) &&
                Objects.equals(rowType, that.rowType) &&
                Objects.equals(entryNote, that.entryNote) &&
                Objects.equals(deliveryDate, that.deliveryDate) &&
                Objects.equals(stockUnitId, that.stockUnitId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, orderId, materialId, unitId, priceUnitId, qty, rowType, entryNote, deliveryDate, stockUnitId);
    }
}