package com.ruoyi.domain.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 退料申请单主表
 * <AUTHOR>
 */
@Data
@TableName("return_material")
@EqualsAndHashCode(callSuper = false)
public class ReturnMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 单据类型ID
     */
    private String billTypeId;

    /**
     * 申请日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date appDate;

    /**
     * 采购组织ID
     */
    private String purchaseOrgId;

    /**
     * 退料类型
     */
    private String rmType;

    /**
     * 申请组织ID
     */
    private String appOrgId;

    /**
     * 退料地点
     */
    private String rmLoc;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 退料方式
     */
    private String rmMode;

    /**
     * 补料方式
     */
    private String replenishMode;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 退料原因
     */
    private String rmReason;

    /**
     * 申请人ID
     */
    private String applicantId;

    /**
     * 申请部门ID
     */
    private String appDeptId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReturnMaterial that = (ReturnMaterial) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(billNo, that.billNo) &&
                Objects.equals(billTypeId, that.billTypeId) &&
                Objects.equals(appDate, that.appDate) &&
                Objects.equals(purchaseOrgId, that.purchaseOrgId) &&
                Objects.equals(rmType, that.rmType) &&
                Objects.equals(appOrgId, that.appOrgId) &&
                Objects.equals(rmLoc, that.rmLoc) &&
                Objects.equals(supplierId, that.supplierId) &&
                Objects.equals(rmMode, that.rmMode) &&
                Objects.equals(replenishMode, that.replenishMode) &&
                Objects.equals(businessType, that.businessType) &&
                Objects.equals(remarks, that.remarks) &&
                Objects.equals(rmReason, that.rmReason) &&
                Objects.equals(applicantId, that.applicantId) &&
                Objects.equals(appDeptId, that.appDeptId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, billNo, billTypeId, appDate, purchaseOrgId, rmType, 
                appOrgId, rmLoc, supplierId, rmMode, replenishMode, businessType, 
                remarks, rmReason, applicantId, appDeptId);
    }
}
