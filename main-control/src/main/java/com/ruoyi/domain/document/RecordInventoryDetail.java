package com.ruoyi.domain.document;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 盘点记录详情
 */
@Data
public class RecordInventoryDetail {
    /**
     * 主键id
     */
    private String id;

    /**
     * 单据编码
     */
    private String boundIndex;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 原库存数量
     */
    private Integer materialNum;

    /**
     * 冻结数量
     */
    private Integer freezeNum;

    /**
     * 盘点总数
     */
    private Integer inventoryNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 盘点时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordDate;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 批次
     */
    private String batch;


}
