package com.ruoyi.domain.document;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料调拨单
 */
@Data
@TableName(value = "record_allot_info")
public class RecordAllotInfo {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 单据编码
     */
    private String boundIndex;

    /**
     * 调拨规则
     */
    private Integer transferRule;

    /**
     * 调拨原因
     */
    private String reasons;

    /**
     * 调拨总数
     */
    private Integer totalNum;

    /**
     * 审核状态  0：未送审，1：审核中，2：锁单
     */
    private Integer state;

    /**
     * 制单时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date recordDate;

    /**
     * 锁单时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date lockDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 制单人
     */
    private String recorder;

}