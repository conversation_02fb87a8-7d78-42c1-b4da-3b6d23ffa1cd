package com.ruoyi.domain.document;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料调拨批次详情
 */
@Data
@TableName(value = "record_allot_detail")
public class RecordAllotDetail {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 单据编码
     */
    private String boundIndex;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 来源物料编码
     */
    private String orignMaterialCode;

    /**
     * 容器编码（出）
     */
    private String orignContainer;

    /**
     * 批次（出）
     */
    private String orignBatch;

    /**
     * 生产日期（出）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date orignProduceDate;

    /**
     * 上位编号（出）
     */
    private String orignUpperIndex;

    /**
     * 目的物料编码
     */
    private String destMaterialCode;

    /**
     * 容器编码（入）
     */
    private String destContainer;

    /**
     * 批次（入）
     */
    private String destBatch;

    /**
     * 生产日期（入）
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date destProduceDate;

    /**
     * 上位编号（入）
     */
    private String destUpperIndex;

    /**
     * 备注
     */
    private String remark;

    /**
     * 批次id
     */
    private String inventoryId;


}