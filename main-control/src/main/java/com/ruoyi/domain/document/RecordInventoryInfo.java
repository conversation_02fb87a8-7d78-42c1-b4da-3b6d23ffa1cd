package com.ruoyi.domain.document;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料盘点单
 */
@Data
public class RecordInventoryInfo {
    /**
     * 主键id
     */
    private String id;

    /**
     * 单据编号
     */
    private String boundIndex;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 盘点总数
     */
    private Integer totalNum;

    /**
     * 审核状态
     * 0：未送审，1：审核中，2：锁单
     */
    private Integer state;

    /**
     * 制单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordDate;

    /**
     * 锁单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lockDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 制单人
     */
    private String recorder;

}
