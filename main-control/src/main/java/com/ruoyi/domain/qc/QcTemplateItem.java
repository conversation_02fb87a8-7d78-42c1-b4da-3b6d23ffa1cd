package com.ruoyi.domain.qc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: lhb
 * @CreateDate: 2025/4/18 14:31
 * @Description: 质检项
 */
@Data
public class QcTemplateItem {

    private String id;

    /**
     * 质检项编码
     */
    private String itemCode;

    /**
     * 质检项名称
     */
    private String itemName;

    /**
     * 质检工具
     */
    private String qcTool;

    /**
     * 质检值类型
     * 0 布尔类型 1数字类型 2string类型 3选项式类型 4文件类型 5图片类型
     */
    private Integer qcResultType;

    /**
     * 值属性
     */
    private String qcResultSpc;

    /**
     * 是否启用 0未启用 1启用
     */
    private Integer enableFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
