package com.ruoyi.domain.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;


/**
 * ERP上报主表实体类
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@Data
@TableName("erp_report_main")
public class ErpReportMain {
    
    /** 主键ID */
    private String id;
    
    /** 上报编号（唯一） */
    private String reportNo;
    
    /** 单据编码 */
    private String documentCode;
    
    /** 业务类型（1:生产领料 2:生产补料 3:生产入库 4:生产退料 5:采购入库 6:采购退货出库 7:销售出库 8:销售退货入库 9:物料调拨 10:库存盘点 11:仓库用料） */
    private Integer businessType;
    
    /** 类型（0-入库，1-出库） */
    private Integer transactionType;
    
    /** 上报状态（0-待上报，1-上报成功，2-上报失败） */
    private Integer reportStatus;
    
    /** ERP返回的单据编号 */
    private String erpBillNo;
    
    /** 总数量 */
    private BigDecimal totalQuantity;
    
    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /** 上报时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /** 上报人 */
    private String reporter;
    

}
