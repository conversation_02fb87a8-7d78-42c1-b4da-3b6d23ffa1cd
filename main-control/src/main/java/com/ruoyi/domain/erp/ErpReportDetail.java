package com.ruoyi.domain.erp;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ERP上报明细表实体类
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@Data
@TableName("erp_report_detail")
public class ErpReportDetail {
    
    /** 主键ID */
    private String id;
    
    /** 主表ID（关联erp_report_main.id） */
    private String mainId;
    
    /** 物料编码 */
    private String materialCode;
    
    /** 物料名称 */
    private String materialName;
    
    /** 容器编码 */
    private String containerCode;
    
    /** 本次数量 */
    private BigDecimal quantity;
    
    /** 单位 */
    private String unit;
    
    /** 批次号 */
    private String batchNo;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
