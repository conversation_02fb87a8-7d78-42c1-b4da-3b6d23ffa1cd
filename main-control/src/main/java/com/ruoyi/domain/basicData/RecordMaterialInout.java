package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物料进出记录
 */
@Data
@TableName("record_material_inout")
public class RecordMaterialInout {
    private String id;

    /**
     * 单据编码
     */
    private String boundIndex;

    /**
     * 单据类型，1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库 9物料调拨 10库存盘点 11仓库用料
     */
    private Integer boundType;

    /**
     * 数据来源
     */
    private Integer dataOrigin;

    /**
     * 出入类型 0：出库，1：入库
     */
    private Integer inoutType;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 批次
     */
    private String batch;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 上位编号
     */
    private String upperIndex;

    /**
     * 物料数量
     */
    private Integer totalNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 记录日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordDate;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 锁单日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lockTime;

}