package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @CreateDate: 2025/07/10
 * @Description: 物料分类类型映射配置
 */
@Data
@TableName("material_classify_type_mapping")
public class MaterialClassifyTypeMapping {
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 物料名称
     */
    private String materialName;
    
    /**
     * 类型值
     */
    private String typeValue;

    /**
     * 是否有效，0：无效，1：有效
     */
    private Integer isActive;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
