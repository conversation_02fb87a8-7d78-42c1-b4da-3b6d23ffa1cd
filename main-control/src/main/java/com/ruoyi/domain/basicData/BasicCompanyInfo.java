package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: psy
 * @CreateDate: 2026/06/11 9:57
 * @Description: 公司信息管理
 */
@Data
@TableName("basic_company_info")
public class BasicCompanyInfo {

    private String id;
    /**
     * 公司类型，0：客户，1：供应商
     */
    private int companyType;
    /**
     * 编码
     */
    private String companyCode;
    /**
     * 名称
     */
    private String companyName;
    /**
     * 分组编码
     */
    private String groupCode;
    /**
     * 分组名称
     */
    @TableField(exist = false)
    private String groupName;
    /**
     * 地址
     */
    private String companyAddress;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
