package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("basic_contact_info")
public class BasicContactInfo {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 所属公司编码
     */
    private String companyCode;
    /**
     * 联系人编码
     */
    private String contactCode;
    /**
     * 邮箱
     */
    private String contactEmail;
    /**
     * 联系人名称
     */
    private String contactName;
    /**
     * 联系人电话
     */
    private String contactTel;
    /**
     * 联系人职位
     */
    private String contactPosition;
    /**
     * 备注
     */
    private String remark;

    /**
     * 所属客户编码
     */
    private String belongCust;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
