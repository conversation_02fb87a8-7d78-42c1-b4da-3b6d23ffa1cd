package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 计量单位信息
 */
@Data
@TableName("basic_unit_info")
public class BasicUnitInfo {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 编码
     */
    private String unitCode;

    /**
     * 名称
     */
    private String unitName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否基准计量单位
     */
    private String isBaseUnit;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}