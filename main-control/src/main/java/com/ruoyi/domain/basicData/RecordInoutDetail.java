package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 出入库记录详情
 */
@Data
@TableName(value = "record_inout_detail")
public class RecordInoutDetail {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 单据编码
     */
    private String boundIndex;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 物料数量
     */
    private Integer materialNum;

    /**
     * 上位编码
     */
    private String upperIndex;

    /**
     * 批次
     */
    private String batch;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单据类型，0：采购入库单，1：采购退货单，2：生产领料出库单，3：生产退料入库单，4：销售出库单，5：销售退货单 6:仓库用料单
     */
    private Integer boundType;

    /**
     * 0合格，1不合格
     */
    private Integer isCompliant;

    /**
     * 批次库存id
     */
    private String inventoryId;

}