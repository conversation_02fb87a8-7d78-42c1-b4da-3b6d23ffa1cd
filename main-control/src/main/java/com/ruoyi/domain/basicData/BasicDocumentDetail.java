package com.ruoyi.domain.basicData;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 *单据明细详情表
 */
@Data
@TableName("basic_document_detail")
public class BasicDocumentDetail {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 父表单据编号
     */
    private String documentCode;

    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 全部数量
     */
    private Integer quantity;
    /**
     * 已完成数量
     */
    private Integer completedNum;
    /**
     * 本次数量
     */
    private Integer currentNum;
}