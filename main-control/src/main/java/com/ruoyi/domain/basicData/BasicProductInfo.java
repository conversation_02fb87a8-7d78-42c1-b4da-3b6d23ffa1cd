package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: psy
 * @CreateDate: 2025/06/11 10:41
 * @Description: 产品信息
 */
@Data
@TableName("basic_product_info")
public class BasicProductInfo {

    /**
     * 主键id
     */
    private String id;
    private String versionId;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 分类编码
     */
    private String classifyCode;
    /**
     * 产品类型 0 发酵罐，1 平养设备
     */
    private Integer productSort;
    /**
     * 规格型号
     */
    private String specifications;
    /**
     * 采购单位
     */
    private String purchaseUnit;
    /**
     * 生产单位
     */
    private String productUnit;
    /**
     * 产品图片
     */
    private String productImg;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 是否外购，0自产 1外购 2均可
     */
    private Integer isOutSource;
    /**
     * 是否锁定，0否，1是
     */
    private Integer isLock;
    /**
     * 保质期
     */
    private String expiryDate;
    /**
     * 最低库存
     */
    private int minInventory;
    /**
     * 最高库存
     */
    private int maxInventory;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateName;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
