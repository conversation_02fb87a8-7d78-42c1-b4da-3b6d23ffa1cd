package com.ruoyi.domain.basicData;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 库位基本信息
 */
@Data
public class BasicWarehouseLocation {
    /**
     * 主键id
     */
    private String id;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 父部节点id
     */
    private String parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 库位编号
     */
    private String locationCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 库位状态
     * 0可用1不可用
     */
    private Integer state;

    /**
     * 货架类型
     * 0型材货架，1板材货架，2物料货架
     */
    private Integer locationType;

    @TableField("`top`")
    private float top;

    @TableField("`left`")
    private float left;

    private float width;

    private float height;


}
