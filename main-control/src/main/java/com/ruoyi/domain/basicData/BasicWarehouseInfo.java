package com.ruoyi.domain.basicData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 仓库基本信息
 */
@Data
public class BasicWarehouseInfo {


    private String id;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库地址
     */
    private String warehouseAddress;

    /**
     * 仓库责任人
     */
    private String manager;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 仓库类型：0平面库，1立库，2货架
     */
    private int warehouseType;

    /**
     * 仓库状态，0：启用，1：停用
     */
    private int state;

    /**
     * 备注
     */
    private String remark;



}
