package com.ruoyi.domain.basicData;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 上游信号表(UpperSignalData)实体类
 *
 * <AUTHOR>
 * @since 2022-02-21 21:43:07
 */
@Data
public class UpperSignalData implements Serializable {
    private static final long serialVersionUID = -69338795066624408L;
    
    private String id;
    /**
     * 请求内容
     */
    private String request;
    /**
     * 响应内容
     */
    private String response;
    /**
     * 接收时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receTime;
    /**
     * 信号类型
     */
    private String signalType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date responseTime;

    private String productId;


    private int isWeb;
}

