package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: psy
 * @CreateDate: 2025/06/11 10:03
 * @Description: BOM清单
 */
@Data
@TableName("basic_bom_info")
public class BasicBomInfo {
    /**
     * 主键id
     */
    private String id;
    private String versionId;
    private String companyId;
    /**
     * 所属物料
     */
    private String belongMaterialCode;
    /**
     * 序号（BOM序号）
     */
    private String treeNum;
    /**
     * 父节点id
     */
    private String parentId;
    /**
     * 层级
     */
    private int level;
    /**
     * 物料（原材料/半成品/材料）编码
     */
    private String materialCode;
    /**
     * 可替代物料，多个以逗号分隔；
     */
    private String replaceCode;
    /**
     * 消耗量
     */
    private float materialNum;
    /**
     * 备注
     */
    private String remark;

    private String createName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
