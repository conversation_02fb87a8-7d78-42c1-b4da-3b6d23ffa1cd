package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: psy
 * @CreateDate: 2025/06/11 10:12
 * @Description: 物料分类
 */
@Data
@TableName("basic_material_classify")
public class BasicMaterialClassify {
    private String id;

    /**
     * 类型值
     */
    private String type;
    /**
     * 分类编码
     */
    private String classifyCode;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 分类描述
     */
    private String classifyDes;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
