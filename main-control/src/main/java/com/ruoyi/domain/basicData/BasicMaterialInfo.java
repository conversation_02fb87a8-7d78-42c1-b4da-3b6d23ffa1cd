package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * @Author: psy
 * @CreateDate: 2025/06/11 10:41
 * @Description: 物料信息
 */
@Data
@TableName("basic_material_info")
public class BasicMaterialInfo {

    /**
     * 主键id
     */
    private String id;
    private String versionId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 分类编码
     */
    private String classifyCode;
    /**
     * 物料类型 1: 建筑材料, 2: 费用类物料, 3: 原材料, 4: 成品主体, 5: 成品, 6: 半成品, 7: 维修件, 8: 发货件, 9: 组装拆卸包, 10: 设备、配件, 11: 资产类物料
     */
    private Integer materialSort;
    /**
     * 规格型号
     */
    private String specifications;
    /**
     * 物料材质
     */
    private String  texture;
    /**
     * 采购单位
     */
    private String purchaseUnit;
    /**
     * 生产单位
     */
    private String produceUnit;
    /**
     * 物料图片
     */
    private String materialImg;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 是否发货件，0：否，1是
     */
    private Integer isShippedItem;
    /**
     * 是否外购，0自产 1外购 2均可
     */
    private Integer isOutSource;
    /**
     * 是否外加工，0否，1是
     */
    private Integer isOutProcess;
    /**
     * 是否锁定，0否，1是
     */
    private Integer isLock;
    /**
     * 批次
     */
    private String batchId;
    /**
     * 保质期
     */
    private String expiryDate;
    /**
     * 最低库存
     */
    private int minInventory;
    /**
     * 最高库存
     */
    private int maxInventory;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateName;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
