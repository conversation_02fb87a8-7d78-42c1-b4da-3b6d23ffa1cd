package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicProductInfo;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.webResponse.dto.BasicProductInfoDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/17 11:44
 * @Description: 类描述
 */
@Mapper
public interface ProductInfoMapper extends BaseMapper<BasicProductInfo>{

    List<BasicProductInfoDto> queryBasicProductInfo(QueryParamVO queryParamVO);
}
