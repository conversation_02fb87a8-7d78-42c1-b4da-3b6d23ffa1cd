package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicMaterialClassify;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.basicData.BasicMaterialClassifyWithTypeNameVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/3/17 11:43
 * @Description: 类描述
 */
@Mapper
public interface BasicMaterialClassifyMapper extends BaseMapper<BasicMaterialClassify> {

    /**
     * 查询物料分类列表（包含类型中文名称）
     * @param queryParamVO 查询参数
     * @return 物料分类列表
     */
    List<BasicMaterialClassifyWithTypeNameVo> queryMaterialClassifyWithTypeName(QueryParamVO queryParamVO);

}
