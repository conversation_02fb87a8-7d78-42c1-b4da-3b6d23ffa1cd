package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicBomInfo;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.basicData.BomInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/3/17 11:43
 * @Description: 类描述
 */
public interface BasicBomInfoMapper extends BaseMapper<BasicBomInfo> {

//    List<BomInfoDto> queryBasicBom(QueryParamVO queryParamVO);

    List<BomInfoDto> queryByAncestorsAndId(@Param("id")String id);

    List<BomInfoDto> queryByAncestors(List<String> result);

    BasicBomInfo checkLocationCodeUnique(@Param("parent_id") String parentId, @Param("material_code") String materialCode);

    List<BasicBomInfo> getBomListByCode(@Param("material_code")String material_code, @Param("parent_id")String parent_id);

    List<BasicBomInfo> getBomListByCodeAndNoParent(@Param("material_code")String material_code);

    List<BomInfoDto> getRawMaterialBomByMaterialCode(@Param("bomId") String bomId);

    List<BomInfoDto> getAllFProductCodeList();

    List<BomInfoDto> getBomInfoByFProductCode(@Param("bomId") String bomId);

    List<BomInfoDto> getMaterialBomByMaterialSort(@Param("bomId") String bomId);

    List<BomInfoDto> queryMaterialBom(QueryParamVO queryParamVO);

    List<BomInfoDto> queryProductBom(QueryParamVO queryParamVO);

    List<BomInfoDto> queryMaterialBomByParentId(@Param("parent_id") String parentId);

    List<BasicBomInfo> getLatestStandardVersionSameMaterial(@Param("material_code")String materialCode);
}
