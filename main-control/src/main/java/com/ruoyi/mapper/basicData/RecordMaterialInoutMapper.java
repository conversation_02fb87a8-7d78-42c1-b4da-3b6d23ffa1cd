package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.RecordMaterialInout;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.report.RecordMaterialInoutVo;

import java.util.List;

public interface RecordMaterialInoutMapper extends BaseMapper<RecordMaterialInout> {
    int updateByPrimaryKey(RecordMaterialInout record);

    List<RecordMaterialInoutVo> queryRecordMaterialInout(QueryParamVO queryParamVO);
}