package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.DocumentInventoryDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.basicData.DocumentInventoryDetailDto;

import java.util.List;

public interface DocumentInventoryDetailMapper extends BaseMapper<DocumentInventoryDetail> {
    List<DocumentInventoryDetailDto> queryDocumentInventoryDetail(QueryParamVO queryParamVO);
}
