package com.ruoyi.mapper.basicData;
import com.ruoyi.domain.basicData.UpperSignalData;
import com.ruoyi.utils.QueryParamVO;

import java.util.List;

/**
 * 上游信号表(UpperSignalData)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-21 21:43:07
 */
public interface UpperSignalDataMapper {

    /**
     * 新增数据
     *
     * @param upperSignalData 实例对象
     * @return 影响行数
     */
    int insert(UpperSignalData upperSignalData);


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    List<UpperSignalData> querySignalData(QueryParamVO queryParamVO);
}

