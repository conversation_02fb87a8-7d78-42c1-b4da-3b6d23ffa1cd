package com.ruoyi.mapper.basicData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicWarehouseContainer;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.apache.ibatis.annotations.Param;

public interface BasicWarehouseContainerMapper extends BaseMapper<BasicWarehouseContainer> {

    String getMaxIndex(@Param("strDate") String var1);

    ContainerLocationInfoDto queryContainerLocationInfo(@Param("code") String code);

    BasicWarehouseContainer getContainerByCode(@Param("containerCode")String container_code);
}
