package com.ruoyi.mapper.basicData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicWarehouseContainer;
import com.ruoyi.domain.basicData.BasicWarehouseLocation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BasicWarehouseLocationMapper extends BaseMapper<BasicWarehouseLocation> {

    String getMaxIndex(@Param("strDate") String var1);

    BasicWarehouseLocation checkLocationNameUnique(BasicWarehouseLocation basicWarehouseLocation);

    List<BasicWarehouseLocation> selectChildrenLocationById(String id);

    void updateLocationChildren(@Param("childrens") List<BasicWarehouseLocation> children);

    int hasChildById(String id);

    List<String> getLocationByWareHouseType(@Param("wareHouseType")Integer wareHouseType,@Param("state")Integer state);

    List<String> getLocationIsUse(@Param("locationCodeList")List<String> locationCodeList);

    List<BasicWarehouseContainer> qryMaterialContainer(String locationCode);

    void deleteByLocationCode(String locationCode);
}
