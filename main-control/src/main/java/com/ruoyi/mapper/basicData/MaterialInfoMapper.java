package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicMaterialInfo;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webResponse.dto.BasicMaterialInfoDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/17 11:44
 * @Description: 类描述
 */
@Mapper
public interface MaterialInfoMapper extends BaseMapper<BasicMaterialInfo>{

    List<BasicMaterialInfoDto> queryBasicMaterialInfo(QueryParamVO queryParamVO);

    void lockBasicMaterialInfo(@Param("list") List<String> ids);
}
