package com.ruoyi.mapper.bill.delivery;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.DeliveryNotice;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.DeliveryNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发货通知单Mapper接口
 * <AUTHOR>
 */
public interface DeliveryNoticeMapper extends BaseMapper<DeliveryNotice> {

    /**
     * 查询发货通知单列表
     *
     * @param queryParamVO 查询参数
     * @return 发货通知单集合
     */
    List<DeliveryNoticeVo> queryDeliveryNotice(@Param("param") QueryParamVO queryParamVO);
}