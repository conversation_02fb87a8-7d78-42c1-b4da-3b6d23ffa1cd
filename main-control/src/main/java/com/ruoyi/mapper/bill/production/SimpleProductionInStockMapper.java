package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.SimpleProductionInStock;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionInStockVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 简单生产入库单Mapper接口
 * <AUTHOR>
 */
public interface SimpleProductionInStockMapper extends BaseMapper<SimpleProductionInStock> {

    /**
     * 查询简单生产入库单列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产入库单集合
     */
    List<SimpleProductionInStockVo> querySimpleProductionInStock(@Param("param") QueryParamVO queryParamVO);
}
