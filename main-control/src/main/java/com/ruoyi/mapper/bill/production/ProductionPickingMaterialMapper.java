package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ProductionPickingMaterial;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionPickingMaterialVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产领料单Mapper接口
 * <AUTHOR>
 */
public interface ProductionPickingMaterialMapper extends BaseMapper<ProductionPickingMaterial> {

    /**
     * 查询生产领料单列表
     *
     * @param queryParamVO 查询参数
     * @return 生产领料单集合
     */
    List<ProductionPickingMaterialVo> queryProductionPickingMaterial(@Param("param") QueryParamVO queryParamVO);
}
