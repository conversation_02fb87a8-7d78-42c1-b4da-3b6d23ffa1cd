package com.ruoyi.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ReturnNoticeDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnNoticeDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退货通知单明细Mapper接口
 * <AUTHOR>
 */
public interface ReturnNoticeDetailMapper extends BaseMapper<ReturnNoticeDetail> {

    /**
     * 查询退货通知单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 退货通知单明细集合
     */
    List<ReturnNoticeDetailVo> queryReturnNoticeDetail(@Param("param") QueryParamVO queryParamVO);
}
