package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ProductionInStockDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionInStockDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产入库单明细Mapper接口
 * <AUTHOR>
 */
public interface ProductionInStockDetailMapper extends BaseMapper<ProductionInStockDetail> {

    /**
     * 查询生产入库单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 生产入库单明细集合
     */
    List<ProductionInStockDetailVo> queryProductionInStockDetailVo(@Param("param") QueryParamVO queryParamVO);
}
