package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ProductionFeedMaterial;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionFeedMaterialVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产补料单Mapper接口
 * <AUTHOR>
 */
public interface ProductionFeedMaterialMapper extends BaseMapper<ProductionFeedMaterial> {

    /**
     * 查询生产补料单列表
     *
     * @param queryParamVO 查询参数
     * @return 生产补料单集合
     */
    List<ProductionFeedMaterialVo> queryProductionFeedMaterial(@Param("param") QueryParamVO queryParamVO);
}
