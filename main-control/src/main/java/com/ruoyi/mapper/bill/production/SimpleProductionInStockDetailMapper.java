package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.SimpleProductionInStockDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionInStockDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 简单生产入库单明细Mapper接口
 * <AUTHOR>
 */
public interface SimpleProductionInStockDetailMapper extends BaseMapper<SimpleProductionInStockDetail> {

    /**
     * 查询简单生产入库单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产入库单明细集合
     */
    List<SimpleProductionInStockDetailVo> querySimpleProductionInStockDetail(@Param("param") QueryParamVO queryParamVO);
}
