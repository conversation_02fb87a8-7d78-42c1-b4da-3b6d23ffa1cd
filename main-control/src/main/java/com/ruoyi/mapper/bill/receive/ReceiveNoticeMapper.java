package com.ruoyi.mapper.bill.receive;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ReceiveNotice;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReceiveNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收料通知单Mapper接口
 * <AUTHOR>
 */
public interface ReceiveNoticeMapper extends BaseMapper<ReceiveNotice> {

    /**
     * 查询收料通知单列表
     *
     * @param queryParamVO 查询参数
     * @return 收料通知单集合
     */
    List<ReceiveNoticeVo> queryReceiveNotice(@Param("param") QueryParamVO queryParamVO);
}
