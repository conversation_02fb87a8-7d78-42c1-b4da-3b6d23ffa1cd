package com.ruoyi.mapper.bill.returnmaterial;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ReturnMaterialDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnMaterialDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退料申请单明细Mapper接口
 * <AUTHOR>
 */
public interface ReturnMaterialDetailMapper extends BaseMapper<ReturnMaterialDetail> {

    /**
     * 查询退料申请单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 退料申请单明细集合
     */
    List<ReturnMaterialDetailVo> queryReturnMaterialDetailVo(@Param("param") QueryParamVO queryParamVO);
}
