package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ProductionFeedMaterialDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionFeedMaterialDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产补料单明细Mapper接口
 * <AUTHOR>
 */
public interface ProductionFeedMaterialDetailMapper extends BaseMapper<ProductionFeedMaterialDetail> {

    /**
     * 查询生产补料单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 生产补料单明细集合
     */
    List<ProductionFeedMaterialDetailVo> queryProductionFeedMaterialDetailVo(@Param("param") QueryParamVO queryParamVO);
}
