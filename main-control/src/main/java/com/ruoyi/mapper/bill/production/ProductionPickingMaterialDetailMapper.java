package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ProductionPickingMaterialDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionPickingMaterialDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产领料单明细Mapper接口
 * <AUTHOR>
 */
public interface ProductionPickingMaterialDetailMapper extends BaseMapper<ProductionPickingMaterialDetail> {

    /**
     * 查询生产领料单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 生产领料单明细集合
     */
    List<ProductionPickingMaterialDetailVo> queryProductionPickingMaterialDetailVo(@Param("param") QueryParamVO queryParamVO);
}
