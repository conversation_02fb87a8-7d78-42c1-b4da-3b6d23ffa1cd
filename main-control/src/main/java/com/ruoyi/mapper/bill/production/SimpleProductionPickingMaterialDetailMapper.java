package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.SimpleProductionPickingMaterialDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionPickingMaterialDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 简单生产领料单明细Mapper接口
 * <AUTHOR>
 */
public interface SimpleProductionPickingMaterialDetailMapper extends BaseMapper<SimpleProductionPickingMaterialDetail> {

    /**
     * 查询简单生产领料单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产领料单明细集合
     */
    List<SimpleProductionPickingMaterialDetailVo> querySimpleProductionPickingMaterialDetail(@Param("param") QueryParamVO queryParamVO);
}
