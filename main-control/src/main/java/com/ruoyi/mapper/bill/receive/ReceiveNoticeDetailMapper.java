package com.ruoyi.mapper.bill.receive;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ReceiveNoticeDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReceiveNoticeDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收料通知单明细Mapper接口
 * <AUTHOR>
 */
public interface ReceiveNoticeDetailMapper extends BaseMapper<ReceiveNoticeDetail> {

    /**
     * 查询收料通知单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 收料通知单明细集合
     */
    List<ReceiveNoticeDetailVo> queryReceiveNoticeDetailVo(@Param("param") QueryParamVO queryParamVO);
}
