package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ProductionInStock;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionInStockVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产入库单Mapper接口
 * <AUTHOR>
 */
public interface ProductionInStockMapper extends BaseMapper<ProductionInStock> {

    /**
     * 查询生产入库单列表
     *
     * @param queryParamVO 查询参数
     * @return 生产入库单集合
     */
    List<ProductionInStockVo> queryProductionInStock(@Param("param") QueryParamVO queryParamVO);
}
