package com.ruoyi.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ReturnNotice;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退货通知单Mapper接口
 * <AUTHOR>
 */
public interface ReturnNoticeMapper extends BaseMapper<ReturnNotice> {

    /**
     * 查询退货通知单列表
     *
     * @param queryParamVO 查询参数
     * @return 退货通知单集合
     */
    List<ReturnNoticeVo> queryReturnNotice(@Param("param") QueryParamVO queryParamVO);
}
