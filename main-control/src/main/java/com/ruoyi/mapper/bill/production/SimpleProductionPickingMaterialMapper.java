package com.ruoyi.mapper.bill.production;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.SimpleProductionPickingMaterial;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionPickingMaterialVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 简单生产领料单Mapper接口
 * <AUTHOR>
 */
public interface SimpleProductionPickingMaterialMapper extends BaseMapper<SimpleProductionPickingMaterial> {

    /**
     * 查询简单生产领料单列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产领料单集合
     */
    List<SimpleProductionPickingMaterialVo> querySimpleProductionPickingMaterial(@Param("param") QueryParamVO queryParamVO);
}
