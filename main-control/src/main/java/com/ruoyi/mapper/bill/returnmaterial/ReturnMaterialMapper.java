package com.ruoyi.mapper.bill.returnmaterial;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.ReturnMaterial;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnMaterialVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退料申请单Mapper接口
 * <AUTHOR>
 */
public interface ReturnMaterialMapper extends BaseMapper<ReturnMaterial> {

    /**
     * 查询退料申请单列表
     *
     * @param queryParamVO 查询参数
     * @return 退料申请单集合
     */
    List<ReturnMaterialVo> queryReturnMaterial(@Param("param") QueryParamVO queryParamVO);
}
