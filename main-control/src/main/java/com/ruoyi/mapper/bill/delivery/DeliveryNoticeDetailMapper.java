package com.ruoyi.mapper.bill.delivery;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.bill.DeliveryNoticeDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.DeliveryNoticeDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发货通知单明细Mapper接口
 * <AUTHOR>
 */
public interface DeliveryNoticeDetailMapper extends BaseMapper<DeliveryNoticeDetail> {

    /**
     * 查询发货通知单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 发货通知单明细集合
     */
    List<DeliveryNoticeDetailVo> queryDeliveryNoticeDetailVo(@Param("param") QueryParamVO queryParamVO);
}