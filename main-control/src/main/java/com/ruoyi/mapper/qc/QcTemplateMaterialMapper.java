package com.ruoyi.mapper.qc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.qc.QcTemplateMaterial;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.qc.QcTemplateMaterialVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/23 10:56
 * @Description: 类描述
 */
public interface QcTemplateMaterialMapper extends BaseMapper<QcTemplateMaterial> {

    List<QcTemplateMaterialVo> queryQcTemplateMaterial(QueryParamVO param);

    QcTemplateMaterial getTemplateMaterialByMaterialCodeAndQcType(@Param("materialCode") String materialCode, @Param("qcType") Integer qcType);
}
