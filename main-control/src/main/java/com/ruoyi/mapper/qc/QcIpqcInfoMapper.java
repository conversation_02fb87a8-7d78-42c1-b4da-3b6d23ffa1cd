package com.ruoyi.mapper.qc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.qc.QcIpqcTaskInfo;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.qc.QcIpqcInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 11:45
 * @Description: 类描述
 */
public interface QcIpqcInfoMapper extends BaseMapper<QcIpqcTaskInfo> {

    String getMaxIndex(@Param("strDate") String strDate);

    List<QcIpqcInfoVo> queryQcIpqcInfo(QueryParamVO param);
}

