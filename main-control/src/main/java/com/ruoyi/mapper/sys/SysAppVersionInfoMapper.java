package com.ruoyi.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.sys.SysAppVersionInfo;
import com.ruoyi.domain.sys.SysInterConfig;
import com.ruoyi.utils.QueryParamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [功能描述]
 * @date 2024/11/25 13:59
 */
public interface SysAppVersionInfoMapper extends BaseMapper<SysAppVersionInfo> {
    List<SysAppVersionInfo> queryAppInfoByPage(QueryParamVO var1);

    SysAppVersionInfo getInfoByNum(@Param("app_edition_num") String var1);
}
