package com.ruoyi.mapper.sys;


import com.ruoyi.domain.sys.ProjectSysConfig;
import org.apache.ibatis.annotations.Param;

public interface IProjectSysConfigMapper {

    ProjectSysConfig getSysConfigByType(@Param("config_type") String config_type);

    ProjectSysConfig getSysConfigByVal(@Param("config_value")String config_value);

    String getSysConfigValByType(@Param("config_type")String config_type);

    void  uptSysConfig(@Param("config_value")String config_value,@Param("config_type")String config_type);

    String getSysConfigTypeByVal(@Param("config_value")String config_value);
}
