package com.ruoyi.mapper.document;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.document.RecordAllotInfo;
import com.ruoyi.utils.QueryParamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecordAllotInfoMapper extends BaseMapper<RecordAllotInfo> {
    String getMaxIndex(@Param("headstr") String headstr);
    int updateByPrimaryKey(RecordAllotInfo record);

    List<RecordAllotInfo> queryRecordAllotInfo(QueryParamVO queryParamVO);
}