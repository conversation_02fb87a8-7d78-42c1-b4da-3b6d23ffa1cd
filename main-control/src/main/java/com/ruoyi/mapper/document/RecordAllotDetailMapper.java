package com.ruoyi.mapper.document;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.document.RecordAllotDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.document.RecordAllotDetailVo;

import java.util.List;

public interface RecordAllotDetailMapper extends BaseMapper<RecordAllotDetail> {
    void updateByPrimaryKey(RecordAllotDetail dto);

    List<RecordAllotDetailVo> queryRecordAllotInfoDetail(QueryParamVO queryParamVO);
}