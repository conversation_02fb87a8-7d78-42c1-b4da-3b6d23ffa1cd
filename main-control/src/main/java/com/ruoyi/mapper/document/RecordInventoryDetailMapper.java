package com.ruoyi.mapper.document;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.document.RecordInventoryDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.warehouse.RecordInventoryDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecordInventoryDetailMapper extends BaseMapper<RecordInventoryDetail> {
    List<RecordInventoryDetailVo> queryInventoryRecordDetail(QueryParamVO queryParamVO);

    void uptUnLockInventoryDetail(RecordInventoryDetail recordInventoryDetail);

    void deleteRecordInventoryDetail(String bound_index);

    List<RecordInventoryDetail> qryInventoryDetailByCode(@Param("bound_index") String bound_index);
}