package com.ruoyi.mapper.document;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.document.RecordInoutDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecordInoutDetailMapper extends BaseMapper<RecordInoutDetail> {

    int removeByBoundIndex(@Param("bound_index") String bound_index);

    int updateByPrimaryKey(RecordInoutDetail record);

    String getMaxIndex(@Param("headstr") String headstr);

    List<RecordInoutDetailVo> queryRecordInoutDetail(QueryParamVO queryParamVO);

    Integer qryOldNumByInoutDetail(RecordInoutDetail recordInoutDetail);

    List<RecordInoutDetail> qryByUpperIndex(@Param("upperIndex") String upperIndex);

    Integer qryUnLockBoundByUpperIndexAndState(@Param("upperIndex")String upperIndex,@Param("state")Integer state);
}