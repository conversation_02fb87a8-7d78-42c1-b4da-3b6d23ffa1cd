package com.ruoyi.mapper.document;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.document.RecordInventoryInfo;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.warehouse.RecordInventoryInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecordInventoryInfoMapper extends BaseMapper<RecordInventoryInfo> {
    String getMaxIndex(@Param("headstr") String headstr);

    List<RecordInventoryInfoVo> queryInventoryRecord(QueryParamVO queryParamVO);

    RecordInventoryInfo queryByBoundIndex(String bound_index);
}