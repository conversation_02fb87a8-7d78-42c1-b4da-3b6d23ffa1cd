package com.ruoyi.mapper.erp;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.erp.ErpReportMain;
import com.ruoyi.utils.QueryParamVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ERP上报主表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@Mapper
public interface ErpReportMainMapper extends BaseMapper<ErpReportMain> {
    
    /**
     * 查询ERP上报主表列表
     * 
     * @param queryParamVO 查询参数
     * @return ERP上报主表列表
     */
    List<ErpReportMain> selectErpReportMainList(QueryParamVO queryParamVO);
    
    /**
     * 根据单据编码查询上报记录
     * 
     * @param documentCode 单据编码
     * @return 上报记录列表
     */
    List<ErpReportMain> selectByDocumentCode(@Param("documentCode") String documentCode);
    
    /**
     * 根据上报状态查询记录
     * 
     * @param reportStatus 上报状态
     * @return 上报记录列表
     */
    List<ErpReportMain> selectByReportStatus(@Param("reportStatus") Integer reportStatus);
    
    /**
     * 根据业务类型查询记录
     * 
     * @param businessType 业务类型
     * @return 上报记录列表
     */
    List<ErpReportMain> selectByBusinessType(@Param("businessType") Integer businessType);
    
    /**
     * 更新上报状态
     * 
     * @param id 主键ID
     * @param reportStatus 上报状态
     * @param erpBillNo ERP单据编号
     * @return 更新结果
     */
    int updateReportStatus(@Param("id") String id, 
                          @Param("reportStatus") Integer reportStatus, 
                          @Param("erpBillNo") String erpBillNo);
}
