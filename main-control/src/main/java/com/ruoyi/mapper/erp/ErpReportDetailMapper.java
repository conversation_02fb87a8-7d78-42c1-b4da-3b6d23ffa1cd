package com.ruoyi.mapper.erp;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.erp.ErpReportDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ERP上报明细表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@Mapper
public interface ErpReportDetailMapper extends BaseMapper<ErpReportDetail> {
    
    /**
     * 根据主表ID查询明细列表
     * 
     * @param mainId 主表ID
     * @return 明细列表
     */
    List<ErpReportDetail> selectByMainId(@Param("mainId") String mainId);
    
    /**
     * 根据主表ID删除明细
     * 
     * @param mainId 主表ID
     * @return 删除结果
     */
    int deleteByMainId(@Param("mainId") String mainId);
    
    /**
     * 批量插入明细
     * 
     * @param details 明细列表
     * @return 插入结果
     */
    int batchInsert(@Param("details") List<ErpReportDetail> details);
}
