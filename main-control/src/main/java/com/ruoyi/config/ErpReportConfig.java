package com.ruoyi.config;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * ERP上报配置类
 * 管理业务类型与ERP接口的映射关系
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
public class ErpReportConfig {
    
    /**
     * 业务类型与ERP接口配置的映射
     */
    private static final Map<Integer, ErpReportTypeConfig> BUSINESS_TYPE_CONFIG = new HashMap<>();
    
    static {
        // 生产领料 -> 生产出库单
        BUSINESS_TYPE_CONFIG.put(1, new ErpReportTypeConfig(
            "PRD_MO_OUTSTOCK", "生产出库单", "/api/production/outstock", "生产领料上报"));
            
        // 生产补料 -> 生产出库单
        BUSINESS_TYPE_CONFIG.put(2, new ErpReportTypeConfig(
            "PRD_MO_OUTSTOCK", "生产出库单", "/api/production/outstock", "生产补料上报"));
            
        // 生产入库（成品入库） -> 生产入库单
        BUSINESS_TYPE_CONFIG.put(3, new ErpReportTypeConfig(
            "PRD_INSTOCK", "生产入库单", "/api/production/instock", "生产入库上报"));
            
        // 生产退料 -> 生产入库单
        BUSINESS_TYPE_CONFIG.put(4, new ErpReportTypeConfig(
            "PRD_INSTOCK", "生产入库单", "/api/production/instock", "生产退料上报"));
            
        // 采购入库 -> 采购入库单
        BUSINESS_TYPE_CONFIG.put(5, new ErpReportTypeConfig(
            "STK_INSTOCK", "采购入库单", "/api/purchase/instock", "采购入库上报"));
            
        // 采购退货出库 -> 采购退货出库单
        BUSINESS_TYPE_CONFIG.put(6, new ErpReportTypeConfig(
            "PUR_RETURNSTOCK", "采购退货出库单", "/api/purchase/returnstock", "采购退货出库上报"));
            
        // 销售出库 -> 销售出库单
        BUSINESS_TYPE_CONFIG.put(7, new ErpReportTypeConfig(
            "SAL_OUTSTOCK", "销售出库单", "/api/sales/outstock", "销售出库上报"));
            
        // 销售退货入库 -> 销售退货入库单
        BUSINESS_TYPE_CONFIG.put(8, new ErpReportTypeConfig(
            "SAL_RETURNSTOCK", "销售退货入库单", "/api/sales/returnstock", "销售退货入库上报"));
            
        // 物料调拨 -> 调拨单
        BUSINESS_TYPE_CONFIG.put(9, new ErpReportTypeConfig(
            "STK_TRANSFERDIRECT", "调拨单", "/api/stock/transfer", "物料调拨上报"));
            
        // 库存盘点 -> 盘点单
        BUSINESS_TYPE_CONFIG.put(10, new ErpReportTypeConfig(
            "STK_STOCKCOUNT", "盘点单", "/api/stock/count", "库存盘点上报"));
            
        // 仓库用料 -> 其他出库单
        BUSINESS_TYPE_CONFIG.put(11, new ErpReportTypeConfig(
            "STK_MISCELLANEOUS", "其他出库单", "/api/stock/miscellaneous", "仓库用料上报"));
    }
    
    /**
     * 根据业务类型获取配置
     * 
     * @param businessType 业务类型
     * @return ERP接口配置
     */
    public static ErpReportTypeConfig getConfig(Integer businessType) {
        return BUSINESS_TYPE_CONFIG.get(businessType);
    }
    
    /**
     * 获取所有配置
     * 
     * @return 所有配置映射
     */
    public static Map<Integer, ErpReportTypeConfig> getAllConfigs() {
        return new HashMap<>(BUSINESS_TYPE_CONFIG);
    }
    
    /**
     * 检查业务类型是否支持
     * 
     * @param businessType 业务类型
     * @return 是否支持
     */
    public static boolean isSupported(Integer businessType) {
        return BUSINESS_TYPE_CONFIG.containsKey(businessType);
    }
    
    /**
     * ERP上报类型配置
     */
    @Data
    public static class ErpReportTypeConfig {
        /** ERP表单ID */
        private String formId;
        
        /** 表单名称 */
        private String formName;
        
        /** 接口地址 */
        private String interfaceUrl;
        
        /** 描述 */
        private String description;
        
        public ErpReportTypeConfig(String formId, String formName, String interfaceUrl, String description) {
            this.formId = formId;
            this.formName = formName;
            this.interfaceUrl = interfaceUrl;
            this.description = description;
        }
    }
}
