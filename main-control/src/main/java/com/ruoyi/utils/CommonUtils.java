package com.ruoyi.utils;

import com.ruoyi.common.utils.ExceptionUtil;
import com.ruoyi.common.utils.StringUtils;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileInputStream;
import lombok.SneakyThrows;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

public class CommonUtils {
    private static final Logger logger = LoggerFactory.getLogger(CommonUtils.class);

    /**
     * 参数校验处理
     * 驼峰形式转下划线
     * @param res
     * @param logger
     * @return
     */
    public static String dealBindingResult(BindingResult res, Logger logger) {
        List<FieldError> fieldErrors = res.getFieldErrors();
        StringBuilder sb = new StringBuilder();

        fieldErrors.forEach(fieldError -> {
            String fieldCamelStr = fieldError.getField();
            String underLineStr = ChangeChar.camelToUnderline(fieldCamelStr, 1);

            sb.append(underLineStr + " : " + fieldError.getDefaultMessage() + "; ");

            //日志打印不符合校验的字段名和错误提示
            logger.info("error field is : {} ,message is : {}", underLineStr, fieldError.getDefaultMessage());
        });

        return sb.toString();
    }

    /**
     * 忽略属性命名形式
     * @param res
     * @param logger
     * @return
     */
    public static String dealBindingResultIgnoreNameType(BindingResult res, Logger logger) {
        List<FieldError> fieldErrors = res.getFieldErrors();
        StringBuilder sb = new StringBuilder();

        fieldErrors.forEach(fieldError -> {
            String underLineStr = fieldError.getField();

            sb.append(underLineStr + " : " + fieldError.getDefaultMessage() + "; ");

            //日志打印不符合校验的字段名和错误提示
            logger.info("error field is : {} ,message is : {}", underLineStr, fieldError.getDefaultMessage());
        });

        return sb.toString();
    }

    /**
     * 对象非空属性转换为map
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> convertNotNullToMap(Object obj) {
        Map<String, Object> result = new HashMap<>();

        List<Field> fieldList = new ArrayList<>();

        Field[] fieldArray = null;
        Class cla = obj.getClass();

        while (cla != null) {
            fieldArray = cla.getDeclaredFields();
            fieldList.addAll(new ArrayList<>(Arrays.asList(fieldArray)));
            cla = cla.getSuperclass();
        }

        for (Field field : fieldList) {
            field.setAccessible(true);

            Object curObject = null;
            try {
                curObject = field.get(obj);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            if (curObject != null) {
                result.put(field.getName(), curObject);
            }
        }
        return result;
    }

    /**
     * 手机号校验
     *
     * @param in
     * @return
     */
    public static boolean validateMobilePhone(String in) {
        Pattern pattern = Pattern.compile("^1[0-9]{10}$");

        return pattern.matcher(in).matches();
    }

    /**
     * 身份证校验
     *
     * @param IDNumber
     * @return
     */
    public static boolean isIDNumber(String IDNumber) {
        if (IDNumber == null || "".equals(IDNumber)) {
            return false;
        }
        // 定义判别用户身份证号的正则表达式（15位或者18位，最后一位可以为字母）
        String regularExpression = "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|" +
                "(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
        //假设18位身份证号码:41000119910101123X  410001 19910101 123X
        //^开头
        //[1-9] 第一位1-9中的一个      4
        //\\d{5} 五位数字           10001（前六位省市县地区）
        //(18|19|20)                19（现阶段可能取值范围18xx-20xx年）
        //\\d{2}                    91（年份）
        //((0[1-9])|(10|11|12))     01（月份）
        //(([0-2][1-9])|10|20|30|31)01（日期）
        //\\d{3} 三位数字            123（第十七位奇数代表男，偶数代表女）
        //[0-9Xx] 0123456789Xx其中的一个 X（第十八位为校验值）
        //$结尾

        //假设15位身份证号码:410001910101123  410001 910101 123
        //^开头
        //[1-9] 第一位1-9中的一个      4
        //\\d{5} 五位数字           10001（前六位省市县地区）
        //\\d{2}                    91（年份）
        //((0[1-9])|(10|11|12))     01（月份）
        //(([0-2][1-9])|10|20|30|31)01（日期）
        //\\d{3} 三位数字            123（第十五位奇数代表男，偶数代表女），15位身份证不含X
        //$结尾


        boolean matches = IDNumber.matches(regularExpression);

        //判断第18位校验值
        if (matches) {

            if (IDNumber.length() == 18) {
                try {
                    char[] charArray = IDNumber.toCharArray();
                    //前十七位加权因子
                    int[] idCardWi = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
                    //这是除以11后，可能产生的11位余数对应的验证码
                    String[] idCardY = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
                    int sum = 0;
                    for (int i = 0; i < idCardWi.length; i++) {
                        int current = Integer.parseInt(String.valueOf(charArray[i]));
                        int count = current * idCardWi[i];
                        sum += count;
                    }
                    char idCardLast = charArray[17];
                    int idCardMod = sum % 11;
                    if (idCardY[idCardMod].toUpperCase().equals(String.valueOf(idCardLast).toUpperCase())) {
                        return true;
                    } else {
                        return false;
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
            }

        }
        return matches;
    }

    /**
     * 获取n天前/后的日期字符串
     *
     * @param format
     * @param amount
     * @return
     */
    public static String getDateStr(String format, int amount) {
        Calendar calendar1 = Calendar.getInstance();
        SimpleDateFormat sdf1 = new SimpleDateFormat(format);
        calendar1.add(Calendar.DATE, amount);

        return sdf1.format(calendar1.getTime());
    }


    public static String getRequestJson(HttpServletRequest request) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();

        try {
            br = new BufferedReader(new InputStreamReader(request.getInputStream(), "utf-8"));
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return sb.toString();
    }

    public static Long getLongByDate(Date date) {
        if (date != null) {
            return date.getTime();
        } else {
            return null;
        }
    }

    public static byte[] getImageFromNetByUrl(String strUrl) {
        try {
            URL url = new URL(strUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            InputStream inStream = conn.getInputStream();//通过输入流获取图片数据
            byte[] btImg = readInputStream(inStream);//得到图片的二进制数据
            return btImg;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while ((len = inStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();
    }

    public static String getBase64Str(String imgUrl) {
        byte[] data = null;
        try {
            data = getImageFromNetByUrl(imgUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);
    }

    public static String getBase64strByFile(MultipartFile file) throws IOException {
        byte[] data = file.getBytes();
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);
    }

    /**
     * 通过共享文件夹配置获取base64
     * @param strUrl
     * @param host
     * @param userName
     * @param passWord
     * @return
     */
    public static String getBase64BySmbFileConfig(String strUrl,String host,String userName,String passWord){
        if (StringUtils.isNotBlank(strUrl) && StringUtils.isNotBlank(host)) {
            if (!strUrl.startsWith("/") ) {
                strUrl = "/" + strUrl;
            }
            return StringUtils.isNotBlank(userName) && StringUtils.isNotBlank(passWord) ? getBase64BySmbFileWithAuth(strUrl, userName, passWord, host) : getBase64BySmbFile(strUrl, host);
        } else {
            logger.info("主机地址为空或URL为空，无法处理请求！");
            return null;
        }
    }

    /**
     * 读取共享文件夹下的套料图并生成base64字符串
     * 无账号模式
     * @param strUrl 文件路径
     * @return  文件内容
     */
    private static String getBase64BySmbFile(String strUrl,String host) {
        try {
            // smb://userName:passWord@host/path/shareFolderPath/fileName
            String url = "smb://" + host + strUrl;
            //无账号
            SmbFile smbFile = new SmbFile(url);
            return getFileContentBySmbFile(strUrl,smbFile);
        } catch (Exception e) {
            logger.info("共享文件夹操作异常：" + e.getMessage(),e);
        }
        return null;
    }

    /**
     * 读取共享文件夹下的套料图并生成base64字符串
     * 有账号模式
     * @param strUrl 文件路径
     * @return  文件内容
     */
    private static String getBase64BySmbFileWithAuth(String strUrl,String userName,String passWord,String host) {

        try {
            // smb://userName:passWord@host/path/shareFolderPath/fileName
            String url = "smb://" + host + strUrl;
            //有账号
            NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication("", userName, passWord);
            SmbFile smbFile = new SmbFile(url, auth);
//            String remoteUrl = "smb://" + userName + ":" + passWord + "@" + host + strUrl ;
//            smbGet(remoteUrl,localPath);
            return getFileContentBySmbFile(strUrl,smbFile);
        }catch (Exception e) {
            logger.info("共享文件夹操作异常：" + e.getMessage(),e);
        }
        return null;
    }

    // 通过smb下载文件保存到本地
    @SuppressWarnings("unused")
    public static void smbGet(String remoteUrl, String localDir) {
        InputStream in = null;
        BufferedOutputStream out = null;
        File file = new File(localDir);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        try {
            SmbFile remoteFile = new SmbFile(remoteUrl);
            if (remoteFile != null) {
                String fileName = remoteFile.getName();
                File localFile = new File(localDir + File.separator + fileName);
                in = new BufferedInputStream(new SmbFileInputStream(remoteFile));
                out = new BufferedOutputStream(new FileOutputStream(localFile));
                for(byte[] buffer = new byte[1024]; in.read(buffer) != -1; buffer = new byte[1024]) {
                    out.write(buffer);
                }
                return;
            }
            logger.info("共享文件不存在");
        } catch (Exception var17) {
            var17.printStackTrace();
            return;
        } finally {
            try {
                out.close();
                in.close();
            } catch (IOException var16) {
                var16.printStackTrace();
            }
        }
    }

    private static String getFileContentBySmbFile(String strUrl,SmbFile remoteFile) throws Exception {
        if (!remoteFile.exists()) {
            logger.info(strUrl + "路径的文件不存在！");
            return null;
        }
        SmbFileInputStream smbFileInputStream = new SmbFileInputStream(remoteFile);
        byte[] btImg = readInputStream(smbFileInputStream);//得到图片的二进制数据
        if (btImg == null || btImg.length == 0) {
            logger.info(strUrl + "路径的文件内容为空！");
            return null;
        }
        return Base64.getEncoder().encodeToString(btImg);
    }


    /**
     * 通过FTP文件夹配置获取base64

     * @return
     */
    public static String getBase64ByFTPFileConfig(String host, int port,String username, String password, String remotePath,String fileName,String localPath){
        if (StringUtils.isEmpty(host)){
            logger.info("FTP服务器地址为空！");
            return null;
        }else {
            boolean flag = downFile(host, port, username, password, remotePath, fileName, localPath);
            if (flag){
                try {
                    String fileUrl = localPath + "/" + fileName;
                    FileInputStream fileInputStream = new FileInputStream(fileUrl);
                    try {
                        byte[] btImg = readInputStream(fileInputStream);
                        if (btImg == null || btImg.length == 0) {
                            logger.info(fileUrl + "路径的文件内容为空！");
                            return null;
                        }
                        return Base64.getEncoder().encodeToString(btImg);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
                return null;
            }else {
                logger.info("FTP服务器账号密码错误或者文件路径不存在");
                return null;
            }
        }
    }


    /**
     * Description: 从FTP服务器下载文件
     * @param ip FTP服务器ip
     * @param port FTP服务器端口
     * @param username FTP登录账号
     * @param password FTP登录密码
     * @param remotePath FTP服务器上的相对路径
     * @param fileName 要下载的文件名
     * @param localPath 下载后保存到本地的路径
     * @return
     */
    public static boolean downFile(String ip, int port,String username, String password, String remotePath,String fileName,String localPath) {
        boolean success = false;
        FTPClient ftp = new FTPClient();
        try {
            int reply;
            ftp.connect(ip, port);
            //如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器
            //登录
            ftp.login(username, password);
            ftp.enterLocalPassiveMode();
            reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return success;
            }
            //转移到FTP服务器目录
            int fileNameIndex = remotePath.lastIndexOf("/");
            if(fileNameIndex != -1){
                ftp.changeWorkingDirectory(remotePath.substring(0,remotePath.lastIndexOf("/")));
            }else {
              logger.info("没有目录不需要切换目录！");
            }
            FTPFile[] fs = ftp.listFiles();
            for(FTPFile ff:fs){
                if(ff.getName().equals(fileName)){
                    //下载目录不存在就创建
                    File file=new File(localPath);
                    if(!file.getParentFile().exists())
                    {
                        file.getParentFile().mkdirs();
                    }
                    if(!file.exists() && !file .isDirectory()) {
                        file .mkdir();
                    }
                    File localFile = new File(localPath + "/" + ff.getName());
                    OutputStream is = new FileOutputStream(localFile);
                    ftp.retrieveFile(ff.getName(), is);
                    is.close();
                    success = true;
                }
            }
            ftp.logout();
        } catch (IOException e) {
            logger.info(e.getMessage());
            return  success;
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                }
            }
        }
        return success;
    }

    /**
     * Description: 从FTP服务器获取文件的base64
     * @param ip FTP服务器hostname
     * @param port FTP服务器端口
     * @param username FTP登录账号
     * @param password FTP登录密码
     * @param remotePath FTP服务器上的相对路径
     * @param fileName 要下载的文件名
     * @return
     */
    public static String getBase64FromFtp(String ip, int port,String username, String password, String remotePath,String fileName) {
        String base64 ="";
        FTPClient ftp = new FTPClient();
        try {
            int reply;
            ftp.connect(ip, port);
            //如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器
            //登录
            ftp.login(username, password);
            ftp.enterLocalPassiveMode();
            reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return base64;
            }
            //转移到FTP服务器目录
            int fileNameIndex = remotePath.lastIndexOf("/");
            if(fileNameIndex != -1){
                ftp.changeWorkingDirectory(remotePath.substring(0,remotePath.lastIndexOf("/")));
            }else {
                logger.info("没有目录不需要切换目录！");
            }
            FTPFile[] fs = ftp.listFiles();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            for(FTPFile ff:fs){
                if(ff.getName().equals(fileName)){
                    ftp.retrieveFile(fileName,out);
                    break;
                }
            }
            byte[] bytes = out.toByteArray();
            BASE64Encoder encoder=new BASE64Encoder();
            base64=encoder.encode(bytes);
            ftp.logout();
        } catch (IOException e) {
            logger.info(e.getMessage());
            return  base64;
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                }
            }
        }
        return base64;
    }

    /**
     * Description: 向FTP服务器上传文件 （文件名可带中文）
     * @param url FTP服务器hostname
     * @param port FTP服务器端口
     * @param username FTP登录账号
     * @param password FTP登录密码
     * @param path FTP服务器保存目录
     * @param filename 上传到FTP服务器上的文件名
     * @param input 输入流
     * @return 成功返回true，否则返回false
     */
    public static boolean uploadCNFile(String url,int port,String username, String password, String path, String filename, InputStream input) {
        try {
            filename = new String(filename.getBytes("UTF-8"), "iso-8859-1");
            return uploadFile(url,port,username,password,path,filename,input);
        } catch (Exception e){
            return false;
        }
    }

    /**
     * Description: FTP服务器指定文件获取内容String（例子：ftp获取套料JSON文件）
     * @param ip
     * @param port
     * @param username
     * @param password
     * @param remotePath
     * @param fileName
     * @return
     */
    public static String downJsonByFtp(String ip, int port, String username, String password, String remotePath, String fileName) {
        FTPClient ftp = new FTPClient();
        String mapStr = null;
        try {
            ftp.connect(ip, port);
            ftp.login(username, password);
            ftp.enterLocalPassiveMode();
            int reply = ftp.getReplyCode();
            if (FTPReply.isPositiveCompletion(reply)) {
                int fileNameIndex = remotePath.lastIndexOf("/");
                if (fileNameIndex != -1) {
                    ftp.changeWorkingDirectory(remotePath.substring(0, remotePath.lastIndexOf("/")));
                } else {
                    logger.info("没有目录不需要切换目录！");
                }
                FTPFile[] fs = ftp.listFiles();
                FTPFile[] var12 = fs;
                int var13 = fs.length;
                for (int var14 = 0; var14 < var13; ++var14) {
                    FTPFile ff = var12[var14];
                    if (ff.getName().equals(fileName)) {
                        String filePath = remotePath + "/" + fileName;
                        InputStream inputStream = ftp.retrieveFileStream(filePath);
                        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
                        StringBuffer stringBuffer = new StringBuffer();
                        for (; ; ) {
                            String line = br.readLine();
                            if (line == null) {
                                break;
                            }
                            stringBuffer.append(line);
                        }
                        mapStr = stringBuffer.toString();
                    }
                }
                ftp.logout();
                return mapStr;
            }
            ftp.disconnect();
            return mapStr;
        } catch (Exception e) {
            logger.info(ExceptionUtil.getExceptionMessage(e));
            return mapStr;
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException var27) {
                }
            }

        }
    }

    /**
     * Description: 向FTP服务器上传文件
     * @param url FTP服务器hostname
     * @param port FTP服务器端口
     * @param username FTP登录账号
     * @param password FTP登录密码
     * @param path FTP服务器保存目录
     * @param filename 上传到FTP服务器上的文件名
     * @param input 输入流
     * @return 成功返回true，否则返回false
     */
    public static boolean uploadFile(String url,int port,String username, String password, String path, String filename, InputStream input) {
        boolean success = false;
        FTPClient ftp = new FTPClient();
        try {
            int reply;
            //连接FTP服务器
            ftp.connect(url, port);
            //如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器
            //登录
            ftp.login(username, password);
            //设置被动模式
            ftp.enterLocalPassiveMode();
            //设置文件类型为binary
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return success;
            }
            String[] directories = path.split("/");
            for (String directory : directories) {
                if (directory.length() > 0) {
                    //如果子目录不存在，则创建子目录
                    if (!ftp.changeWorkingDirectory(directory)) {
                        //创建子目录
                        if (!ftp.makeDirectory(directory)) {
                            //如果创建目录失败，则返回
                            logger.info("创建ftp服务器文件目录" + directory + "失败");
                            return success;
                        } else {
                            ftp.changeWorkingDirectory(directory);
                        }
                    }else {
                        ftp.changeWorkingDirectory(directory);
                    }
                }
            }
            success = ftp.storeFile(filename, input);
            input.close();
            ftp.logout();
        } catch (IOException e) {
            e.printStackTrace();
            return  success;
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                }
            }
        }
        return success;
    }

    @SneakyThrows
    public static InputStream getFileStreamFromFTP(String filePath, String userName, String passWord, String host, Integer port) {
        FTPClient ftpClient = new FTPClient();
        try {
            // 连接到 FTP 服务器
            ftpClient.connect(host, port);
            ftpClient.login(userName, passWord);
            // 设置传输模式为被动模式（可根据 FTP 服务器设置调整）
            ftpClient.enterLocalPassiveMode();
            // 设置文件传输类型为二进制
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            // 确保切换到正确的目录
            String directory = filePath.substring(0, filePath.lastIndexOf('/'));
            String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
            boolean changeDir = ftpClient.changeWorkingDirectory(directory);
            if (!changeDir) {
                logger.info("无法切换到目标目录：" + directory);
                return null;
            }
            // 获取文件流
            InputStream inputStream = ftpClient.retrieveFileStream(fileName);
            if (inputStream == null) {
                logger.info("无法获取 FTP 文件流，文件可能不存在：" + fileName);
                ftpClient.logout();
                ftpClient.disconnect();
                return null;
            }
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
            return bufferedInputStream;
        } catch (IOException e) {
            logger.error("FTP 连接或文件检索失败", e);
            e.printStackTrace();
        } finally {
            // 确保断开连接
            try {
                if (ftpClient.isConnected()) {
                    ftpClient.disconnect();
                }
            } catch (IOException e) {
                logger.error("关闭 FTP 连接时发生错误", e);
            }
        }
        return null;
    }

    /**
     *  判断ftp指定目录下文件是否存在
     *  filePath 文件路径 （示例：2024-06-26/3240228A9194.json）
     */
    public static boolean isFileExist(String url, int port, String username, String password, String filePath) {
        FTPClient ftpClient = new FTPClient();
        boolean fileExists = false;

        try {
            ftpClient.connect(url, port);
            ftpClient.login(username, password);
            ftpClient.enterLocalPassiveMode();

            String directory = filePath.substring(0, filePath.lastIndexOf('/'));
            String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);

            ftpClient.changeWorkingDirectory(directory);

            fileExists = ftpClient.listFiles(fileName).length > 0;

            ftpClient.logout();
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (ftpClient.isConnected()) {
                    ftpClient.disconnect();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }

        return fileExists;
    }
}
