package com.ruoyi.utils;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.*;
import net.sf.jasperreports.engine.util.FileBufferedOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ReportUtil {

    static {
        System.setProperty("java.awt.headless", "true");
    }

    public static void reportBasicsPdf(JasperPrint jasperPrint, HttpServletResponse response, String filename) {
        if (null != jasperPrint) {
            FileBufferedOutputStream fbos = new FileBufferedOutputStream();
            JRPdfExporter exporter = new JRPdfExporter();
            exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM,fbos);
            exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT,jasperPrint);

            exporter.setParameter(JRPdfExporterParameter.OUTPUT_FILE_NAME,filename);
            exporter.setParameter(JRPdfExporterParameter.CHARACTER_ENCODING, "UTF-8");
            try {
                exporter.exportReport();
                fbos.close();
                if (fbos.size() > 0) {
                    response.setContentType("application/pdf");
                    response.setHeader("Content-Disposition","inline; filename=\"" + filename + "\"");
                    response.setContentLength(fbos.size());
                    ServletOutputStream ouputStream = response.getOutputStream();
                    try {
                        fbos.writeData(ouputStream);
                        fbos.dispose();
                        ouputStream.flush();
                    } finally {
                        if (null != ouputStream) {
                            ouputStream.close();
                        }
                    }
                }
            } catch (Exception e1) {
                e1.printStackTrace();
            } finally {
                if (null != fbos) {
                    try {
                        fbos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    fbos.dispose();
                }
            }
        }
    }


    public static void reportBasicsExcel(JasperPrint jasperPrint, HttpServletResponse response, String filename) {
        if (null != jasperPrint) {
            FileBufferedOutputStream fbos = new FileBufferedOutputStream();
            JRXlsAbstractExporter exporter = new JRXlsExporter();
            exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, fbos);
            exporter.setParameter(JRExporterParameter.JASPER_PRINT,
                    jasperPrint);
            exporter.setParameter(
                    JRXlsAbstractExporterParameter.IS_ONE_PAGE_PER_SHEET,
                    Boolean.FALSE);
            exporter.setParameter(
                    JRXlsAbstractExporterParameter.IS_WHITE_PAGE_BACKGROUND,
                    Boolean.FALSE);
            try {
                exporter.exportReport();
                fbos.close();
                if (fbos.size() > 0) {
                    response.setContentType("application/xls");
                    response.setHeader("Content-Disposition","inline; filename=\"" + filename + "\"");
                    response.setContentLength(fbos.size());
                    ServletOutputStream ouputStream = response.getOutputStream();
                    try {
                        fbos.writeData(ouputStream);
                        fbos.dispose();
                        ouputStream.flush();
                    } finally {
                        if (null != ouputStream) {
                            ouputStream.close();
                        }
                    }
                }
            } catch (Exception e1) {
                e1.printStackTrace();
            } finally {
                if (null != fbos) {
                    try {
                        fbos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    fbos.dispose();
                }
            }
        }
    }

}
