package com.ruoyi.utils;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.utils.constant.CommonConstant;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;

public class AutoNum {
    public static String START_TIME_VALUE = "202107010000";
    public static String BAN_CHENG_PIN_START_VALUE = "301";
    public static void main(String[] args) {

//		AutoNum an = new AutoNum();
//		String str = an.getNum("1", "1201602020000");
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//		Date date = new Date();
//		String[] sd = sdf.format(date).split("-");
//		String strDate = "";
//		for (int i = 0; i < sd.length; i++) {
//			strDate += sd[i];
//		}
    }

    public String getStrDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String[] sd = sdf.format(date).split("-");
        String strDate = "";
        for (int i = 0; i < sd.length; i++) {
            strDate += sd[i];
        }
        return strDate;
    }

    /**
     * 在用此类时，必须先明确自己模块编号的开头
     * //@param start编号开头
     * //@param num从数据库查询出的最大编号
     * //@return
     */
    public String getNum(String start, String num) {
        if (StringUtils.isNotEmpty(num) && !num.startsWith(start)) {
            System.out.println("编号的开头与从数据库取得的编号开头不相同");
            return "";
        }
        String strDate = "";// 流水号时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //SimpleDateFormat sdf = new SimpleDateFormat("yy-MM-dd");
        Date date = new Date();
        // 格式化当前时间，并按字符'-'分割
        String[] sd = sdf.format(date).split("-");
        // 截取编号中的日期
        String time = num.substring(start.length(), start.length()+8).toString();

        for (int i = 0; i < sd.length; i++) {
            strDate += sd[i];
        }

        // 如果当天或者表中没有记录，返回当天第一条记录
        if (!time.equals(strDate) || num.length() == (start.length() + strDate.length())) {
            return start + strDate + "0001";
        }
        // 截取编号最后的流水号
        String end = num.substring(start.length()+8);

        String s2 = "";
        int lg = Integer.parseInt(end);

        // 对流水号结尾的四位数字进行判断，以便增加
        if (lg >= 0 && lg < 9) {
            s2 = "000" + (lg + 1);
        } else if (lg >= 9 && lg < 99) {
            s2 = "00" + (lg + 1);
        } else if (lg >= 99 && lg < 999) {
            s2 = "0" + (lg + 1);
        } else if (lg >= 999 && lg < 9999) {
            s2 = "" + (lg + 1);
        }
        // 返回自动生成后的流水号
        return start += strDate + s2;
    }

    public String getSpecialNum(String start, String num) {

        if (!num.startsWith(start)) {
            System.out.println("编号的开头与从数据库取得的编号开头不相同");
            return "";
        }
        String strDate = "";// 流水号时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //SimpleDateFormat sdf = new SimpleDateFormat("yy-MM-dd");
        Date date = new Date();
        // 格式化当前时间，并按字符'-'分割
        String[] sd = sdf.format(date).split("-");
        // 截取编号中的日期
        String time = num.substring(start.length(), start.length()+8).toString();

        for (int i = 0; i < sd.length; i++) {
            strDate += sd[i];
        }

        // 如果当天或者表中没有记录，返回当天第一条记录
        if (!time.equals(strDate) || num.equals("")) {
            return start + strDate + "001";
        }
        // 截取编号最后的流水号
        String end = num.substring(start.length()+8);

        String s2 = "";
        int lg = Integer.parseInt(end);

        // 对流水号结尾的三位数字进行判断，以便增加
        if (lg >= 0 && lg < 9) {
            s2 = "00" + (lg + 1);
        } else if (lg >= 9 && lg < 99) {
            s2 = "0" + (lg + 1);
        } else if (lg >= 99 && lg < 999) {
            s2 = "" + (lg + 1);
        }
        // 返回自动生成后的流水号
        return start += strDate + s2;
    }

    /**
     * 获取下一个值
     * @param index
     * @return
     */
    public String getNextNum(String index) {
        long dindex = 0;
        if(StringUtils.isNotEmpty(index)){
            dindex = Long.valueOf(index);
            dindex += 1;
        }
        return dindex + "";
    }


    /**
     * 获取项唯一编码
     * 例子：QI 25060001 （前缀 +  年份 + 月份）
     */
    public static String geMaxCode(String prefix,String maxCode) {
        LocalDate today = LocalDate.now();
        String year = String.format("%02d", today.getYear() % 100);
        String month = String.format("%02d", today.getMonthValue());
        String datePrefix = prefix + year + month;
        // 查询已有最大编码
        int nextSerial = 1;
        if (maxCode != null) {
            // 拆出最后的 4 位流水号
            String serialStr = maxCode.substring(maxCode.length() - 4);
            nextSerial = Integer.parseInt(serialStr) + 1;
        }
        return String.format("%s%04d", datePrefix, nextSerial);
    }

}
