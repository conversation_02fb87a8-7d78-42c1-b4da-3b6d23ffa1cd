package com.ruoyi.utils;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

/**
 * Gson公用方法
 */
public class GsonUtils {

    private static final Gson gson = new GsonBuilder().setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).registerTypeAdapterFactory(ResponseResultTypeAdaptor.FACTORY).serializeNulls().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    private GsonUtils(){}

    /**
     * 对象转json
     * @param object
     * @return
     */
    public static String toJsonString(Object object) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
        }
        return null;
    }

    /**
     * json转对象
     * @param gsonString
     * @param cls
     * @param <T>
     * @return
     */
    public static <T> T gsonToBean(String gsonString, Class<T> cls) {
        T t = null;
        if (gson != null) {
            t = gson.fromJson(gsonString, cls);
        }
        return t;
    }

    public static <T> T jsonToBean(String jsonString, Class<T> cls) {
        T t = null;
        if (jsonString != null) {
            t = JSONObject.parseObject(jsonString,cls);
        }
        return t;
    }

    /**
     * json转对象
     * @param json
     * @param type
     * @param <T>
     * @return
     */
    public static <T> T fromJson(String json, Type type) {
        return gson.fromJson(json, type);
    }

    /**
     * 通用 string 转 list
     * @param gsonString
     * @return
     */
    public static <T> List<T> gsonToListBean(String gsonString,Class clazz) {
        //泛型转换
        Type type = new ParameterizedTypeImpl(clazz);
        List<T> list =  new Gson().fromJson(gsonString, type);
        return list;
    }

    /**
     * Gson解析不支持泛型，利用ParameterizedType获取泛型参数类型
     */
    private static class ParameterizedTypeImpl implements ParameterizedType {
        Class clazz;

        public ParameterizedTypeImpl(Class clz) {
            clazz = clz;
        }
        @Override
        public Type[] getActualTypeArguments() {
            //返回实际类型组成的数据
            return new Type[]{clazz};
        }
        @Override
        public Type getRawType() {
            //返回原生类型，即HashMap
            return List.class;
        }
        @Override
        public Type getOwnerType() {
            //返回Type对象
            return null;
        }
    }
}
