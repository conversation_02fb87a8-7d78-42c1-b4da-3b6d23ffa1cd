package com.ruoyi.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;

public class HttpUtils {

	public static final Logger logger = LoggerFactory.getLogger(HttpUtils.class);

	/**
	 * 获取操作响应结果
	 * @param paramObj 参数对象
	 * @return
	 */
	public static ResponseResult sendRequestGetResponseResult(String url, Object paramObj){
		return sendRequestGetResponseResult(url,paramObj,null);
	}


	public static ResponseResult sendRequestGetResponseResult(String url, Object paramObj,Integer readTimeOut){
		String responseStr = HttpUtils.getApiResponseData(url,paramObj,readTimeOut);
		String dealMsg = "";
		int code = 400;
		Object data = null;
		ResponseResult responseResult = null;
		if(StringUtils.isEmpty(responseStr)){
			dealMsg = "发出操作请求，未收到响应结果，操作无效！URL:" + url;
			logger.error("发出操作请求，未收到响应结果，操作无效！URL:" + url);
			return ResponseResult.getErrorResult(dealMsg);
		}else{
/*			GsonBuilder gsonBuilder = new GsonBuilder();
			gsonBuilder.registerTypeAdapterFactory(ResponseResultTypeAdaptor.FACTORY);
			Gson gson = gsonBuilder.create();
			return gson.fromJson(responseStr, ResponseResult.class);*/
			return GsonUtils.jsonToBean(responseStr,ResponseResult.class);
		}
	}

	public static String getApiResponseData(String url,Object object,Integer readTimeout){
		String response = "";
		HttpURLConnection connection = null;
		try{
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
			String paramJson = objectMapper.writeValueAsString(object);
			connection =  getConnection(url,null);
			connection.setRequestMethod("POST");// 请求方式
			connection.setRequestProperty("Content-Type", "application/json");
			connection.setConnectTimeout(6000);
			connection.setReadTimeout(6000);
			if(readTimeout != null && readTimeout.intValue() > 3000){
				connection.setReadTimeout(readTimeout);
			}
			OutputStream output = connection.getOutputStream();
			output.write(paramJson.getBytes("UTF-8"));
			output.flush();
			output.close();
			BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(),"UTF-8"));
			StringBuffer buffer = new StringBuffer();
			String line = "";
			while ((line = reader.readLine()) != null) {
				buffer.append(line);
			}
			response = buffer.toString();
			reader.close();
			connection.disconnect();
		}catch (Exception e){
			logger.error("发出操作请求，未收到响应结果，操作无效！URL:" + url);
			return "";
		}finally {
			if(connection!=null){
				connection.disconnect();
			}
		}
		return response;
	}


	public static String getApiResponseData(String url,Object object){
		return getApiResponseData(url,object,null);
	}

	private static HttpURLConnection getConnection(String url, String token) throws IOException {
		URL postUrl = new URL(url);
		HttpURLConnection connection = (HttpURLConnection) postUrl.openConnection();
		connection.setDoOutput(true);
		connection.setDoInput(true);
		connection.setUseCaches(false);
		connection.setInstanceFollowRedirects(true);
		connection.setRequestProperty("Connection", "Keep-Alive");
		connection.setRequestProperty("Charset", "UTF-8");
		if(!StringUtils.isEmpty(token)){
			connection.addRequestProperty("Authorization", "Bearer " + token);// 有权限认证的服务必须加上认证
		}
		return connection;
	}
}
