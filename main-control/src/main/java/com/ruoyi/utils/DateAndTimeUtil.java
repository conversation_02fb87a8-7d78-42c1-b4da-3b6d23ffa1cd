package com.ruoyi.utils;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.WeekFields;
import java.util.*;

public class DateAndTimeUtil {

    public static String DATE_FORMAT_DETAIL = "yyyy-MM-dd HH:mm:ss";

    public static String DATE_FORMAT_DETAIL_CONTACT = "yyyyMMddHHmmss";

    public static String DATE_T_FORMAT_DETAIL = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";

    public static String DATE_FORMAT_DET = "yyyyMMdd";

    public static String BEGINTIME = " 00:00:00";

    public static String ENDTIME = " 23:59:59";

    public static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

    public static SimpleDateFormat formatMm =  new SimpleDateFormat("yyyy-MM");

    public static SimpleDateFormat formatNoSeparator = new SimpleDateFormat(DATE_FORMAT_DET);

    public static SimpleDateFormat formatMonth = new SimpleDateFormat("MM-dd");

    public static SimpleDateFormat formatDetail = new SimpleDateFormat(DATE_FORMAT_DETAIL);

    public static SimpleDateFormat formatDetailContact = new SimpleDateFormat(DATE_FORMAT_DETAIL_CONTACT);

    public static SimpleDateFormat formatDetailDe = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS+08:00");

    public static DecimalFormat  formatDec  = new DecimalFormat("0.00");

    /**
     * 获取最近7天的日期(不包括当天)
     * @return
     */
    public synchronized static List<String> getLastSevenDay(){
        List<String> list = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for(int i = 7;i >= 0;i--){
            Calendar c = Calendar.getInstance();
            c.add(Calendar.DATE, - i);
            Date monday = c.getTime();
            String preMonday = sdf.format(monday);
            list.add(preMonday);
        }
        return list;
    }

    /**
     * 获取最近7天的日期(包括当天)
     * @return
     */
    public synchronized static List<String> getNowSevenDay(){
        return getLatestDay(7);
    }

    /**
     * 计算当天到月底还有多少天
     * @return
     */
    public synchronized static int getDayToMonthEnd(){
        Calendar c = Calendar.getInstance();
        int d = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        int now = c.get(Calendar.DAY_OF_MONTH);
        return d - now;
    }
    /**
     * 获取当前时间
     * @return
     */
    public synchronized static String getNowTime(){
        Date date = new Date();
        String time = format.format(date);
        return time;
    }

    public static List<String> getLatestDay(int days) {
        List<String> list = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for(int i = days - 1;i >= 0;i--){
            Calendar c = Calendar.getInstance();
            c.add(Calendar.DATE, - i);
            Date monday = c.getTime();
            String preMonday = sdf.format(monday);
            list.add(preMonday);
        }
        return list;
    }

    public String getObjNowTime(){
        Date date = new Date();
        String time = format.format(date);
        return time;
    }

    public synchronized static String getNowTimeNotYear(){
        Date date = new Date();
        String time = formatMonth.format(date);
        return time;
    }

    /**
     * 获取当前时间，具体到时分秒
     * @return
     */
    public synchronized static String getNowDetailTime(){
        Date date = new Date();
        String time = formatDetail.format(date);
        return time;
    }

    /**
     * 获取当前时间
     * @return
     */
    public synchronized static String getNowTimeNoSeparator(){
        Date date = new Date();
        return formatNoSeparator.format(date);
    }
    /***
     * 日期月份减一个月
     * @param datetime
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String dateFormatBeforeOneMonth(String datetime) {
        Date date = null;
        try {
            date = format.parse(datetime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.MONTH, -1);
        date = cl.getTime();
        return format.format(date);
    }

    /***
     * 日期增加或减少指定的时间
     * @param datetime
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String dateCalMinutesFormat(String datetime,int minutes) {
        Date date = null;
        try {
            date = formatDetail.parse(datetime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.MINUTE, minutes * -1);
        date = cl.getTime();
        return formatDetail.format(date);
    }

    /***
     * 日期月份增加或减少指定的月数
     * @param datetime
     *            日期(2014-11)
     * @return 2014-10
     */
    public static String dateCalMonthFormat(String datetime,int monthNum) {
        Date date = null;
        try {
            date = format.parse(datetime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.MONTH, monthNum);
        date = cl.getTime();
        return format.format(date);
    }
    /**
     * 返回字符串的格式化时间值
     * @param date
     * @return
     */
    public static String dateFormatDay(Date date) {
        return format.format(date);
    }

    public static String dateFormatByDay(String date) {
        try {
            return format.format(format.parse(date));
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 指定时间延后指定天数
     * @param date
     * //@param period
     * @return
     * @throws ParseException
     */
    public static String dateSubPointDay(Date date,int day) {
        String temp = "";
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.add(Calendar.DATE, day);
        temp = format.format(cl.getTime());
        return temp;
    }

    /****
     * 获取月末最后一天
     * @param sDate
     *            2014-11-24
     * @return 30
     */
    public static String getMonthMaxDay(String sDate) {
        Calendar cal = Calendar.getInstance();
        Date date = null;
        try {
            date = format.parse(sDate + "-01");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        cal.setTime(date);
        int last = cal.getActualMaximum(Calendar.DATE);
        return String.valueOf(last);
    }

    // 判断是否是月末
    public static boolean isMonthEnd(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if (cal.get(Calendar.DATE) == cal
                .getActualMaximum(Calendar.DAY_OF_MONTH))
            return true;
        else
            return false;
    }

    /***
     * 日期减一天、加一天
     *
     * @param option
     *            传入类型 pro：日期减一天，next：日期加一天
     * @param _date
     *            2014-11-24
     * @return 减一天：2014-11-23或(加一天：2014-11-25)
     */
    public static String checkOption(String option, String _date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cl = Calendar.getInstance();
        Date date = null;

        try {
            date = (Date) sdf.parse(_date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        cl.setTime(date);
        if ("pre".equals(option)) {
            // 时间减一天
            cl.add(Calendar.DAY_OF_MONTH, -1);

        } else if ("next".equals(option)) {
            // 时间加一天
            cl.add(Calendar.DAY_OF_YEAR, 1);
        } else {
            // do nothing
        }
        date = cl.getTime();
        return sdf.format(date);
    }

    /**
     * 日期减少或增加指定天数
     * @param option
     * @param _date
     * @param day
     * @return
     */
    public static String checkOptionGetDate(String option, String _date,int day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cl = Calendar.getInstance();
        Date date = null;

        try {
            date = (Date) sdf.parse(_date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        cl.setTime(date);
        if ("pre".equals(option)) {
            // 时间减一天
            cl.add(Calendar.DAY_OF_MONTH, -day);
        } else if ("next".equals(option)) {
            // 时间加一天
            cl.add(Calendar.DAY_OF_YEAR, day);
        } else {
            // do nothing
        }
        date = cl.getTime();
        return sdf.format(date);
    }

    /***
     * 判断日期是否为当前月， 是当前月返回当月最小日期和当月目前最大日期以及传入日期上月的最大日和最小日
     * 不是当前月返回传入月份的最大日和最小日以及传入日期上月的最大日和最小日
     *
     * @param date
     *            日期 例如：2014-11
     * @return String[] 开始日期，结束日期，上月开始日期，上月结束日期
     * @throws ParseException
     */
    public static String[] getNow_Pre_Date(String date) throws ParseException {

        String[] str_date = new String[4];
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat sdf_full = new SimpleDateFormat("yyyy-MM-dd");
        String stMonth = sdf.format(now);
        String stdate = "";// 开始日期
        String endate = "";// 结束日期
        String preDate_start = "";// 上月开始日期
        String preDate_end = "";// 上月结束日期

        // 当前月
        if (date.equals(stMonth)) {
            stdate = stMonth + "-01"; // 2014-11-01
            endate = sdf_full.format(now);// 2014-11-24
            preDate_start = dateCalMonthFormat(stdate,6);// 2014-10-01
            preDate_end = dateCalMonthFormat(endate,6);// 2014-10-24
        } else {
            // 非当前月
            String monthMaxDay = getMonthMaxDay(date);
            stdate = date + "-01";// 2014-10-01
            endate = date + "-" + monthMaxDay;// 2014-10-31
            preDate_start = dateCalMonthFormat(stdate,6);// 2014-09-01
            preDate_end = dateCalMonthFormat(endate,6);// 2014-09-30
        }
        str_date[0] = stdate;
        str_date[1] = endate;
        str_date[2] = preDate_start;
        str_date[3] = preDate_end;
        return str_date;
    }

    public static boolean ifOverdue(String scdate,String period){
        //用java中date的before方法，date1.before(date2)，返回boolean 类型
        Date date = new Date();
        try {
            Date scd = format.parse(scdate);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(scd);
            rightNow.add(Calendar.MONTH, Integer.valueOf(period));
            Date dt1 = rightNow.getTime();
            //如果 返回true 过期、返回false 未过期
            System.out.println(format.format(dt1));
            return date.after(dt1);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     *
     * @Title : findYearMonth
     * @Type : YearAndMonth
     * @date : 2014年4月3日 下午10:48:52
     * @Description : 获取年月
     * @return
     */
    public static String getYearMonth()
    {
        int year;
        int month;
        String date;
        Calendar calendar = Calendar.getInstance();
        /**
         * 获取年份
         */
        year = calendar.get(Calendar.YEAR);
        /**
         * 获取月份
         */
        month = calendar.get(Calendar.MONTH) + 1;
        /**
         * 拼接年份和月份
         */
        date = year + "-" + ( month<10 ? "0" + month : month);
        /**
         * 返回当前年月
         */
        return date;
    }

    public static void main(String[] args) throws ParseException {
        String str = getYearMonth();
        /**
         * 打印现在年份和月份
         */
        System.out.println("现在年份和月份：" + str);


        String bmonth = "2018-05";
        System.out.println(DateAndTimeUtil.getNowYear());

        try {
            System.out.println(ifOverdue("2022-08-28","1"));
            System.out.println("========>" + dateSubPointDay(DateAndTimeUtil.getNowDate(),20));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String a = "100";
        String b = a;
        a = (Double.valueOf(a) - 50) + "";
        System.out.println(b + "==" + a);

        /*
         * String a =DateAndTimeUtil.dateFormat(new Date());
         * System.out.println(a); String b =
         * DateAndTimeUtil.subMonth("2014-03-31"); System.out.println(b);
         * SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd"); Date
         * dt=sdf.parse("2014-03-31");
         * System.out.println(DateAndTimeUtil.isMonthEnd(dt));
         */
        // str = DateAndTimeUtil.checkOption("next", "2014-11-30");
        // str = getMonthMaxDay("2014-11-24");
        // str = dateFormat("2014-11");
        //str = getNow_Pre_Date("2014-10")[0];

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        System.out.println(sdf.format(DateAndTimeUtil.getNowDate()));
        Timestamp stamp = new Timestamp(1514822400000L);
        System.out.println("-->" + sdf.format(stamp));

        String date_string="201609";
        // 利用java中的SimpleDateFormat类，指定日期格式，注意yyyy,MM大小写
        // 这里的日期格式要求javaAPI中有详细的描述，不清楚的话可以下载相关API查看
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");

        // SimpleDateFormat format=new SimpleDateFormat("yyyyMM");
        // 设置日期转化成功标识
        boolean dateflag=true;
        // 这里要捕获一下异常信息
        try {
            Date date = format.parse(date_string);
        } catch (ParseException e) {
            dateflag = false;
        } finally {
            // 成功：true ;失败:false
            System.out.println("日期是否满足要求" + dateflag);
        }
    }
    /**
     * 判断时间是否符合格式要求
     * @param date
     * @return
     */
    public static boolean judgeTimeFormat(String date){
        // 设置日期转化成功标识
        boolean dateflag = true;
        // 这里要捕获一下异常信息
        try {
            format.parse(date);
        } catch (ParseException e) {
            dateflag = false;
        } finally {
            // 成功：true ;失败:false
            System.out.println("日期是否满足要求" + dateflag);
        }
        return dateflag;
    }
    /**
     * 指定的时间增加指定的年份
     * @param scdate
     * @param numberYear
     * @return
     */
    public static String addPointYears(String scdate, int numberYear) {
        // TODO Auto-generated method stub
        String[] st = scdate.split("-");
        int year = Integer.valueOf(st[0]) + numberYear;
        String newTimeStr = year + "-" + st[1] + "-" + st[2];
        return newTimeStr;
    }

    public synchronized static Date getNowDate() {
        // TODO Auto-generated method stub
        Date date = new Date();
        Date nowTime = null;
        try {
            nowTime = formatDetail.parse(formatDetail.format(date));
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return nowTime;
    }
    public synchronized static String getContactTime() {
        // TODO Auto-generated method stub
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }

    public synchronized static String getContactYMDTime() {
        // TODO Auto-generated method stub
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(new Date());
    }

    public synchronized static String getNowYear() {
        // TODO Auto-generated method stub
        SimpleDateFormat sdf = new SimpleDateFormat("yy");
        return sdf.format(new Date());
    }

    public synchronized static String getDetailTime() {
        // TODO Auto-generated method stub
        return formatDetail.format(new Date());
    }

    public synchronized static String getDetailTimeContact() {
        // TODO Auto-generated method stub
        return formatDetailContact.format(new Date());
    }

    public synchronized static String getTimeStrByDate(Date date) {
        // TODO Auto-generated method stub
        return formatDetail.format(date);
    }

    /**
     * 返回传参过来的时间的月日数据
     * @param scygdate  yyyy-MM-dd
     * @return
     */
    public static String getMonthDayOfDate(String scygdate) {
        // TODO Auto-generated method stub
        String[] dates = scygdate.split("-");
        if(dates.length > 2){
            return dates[1] + "-" + dates[2];
        }else{
            return scygdate;
        }
    }

    /**
     * 格式化时间，返回年月日
     * @param scygdate  yyyy-MM-dd
     * @return
     */
    public static String getFormatYearMonthDay(String scygdate) {
        // TODO Auto-generated method stub
        String[] dates = scygdate.split("-");
        if(dates.length > 2){
            return dates[0] + "-" +dates[1] + "-" + dates[2];
        }else{
            return scygdate;
        }
    }

    /**
     * 获取年周期
     *
     * @return
     * @throws ParseException
     */
    public static String getYearAndWeek(String today) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(today);
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(date);
        return year+""+calendar.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 将字符串类型的时间转时间类型
     * @param dateStr
     * @return
     * @throws ParseException
     */
    public static Date getDateTypeFromStringTime(String dateStr) throws ParseException{
        return format.parse(dateStr);
    }

    /**
     * yyyy-MM-dd HH:mm:ss 格式
     * @param dateStr
     * @return
     * @throws ParseException
     */
    public static Date getDateFromStringTime(String dateStr) throws ParseException{
        return formatDetail.parse(dateStr);
    }

    /**
     * 2022-10-30T22:51:30.000+08:00  格式
     * @param dateStr
     * @return
     * @throws ParseException
     */
    public static Date getDateFromStringTimeDe(String dateStr) throws ParseException{
        return formatDetailDe.parse(dateStr);
    }
    /**
     * 计算时间间隔结果为分
     * @param
     */
    public static String   calculationTimeInterval(String finishTime,String startTime){
        String   minutes = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_DETAIL);
        try {
            if (StringUtils.isNotEmpty(finishTime) && StringUtils.isNotEmpty(startTime)) {
                float diff = sdf.parse(finishTime).getTime() - sdf.parse(startTime).getTime();
                minutes = String.valueOf(diff / (1000 * 60));  //获取分钟
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return minutes;
    }
    /**
     * 划算成分。
     * @param
     */
    public static String  calculationTimePlus(float  time) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(time / (1000 * 60));
    }

    /**
     * 获取根据时间段算出第几周的周一跟周日
     * @return AjaxResult
     */
    public static List<String> getWeekByDate(String startDate, String endDate) throws ParseException {
        List<String> weekList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar s_c = Calendar.getInstance();
        Calendar e_c = Calendar.getInstance();
        Date s_time = sdf.parse(startDate);
        Date e_time = sdf.parse(endDate);

        String first_week = getFirstDayOfWeek(startDate);//取得开始日期指定日期所在周的第一天
        String last_week = getLastDayOfWeek(endDate);//取得结束日期指定日期所在周的最后一天

        s_c.setTime(s_time);
        e_c.setTime(e_time);

        int currentWeekOfYear = s_c.get(Calendar.WEEK_OF_YEAR);
        int currentWeekOfYear_e = e_c.get(Calendar.WEEK_OF_YEAR);

        if (currentWeekOfYear_e == 1) {
            currentWeekOfYear_e = 53;
        }

        int j = 12;
        for (int i=0; i < currentWeekOfYear_e; i++) {
            int dayOfWeek = e_c.get(Calendar.DAY_OF_WEEK) - 2;
            e_c.add(Calendar.DATE, - dayOfWeek); //得到本周的第一天
            String s_date = sdf.format(e_c.getTime());
            e_c.add(Calendar.DATE, 6);  //得到本周的最后一天

            String e_date = sdf.format(e_c.getTime());
            e_c.add(Calendar.DATE, -j); //减去增加的日期

            //只取两个日期之间的周
            if(currentWeekOfYear == currentWeekOfYear_e - i + 2){
                break;
            }
            //只取两个日期之间的周
            if(compareDate(first_week, s_date) && compareDate(s_date, last_week)
                    && compareDate(first_week, e_date) && compareDate(e_date, last_week)){
                //超过选择的日期，按选择日期来算
//                    String s = year + "年的第" + (currentWeekOfYear - i) + "周" + "(" + s_date + "至" + e_date + ")";
                weekList.add(s_date  + "至" + e_date);
            }
        }
        Collections.reverse(weekList);
        return weekList;
    }

    /**
     * 取得指定日期所在周的第一天
     */
    public static String getFirstDayOfWeek(String date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date time = sdf.parse(date);
            Calendar c = new GregorianCalendar();
            c.setFirstDayOfWeek(Calendar.MONDAY);
            c.setTime(time);
            c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek()); // Monday
            return sdf.format(c.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }


    }

    /**
     * compareDate方法
     * <p>方法说明：
     * 		比较endDate是否是晚于startDate；
     * 			如果是，返回true， 否则返回false
     * </p>
     */
    public static boolean compareDate(String startDate, String endDate) {
        try {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date1 = dateFormat.parse(startDate);
            Date date2 = dateFormat.parse(endDate);
            if (date1.getTime() > date2.getTime())
                return false;
            return true; //startDate时间上早于endDate

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 取得指定日期所在周的最后一天
     */
    public static String getLastDayOfWeek(String date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date time = sdf.parse(date);
            Calendar c = new GregorianCalendar();
            c.setFirstDayOfWeek(Calendar.MONDAY);
            c.setTime(time);
            c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6); // Sunday
            return sdf.format(c.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取根据第几周算出周一跟周日的日期
     * @return AjaxResult
     */
    public static List<String> getDataByWeek(String week) throws ParseException {
        List<String> dateList = new ArrayList<>();
        WeekFields weekFields= WeekFields.ISO;
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        LocalDate localDate = now.withYear(year).with(weekFields.weekOfYear(), Long.parseLong(week));
        //周一
        LocalDate monday  = localDate.with(weekFields.dayOfWeek(), 1L);
        dateList.add(String.valueOf(monday));
        //周日
        LocalDate sunday  = localDate.with(weekFields.dayOfWeek(), 7L);
        dateList.add(String.valueOf(sunday));
        return  dateList;
    }

    /**
     * 获取日期获取是本年的第几周
     * @return AjaxResult
     */
    public static int getWeekByDate(String today) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        date = format.parse(today);
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.SATURDAY);
        calendar.setTime(date);
        return  (calendar.get(Calendar.WEEK_OF_YEAR)) - 1;
    }

    /**
     * 获取最近  ‘day’  天的日期
     * @return AjaxResult
     */
    public static List<String> getNearlyDays(int day) {
        List<String> dateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 0; i < day; i++) {
            Date date = DateUtils.addDays(new Date(), -i );
            String formatDate = sdf.format(date);
            dateList.add(formatDate);
        }
        return dateList;
    }

    /**
     * 根据开始月份合结束月份算出所有的月份
     * @return AjaxResult
     */
    public static List<String> getDateByMonth(Date startDate, Date endDate) {
        Calendar c = Calendar.getInstance();
        List<String> list = new ArrayList<String>();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM");
        for (; startDate.getTime() <= endDate.getTime();) {
            String _startDate = df.format(startDate);
            list.add(_startDate);
            c.setTime(startDate);
            c.add(Calendar.MONTH, 1);    //加一个月
            startDate = c.getTime();
        }
        return list;
    }

    /**
     * 根据开始日期合结束日期算出所有的日期
     * @return AjaxResult
     */
    public static List<String> getDateByDay(Date startDate, Date endDate) {
        Calendar c = Calendar.getInstance();
        List<String> list = new ArrayList<String>();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        for (; startDate.getTime() <= endDate.getTime();) {
            String _startDate = df.format(startDate);
            list.add(_startDate);
            c.setTime(startDate);
            c.add(Calendar.DATE, 1);    //加一个天
            startDate = c.getTime();
        }
        return list;
    }

    /**
     * 根据月份算出当月的最后一天跟第一天的时间
     * @return AjaxResult
     */
    public static List<String> getFirstday_Lastday_Month(Date date) {
        ArrayList<String> months = new ArrayList<>();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 0);
        Date theDate = calendar.getTime();

        //当月第一天
        GregorianCalendar gcLast = (GregorianCalendar) Calendar.getInstance();
        gcLast.setTime(theDate);
        gcLast.set(Calendar.DAY_OF_MONTH, 1);
        String day_first = df.format(gcLast.getTime());
        StringBuffer str = new StringBuffer().append(day_first).append(" 00:00:00");
        day_first = str.toString();

        //当月最后一天
        calendar.add(Calendar.MONTH, 1);    //加一个月
        calendar.set(Calendar.DATE, 1);        //设置为该月第一天
        calendar.add(Calendar.DATE, -1);    //再减一天即为上个月最后一天
        String day_last = df.format(calendar.getTime());
        StringBuffer endStr = new StringBuffer().append(day_last).append(" 23:59:59");
        day_last = endStr.toString();

        months.add(day_first);
        months.add(day_last);
        return months;
    }

    /**
     * 判断当前时间是否在0点-8点之间
     * @return AjaxResult
     */
    public static Boolean JudgeNowDate() {
        Boolean flag =false;
        SimpleDateFormat dateForMater = new SimpleDateFormat("HHmmss");
        String date = dateForMater.format(new Date());
        int time = Integer.parseInt(date);
        if (time > 0 && time < 80000) {
            flag = false;
        } else {
            flag = true;
        }
        return flag;
    }

    /**
     * 获取最近十五天的日期（早上8点-第二天早上8点为一天）
     * @return AjaxResult
     */
    public static List<String> getNearlyDaysFif() {
        List<String> dateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Boolean  flag = JudgeNowDate();
        for (int i = 0; i < 15; i++) {
            Date date = null;
            if (flag){
                date = DateUtils.addDays(new Date(), -i );
            }else {
                date = DateUtils.addDays(new Date(), -i - 1);
            }
            String formatDate = sdf.format(date);
            dateList.add(formatDate);
        }
        return dateList;
    }

    /**
     * 计算两个时间相差多少秒
     * @return AjaxResult
     */
    public static Integer calculationTimeSecond(String beginTime,String endTime){
        SimpleDateFormat simpleFormat = new SimpleDateFormat(DATE_FORMAT_DETAIL);
        Date fromDate3 = null;
        try {
            fromDate3 = simpleFormat.parse(beginTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Date toDate3 = null;
        try {
            toDate3 = simpleFormat.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        long from3 = fromDate3.getTime();
        long to3 = toDate3.getTime();
        return (int) ((to3 - from3) / (1000));
    }

    /**
     * 计算两个时间相差多少分钟
     * @return AjaxResult
     */
    public static Integer calculationTimeMinute(String beginTime,String endTime){
        SimpleDateFormat simpleFormat = new SimpleDateFormat(DATE_FORMAT_DETAIL);
        Date fromDate3 = null;
        try {
            fromDate3 = simpleFormat.parse(beginTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Date toDate3 = null;
        try {
            toDate3 = simpleFormat.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        long from3 = fromDate3.getTime();
        long to3 = toDate3.getTime();
        return (int) ((to3 - from3) / (1000 * 60));
    }

    /**
     * 计算两个时间相差多少天
     * @param beginTime 起始时间
     * @param endTime 结束时间
     * @return 相差的天数
     */
    public static Integer calculationTimeDay(Date beginTime, Date endTime) {
        // 计算两个日期的毫秒数差
        long fromMillis = beginTime.getTime();
        long toMillis = endTime.getTime();

        // 计算天数差
        return (int) ((toMillis - fromMillis) / (1000 * 60 * 60 * 24)); // 毫秒 -> 天
    }

    public static Date transferLongToDate(String dateFormat,Long millSec){

        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);

        Date date= new Date(millSec);
        //sdf.format(date);
        return date;

    }

    public synchronized static Long getCueTimeLong() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前时间到这天结束的时间差(单位：秒)
     */
    public static int getSecond(){
        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime maxTime = localDateTime.with(LocalTime.MAX);
        int startSecond = localDateTime.getSecond();
        int endSecond = maxTime.getSecond();
        return endSecond-startSecond;
    }

    public static String getStartTime(int hour,int i) {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.SECOND, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.HOUR_OF_DAY, hour);
        todayStart.set(Calendar.MILLISECOND, 0);
        todayStart.add(Calendar.DAY_OF_MONTH, i);
        Date time = todayStart.getTime();
        return formatDetail.format(time);
    }

    public static String getEndTime(int second,int minute,int hour,int i) {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.SECOND, second);
        todayEnd.set(Calendar.MINUTE, minute);
        todayEnd.set(Calendar.HOUR_OF_DAY, hour);
        todayEnd.set(Calendar.MILLISECOND, 999);
        todayEnd.add(Calendar.DAY_OF_MONTH, i);
        Date time = todayEnd.getTime();
        return formatDetail.format(time);
    }

    //获取当天及前面六天日期
    public static List<String> getNearlyWeekDates() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        //过去七天
        c.setTime(new Date());
        String today = format.format(new Date());
        c.add(Calendar.DATE, - 7);
        Date d = c.getTime();
        String day = format.format(d);
        List<String> result = getBetweenDates(day,today,false);

        return result;
    }

    public static List<String> getBetweenDates(String startTime, String endTime,boolean isIncludeStartTime){
        List<String> result = new ArrayList<>();
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date d1 = new SimpleDateFormat("yyyy-MM-dd").parse(startTime);//定义起始日期
            Date d2 = new SimpleDateFormat("yyyy-MM-dd").parse(endTime);//定义结束日期  可以去当前月也可以手动写日期。
            Calendar dd = Calendar.getInstance();//定义日期实例
            dd.setTime(d1);//设置日期起始时间
            if(isIncludeStartTime) {
                result.add(format.format(d1));
            }
            while (dd.getTime().before(d2)) {//判断是否到结束日期
                dd.add(Calendar.DATE, 1);//进行当前日期加1
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String str = sdf.format(dd.getTime());
                result.add(str);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }
}
