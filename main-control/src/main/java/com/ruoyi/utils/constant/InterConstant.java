package com.ruoyi.utils.constant;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [接口地址常量]
 * @date 2024/6/14 15:09
 */
public class InterConstant {
    /**
     * 接口配置
     */
    public class InterType {
        // 料框立库使用率查询
        public static final String BOX_WAREHOUSE_RATE = "1";
        // 板材立库使用率查询
        public static final String PLATE_WAREHOUSE_RATE = "2";
        // 型材立库使用率查询
        public static final String PROFILE_WAREHOUSE_RATE = "3";
        // 料框立库状态查询
        public static final String BOX_WAREHOUSE_STATE = "4";
        // 板材立库状态查询
        public static final String PLATE_WAREHOUSE_STATE = "5";
        // 型材立库状态查询
        public static final String PROFILE_WAREHOUSE_STATE = "6";
        // 料框立库入库
        public static final String BOX_SEND_IN = "7";
        // 板材立库入库
        public static final String PLATE_SEND_IN = "8";
        // 型材立库入库
        public static final String PROFILE_SEND_IN = "9";
        // 料框立库物料查询
        public static final String BOX_INVENTORY_STATISTICS = "10";
        // 板材立库物料查询
        public static final String PLATE_INVENTORY_STATISTICS = "11";
        // 型材立库物料查询
        public static final String PROFILE_INVENTORY_STATISTICS = "12";
        // 料框立库出库
        public static final String BOX_SEND_OUT = "13";
        // 板材立库出库
        public static final String PLATE_SEND_OUT = "14";
        // 型材立库出库
        public static final String PROFILE_SEND_OUT = "15";
        // mes单据报工
        public static final String MES_DOCUMENT_REPORT = "16";

    }

}
