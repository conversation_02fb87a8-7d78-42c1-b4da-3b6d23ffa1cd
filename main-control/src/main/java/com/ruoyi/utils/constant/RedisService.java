package com.ruoyi.utils.constant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [Redis工具处理类]
 * @date 2022/9/23 16:03
 */
@Service
public class RedisService {
    private final Logger logger = LoggerFactory.getLogger(RedisService.class);

    @Resource
    private RedisTemplate redisTemplate;

    public boolean containRedisKey(String redisKey) {
        if(redisTemplate == null){
            return false;
        }
        return redisTemplate.hasKey(redisKey);
    }



//    public void storeBoardStatus(String redisKey, BoardDevStateVo param, int cacheMillSecond) {
//        ValueOperations<String, BoardDevStateVo> operations = redisTemplate.opsForValue();
//        operations.set(redisKey,param , cacheMillSecond, TimeUnit.MILLISECONDS);
//    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value)
    {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key)
    {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }


    public boolean deleteObject(final String key)
    {
        return redisTemplate.delete(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
