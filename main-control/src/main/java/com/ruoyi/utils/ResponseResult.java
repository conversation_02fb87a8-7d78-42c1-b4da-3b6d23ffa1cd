package com.ruoyi.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应结果
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ResponseResult implements Serializable {
    private Integer code;
    private String msg;
    private Object data;
    private String timestamp;

    public ResponseResult(){

    }

    public ResponseResult(Integer code, String msg, String data){
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static ResponseResult getSuccessResult(){
        ResponseResult innerDataResponse = new ResponseResult();
        innerDataResponse.setCode(200);
        innerDataResponse.setMsg("success");
        return innerDataResponse;
    }

    public static ResponseResult getSuccessResult(String msg,Object data){
        ResponseResult innerDataResponse = new ResponseResult();
        innerDataResponse.setCode(200);
        innerDataResponse.setMsg(msg);
        innerDataResponse.setData(data);
        return innerDataResponse;
    }

    public static ResponseResult getErrorResult(String msg){
        ResponseResult innerDataResponse = new ResponseResult();
        innerDataResponse.setCode(400);
        innerDataResponse.setMsg(msg);
        return innerDataResponse;
    }

    public static ResponseResult getErrorResult(String msg,Object data){
        ResponseResult innerDataResponse = new ResponseResult();
        innerDataResponse.setCode(400);
        innerDataResponse.setMsg(msg);
        innerDataResponse.setData(data);
        return innerDataResponse;
    }



}
