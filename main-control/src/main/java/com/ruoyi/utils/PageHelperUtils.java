package com.ruoyi.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.page.TableDataInfo;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * @author:lhb
 * @create: 2022-07-06 08:59
 * @Description: 分页工具类
 */

public class PageHelperUtils {

    /**
     * 响应请求分页数据(对list进行分页)
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static TableDataInfo getDataTableList(List<?> list, Integer pageNum, Integer pageSize)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("success");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        Page page = new Page(pageNum, pageSize);
        //为Page类中的total属性赋值
        int total = list.size();
        rspData.setTotal(total);
        //计算当前需要显⽰的数据下标起始值
        int startIndex = (pageNum - 1) *  pageSize;
        int endIndex = Math.min(startIndex + pageSize,total);
        //从链表中截取需要显⽰的⼦链表，并加⼊到Page
        page.addAll(list.subList(startIndex,endIndex));
        //以Page创建PageInfo
        PageInfo pageInfo = new PageInfo<>(page);
        rspData.setRows(pageInfo.getList());
        return rspData;
    }

    /**
     * 响应请求分页数据(特殊处理方式，)
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static TableDataInfo getDataTableListObj(List<?> list, Integer pageNum, Integer pageSize,Object obj)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("success");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        Page page = new Page(pageNum, pageSize);
        //为Page类中的total属性赋值
        int total = list.size();
        rspData.setTotal(total);
        //计算当前需要显⽰的数据下标起始值
        int startIndex = (pageNum - 1) *  pageSize;
        int endIndex = Math.min(startIndex + pageSize,total);
        //从链表中截取需要显⽰的⼦链表，并加⼊到Page
        page.addAll(list.subList(startIndex,endIndex));
        //以Page创建PageInfo
        PageInfo pageInfo = new PageInfo<>(page);
        rspData.setRows(pageInfo.getList());
        return rspData;
    }


}
