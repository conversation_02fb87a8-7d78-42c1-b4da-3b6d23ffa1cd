package com.ruoyi.utils;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据查询参数
 */
@Data
public class QueryParamVO implements Serializable {

    private String keyWord;
    private String keySubWord;
    private String keyThirdWord;
    private String keyFourWord;
    private Integer stateSub;
    private Integer state;
    /** 当前记录起始索引 */
    private Integer pageNum;

    /** 每页显示记录数 */
    private Integer pageSize;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String edate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String bdate;
}
