package com.ruoyi.service.erp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.domain.basicData.BasicMaterialInfo;
import com.ruoyi.domain.erp.ErpReportDetail;
import com.ruoyi.domain.erp.ErpReportMain;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.mapper.erp.ErpReportDetailMapper;
import com.ruoyi.mapper.erp.ErpReportMainMapper;
import com.ruoyi.service.basicData.BasicMaterialInfoService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.DocumentDetailVo;
import com.ruoyi.vo.document.DocumentInventoryDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ERP上报服务
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@Slf4j
@Service
public class ErpReportService extends ServiceImpl<ErpReportMainMapper, ErpReportMain> {

    @Resource
    private ErpReportMainMapper erpReportMainMapper;

    @Resource
    private ErpReportDetailMapper erpReportDetailMapper;

    @Resource
    private BasicDocumentInfoMapper basicDocumentInfoMapper;

    @Resource
    private BasicMaterialInfoService basicMaterialInfoService;

    /**
     * 上报当前出入库操作到ERP
     *
     * @param lists 本次操作的单据明细列表
     * @param type  操作类型（入库/出库）
     */
    public void reportCurrentOperation(List<DocumentDetailVo> lists, Integer type) {
        log.info("开始创建ERP上报记录，操作类型：{}，单据数量：{}", type, lists.size());

        for (DocumentDetailVo detail : lists) {
            try {
                // 获取单据信息
                BasicDocumentInfo documentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(detail.getDocumentCode());
                if (documentInfo == null) {
                    log.warn("未找到单据信息，单据编码：{}", detail.getDocumentCode());
                    continue;
                }

                // 创建上报记录
                createReportRecord(detail, documentInfo);

            } catch (Exception e) {
                log.error("创建ERP上报记录失败，单据编码：{}，错误：{}", detail.getDocumentCode(), e.getMessage(), e);
            }
        }
    }

    /**
     * 创建上报记录
     *
     * @param detail       单据明细
     * @param documentInfo 单据信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void createReportRecord(DocumentDetailVo detail, BasicDocumentInfo documentInfo) {
        // 1. 创建主表记录
        ErpReportMain mainRecord = createMainRecord(detail, documentInfo);

        // 2. 创建明细记录
        List<ErpReportDetail> detailRecords = createDetailRecords(detail, mainRecord.getId());

        // 3. 保存到数据库
        erpReportMainMapper.insert(mainRecord);
        if (!detailRecords.isEmpty()) {
            erpReportDetailMapper.batchInsert(detailRecords);
        }

        log.info("ERP上报记录创建成功，上报编号：{}，单据编码：{}，明细数量：{}",
                mainRecord.getReportNo(), detail.getDocumentCode(), detailRecords.size());

        // 4. 异步执行上报
        executeReportAsync(mainRecord.getId());
    }

    /**
     * 创建主表记录
     */
    private ErpReportMain createMainRecord(DocumentDetailVo detail, BasicDocumentInfo documentInfo) {
        ErpReportMain mainRecord = new ErpReportMain();
        mainRecord.setId(UUID.randomUUID().toString());
        mainRecord.setReportNo(generateReportNo());
        mainRecord.setDocumentCode(detail.getDocumentCode());
        mainRecord.setBusinessType(documentInfo.getBusinessType());
        mainRecord.setTransactionType(documentInfo.getTransactionType());
        mainRecord.setReportStatus(CommonConstant.ReportStatus.PENDING);

        // 计算总数量
        BigDecimal totalQuantity = BigDecimal.ZERO;
        if (detail.getDetails() != null) {
            for (DocumentInventoryDetailVo inventoryDetail : detail.getDetails()) {
                totalQuantity = totalQuantity.add(BigDecimal.valueOf(inventoryDetail.getQuantity()));
            }
        }
        mainRecord.setTotalQuantity(totalQuantity);

        mainRecord.setCreateTime(DateAndTimeUtil.getNowDate());
        mainRecord.setReporter(SecurityUtils.getUsername());

        return mainRecord;
    }

    /**
     * 创建明细记录
     */
    private List<ErpReportDetail> createDetailRecords(DocumentDetailVo detail, String mainId) {
        List<ErpReportDetail> detailRecords = new ArrayList<>();

        if (detail.getDetails() != null) {
            for (DocumentInventoryDetailVo inventoryDetail : detail.getDetails()) {
                ErpReportDetail detailRecord = new ErpReportDetail();
                detailRecord.setId(UUID.randomUUID().toString());
                detailRecord.setMainId(mainId);
                detailRecord.setMaterialCode(detail.getMaterialCode());
                detailRecord.setContainerCode(inventoryDetail.getContainerCode());
                detailRecord.setQuantity(BigDecimal.valueOf(inventoryDetail.getQuantity()));
                detailRecord.setCreateTime(DateAndTimeUtil.getNowDate());

                // 获取物料信息
                BasicMaterialInfo materialInfo = basicMaterialInfoService.queryBasicMaterialByCode(detail.getMaterialCode());
                if (materialInfo != null) {
                    detailRecord.setMaterialName(materialInfo.getMaterialName());
                    detailRecord.setUnit(materialInfo.getProduceUnit());
                }

                detailRecords.add(detailRecord);
            }
        }

        return detailRecords;
    }

    /**
     * 生成上报编号
     */
    private String generateReportNo() {
        return "ERP_REPORT_" + System.currentTimeMillis();
    }

    /**
     * 异步执行上报
     */
    @Async
    public void executeReportAsync(String mainId) {
        try {
            executeReport(mainId);
        } catch (Exception e) {
            log.error("异步执行ERP上报失败，主表ID：{}，错误：{}", mainId, e.getMessage(), e);
        }
    }

    /**
     * 执行具体上报逻辑
     */
    private void executeReport(String mainId) {
        ErpReportMain mainRecord = erpReportMainMapper.selectById(mainId);
        if (mainRecord == null) {
            log.warn("未找到上报记录，主表ID：{}", mainId);
            return;
        }

        List<ErpReportDetail> details = erpReportDetailMapper.selectList(
                new LambdaQueryWrapper<ErpReportDetail>()
                        .eq(ErpReportDetail::getMainId, mainId)
                        .orderByAsc(ErpReportDetail::getCreateTime)
        );

        try {
            log.info("开始执行ERP上报，上报编号：{}，业务类型：{}", mainRecord.getReportNo(), mainRecord.getBusinessType());
            // 根据业务类型调用不同的ERP接口
            String erpBillNo = callErpInterface(mainRecord, details);
            if (erpBillNo == null || erpBillNo.isEmpty()) {
                // 特殊处理调拨单的失败原因记录
                if (CommonConstant.BoundType.WLDB.equals(mainRecord.getBusinessType())) {
                    log.info("调拨单ERP上报记录为失败状态（原因：ERP接口未实现），上报编号：{}", mainRecord.getReportNo());
                } else {
                    log.warn("ERP上报失败（接口返回空），上报编号：{}", mainRecord.getReportNo());
                }
                updateReportFailed(mainId);
                return;
            }
            // 更新成功状态
            updateReportSuccess(mainId, erpBillNo);
            log.info("ERP上报成功，上报编号：{}，ERP单据编号：{}", mainRecord.getReportNo(), erpBillNo);
        } catch (Exception e) {
            // 更新失败状态
            updateReportFailed(mainId);
            log.error("ERP上报失败，上报编号：{}，错误：{}", mainRecord.getReportNo(), e.getMessage());
        }
    }

    /**
     * 调用ERP接口
     */
    private String callErpInterface(ErpReportMain mainRecord, List<ErpReportDetail> details) {
        // 调拨单特殊处理 - 调用ErpService专用方法
        if (CommonConstant.BoundType.WLDB.equals(mainRecord.getBusinessType())) {
            return callTransferBillInterface(mainRecord, details);
        }
        
        // TODO: 根据业务类型调用不同的ERP接口
        // 其他业务类型暂时返回模拟的ERP单据编号
        log.info("调用ERP接口（暂未实现），业务类型：{}，明细数量：{}", mainRecord.getBusinessType(), details.size());
        return "ERP_BILL_" + System.currentTimeMillis();
    }
    
    /**
     * 调拨单专用ERP接口调用
     */
    private String callTransferBillInterface(ErpReportMain mainRecord, List<ErpReportDetail> details) {
        try {
            log.info("开始调用ErpService处理调拨单上报：单据编码：{}，明细数量：{}", 
                    mainRecord.getDocumentCode(), details.size());
            
            // 注入ErpService
            ErpService erpService = SpringUtils.getBean(ErpService.class);
            
            // 调用ErpService的调拨单上报方法
            String erpBillNo = erpService.reportTransferBill(mainRecord, details);
            
            if (erpBillNo != null && !erpBillNo.isEmpty()) {
                log.info("调拨单ERP上报成功：单据编码：{}，ERP单据编号：{}", mainRecord.getDocumentCode(), erpBillNo);
                return erpBillNo;
            } else {
                log.info("调拨单ERP上报（当前ERP未接入，记录为失败状态）：单据编码：{}", mainRecord.getDocumentCode());
                return null;
            }
            
        } catch (Exception e) {
            log.error("调拨单ERP上报调用ErpService失败：单据编码：{}，错误：{}", mainRecord.getDocumentCode(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新上报状态（通用方法）
     *
     * @param mainId       主表ID
     * @param reportStatus 上报状态
     * @param erpBillNo    ERP单据编号（可为null）
     */
    private void updateReportStatus(String mainId, Integer reportStatus, String erpBillNo) {
        LambdaUpdateWrapper<ErpReportMain> updateWrapper = new LambdaUpdateWrapper<ErpReportMain>()
                .eq(ErpReportMain::getId, mainId)
                .set(ErpReportMain::getReportStatus, reportStatus)
                .set(ErpReportMain::getReportTime, new Date());

        if (erpBillNo != null && !erpBillNo.isEmpty()) {
            updateWrapper.set(ErpReportMain::getErpBillNo, erpBillNo);
        }

        this.update(updateWrapper);
    }

    /**
     * 更新上报成功状态
     */
    private void updateReportSuccess(String mainId, String erpBillNo) {
        updateReportStatus(mainId, CommonConstant.ReportStatus.SUCCESS, erpBillNo);
    }

    /**
     * 更新上报失败状态
     */
    private void updateReportFailed(String mainId) {
        updateReportStatus(mainId, CommonConstant.ReportStatus.FAILED, null);
    }


    /**
     * 根据单据编码查询上报记录
     */
    public List<ErpReportMain> getReportsByDocumentCode(String documentCode) {
        return this.list(new LambdaQueryWrapper<ErpReportMain>()
                .eq(ErpReportMain::getDocumentCode, documentCode)
                .orderByDesc(ErpReportMain::getCreateTime)
        );
    }


    /**
     * 根据主表ID查询明细列表
     */
    public List<ErpReportDetail> getDetailsByMainId(String mainId) {
        return erpReportDetailMapper.selectList(
                new LambdaQueryWrapper<ErpReportDetail>()
                        .eq(ErpReportDetail::getMainId, mainId)
                        .orderByAsc(ErpReportDetail::getCreateTime)
        );
    }


}
