package com.ruoyi.service.common;

import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.domain.sys.SysFileInfo;
import com.ruoyi.service.sys.SysFileInfoService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.webRequest.FileInfoRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [文件处理服务]
 * @date 2024/5/9 16:47
 */
@Service
public class FileDealService {

    @Resource
    private SysFileInfoService sysFileInfoService;


    public void addFileInfo(FileInfoRequest fileInfoRequest) {
        SysFileInfo sysFileInfo = sysFileInfoService.queryFileInfo(fileInfoRequest.getFileId());
        if(sysFileInfo == null){
            sysFileInfo = new SysFileInfo();
            BeanUtils.copyProperties(fileInfoRequest, sysFileInfo);
            sysFileInfo.setRecordTime(DateAndTimeUtil.getDetailTime());
            sysFileInfoService.addFileInfo(sysFileInfo);
        }else{
            BeanUtils.copyProperties(fileInfoRequest, sysFileInfo);
            sysFileInfo.setRecordTime(DateAndTimeUtil.getDetailTime());
            sysFileInfoService.updataByFileId(sysFileInfo);
        }
    }

    public void deleteFileInfoById(List<String> ids) {
        for (String id: ids){
            SysFileInfo sysFileInfo = this.sysFileInfoService.queryFileInfo(id);
            String path = sysFileInfo.getFilePath() + File.separator+sysFileInfo.getFileName();
            FileUtils.deleteFile(path);
            this.sysFileInfoService.deleteFileInfoById(id);
        }
    }
}
