package com.ruoyi.service.common;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.controller.common.FileController;
import com.ruoyi.service.sys.ProjectSysConfigService;
import com.ruoyi.utils.CommonUtils;
import com.ruoyi.utils.constant.CommonConstant;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.io.InputStream;

/**
 * @Author: lhb
 * @CreateDate: 2025/3/12 16:20
 * @Description: 类描述
 */
@Service
public class CommonFileService {
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Resource
    private ProjectSysConfigService projectSysConfigService;

    /**
     * 上传文件到ftp服务器
     */
    @SneakyThrows
    public boolean uploadFile(String filePath, String fileName, InputStream input){
        String userName = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_NAME);
        String passWord = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PWD);
        String host = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_IP);
        String port = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PORT);
        if (StringUtils.isEmpty(host) || StringUtils.isEmpty(userName) || StringUtils.isEmpty(port)) {
            logger.info("FTP服务器地址为空、账号、端口不能为空！ ip:" + host + ",账号:" + userName + ",端口：" + port );
            return false;
        }
        fileName = new String(fileName.getBytes("UTF-8"), "iso-8859-1");
        return CommonUtils.uploadFile(host, Integer.parseInt(port),userName,passWord,filePath,fileName,input);
    }


    /**
     * 获取fpt中的文件流
     * 示例:2025-03-12/22.png
     */
    public InputStream getFileStreamFromFTP(String fileName) {
        String userName = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_NAME);
        String passWord = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PWD);
        String host = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_IP);
        String port = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PORT);
        if (StringUtils.isEmpty(host) || StringUtils.isEmpty(userName) || StringUtils.isEmpty(port)) {
            logger.info("FTP服务器地址为空、账号、端口不能为空！ ip:" + host + ",账号:" + userName + ",端口：" + port );
            return null;
        }
        return CommonUtils.getFileStreamFromFTP(fileName,userName,passWord,host, Integer.valueOf(port));
    }

    /**
     * 判断ftp指定目录下文件是否存在
     */
    public boolean isFtpFileExist(String filePath) {
        String userName = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_NAME);
        String passWord = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PWD);
        String host = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_IP);
        String port = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PORT);
        if (StringUtils.isEmpty(host) || StringUtils.isEmpty(userName) || StringUtils.isEmpty(port)) {
            logger.info("FTP服务器地址为空、账号、端口不能为空！ ip:" + host + ",账号:" + userName + ",端口：" + port);
            return false;
        }
        return CommonUtils.isFileExist(host, Integer.parseInt(port), userName, passWord, filePath);
    }

    /**
     * 从ftp下载文件
     */
    public boolean downFtpFile(String filePath, String fileName, String localPath) {
        String userName = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_NAME);
        String passWord = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PWD);
        String host = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_IP);
        String port = projectSysConfigService.getSysConfigValByType(CommonConstant.SysConfig.SHARE_FILE_DICT_TYPE_PORT);
        if (StringUtils.isEmpty(host) || StringUtils.isEmpty(userName) || StringUtils.isEmpty(port)) {
            logger.info("FTP服务器地址为空、账号、端口不能为空！ ip:" + host + ",账号:" + userName + ",端口：" + port);
            return false;
        }
        return CommonUtils.downFile(host, Integer.parseInt(port), userName, passWord, filePath, fileName, localPath);
    }

}
