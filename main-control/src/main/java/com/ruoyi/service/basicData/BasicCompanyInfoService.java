package com.ruoyi.service.basicData;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.basicData.BasicCompanyGroup;
import com.ruoyi.domain.basicData.BasicCompanyInfo;
import com.ruoyi.mapper.basicData.BasicCompanyInfoMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.erp.common.ErpQueryReq;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class BasicCompanyInfoService extends ServiceImpl<BasicCompanyInfoMapper, BasicCompanyInfo> {

    @Resource
    private BasicCompanyInfoMapper basicCompanyInfoMapper;
    @Resource
    private ErpService erpService;

    @Resource
    private BasicCompanyGroupService basicCompanyGroupService;


    /**
     * 分页查询
     */
    public List<BasicCompanyInfo> queryBasicCustomerInfo(QueryParamVO param) {
        QueryWrapper<BasicCompanyInfo> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotEmpty(param.getKeyWord())){
            queryWrapper.eq("company_type",param.getKeyWord());
        }
        if(StringUtils.isNotEmpty(param.getKeySubWord())){
            queryWrapper.and(i -> i.like("company_code", param.getKeySubWord())
                    .or().like("company_name", param.getKeySubWord()));
        }
        if(StringUtils.isNotEmpty(param.getBdate())){
            queryWrapper.ge("create_time",param.getBdate());
        }
        if(StringUtils.isNotEmpty(param.getEdate())){
            queryWrapper.le("create_time",param.getEdate());
        }
        queryWrapper.orderByDesc("create_time");
        List<BasicCompanyInfo> companyList = this.basicCompanyInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(companyList)) {
            return companyList;
        }
        // 提取groupCodes
        List<String> groupCodes = companyList.stream() .map(BasicCompanyInfo::getGroupCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupCodes)) {
            return companyList;
        }
        List<BasicCompanyGroup> groups = basicCompanyGroupService.list(new QueryWrapper<BasicCompanyGroup>().in("group_code", groupCodes)   );
        if (CollectionUtils.isEmpty(groups)) {
            return companyList;
        }
        Map<String, String> groupNameMap = groups.stream()
                .collect(Collectors.toMap(BasicCompanyGroup::getGroupCode, BasicCompanyGroup::getGroupName, (o1, o2) -> o1));
        // 设置groupName
        companyList.forEach(company -> {
            if (StringUtils.isNotEmpty(company.getGroupCode())) {
                company.setGroupName(groupNameMap.get(company.getGroupCode()));
            }
        });
        return companyList;
    }

    /**
     * 从ERP同步客户
     */
    public ResponseResult syncFromErpCustomers() throws Exception {
        return syncCompanies(CommonConstant.ErpFormId.CUSTOMER,
                CommonConstant.ErpFieldKeys.Customer.ID,
                CommonConstant.ErpFieldKeys.Customer.NAME,
                CommonConstant.ErpFieldKeys.Customer.CODE,
                CommonConstant.ErpFieldKeys.Customer.DESCRIPTION,
                CommonConstant.CompanyGroupType.CUSTOMER,
                "客户");
    }

    /**
     * 从ERP同步供应商
     */
    public ResponseResult syncFromErpSuppliers() throws Exception {
        return syncCompanies(CommonConstant.ErpFormId.SUPPLIER,
                CommonConstant.ErpFieldKeys.Supplier.ID,
                CommonConstant.ErpFieldKeys.Supplier.NAME,
                CommonConstant.ErpFieldKeys.Supplier.CODE,
                CommonConstant.ErpFieldKeys.Supplier.DESCRIPTION,
                CommonConstant.CompanyGroupType.SUPPLIER,
                "供应商");
    }

    /**
     * 通用同步方法
     *
     * @param formId      ERP表单ID
     * @param idField     ID字段名
     * @param nameField   名称字段名
     * @param codeField   编码字段名
     * @param descField   描述字段名
     * @param companyType 类型 (0:客户, 1:供应商)
     * @param contextName 日志上下文名称
     * @return 同步结果
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncCompanies(String formId, String idField, String nameField, String codeField, String descField, int companyType, String contextName) throws Exception {
        log.info("开始同步ERP[{}]...", contextName);

        // 0. 首先确保分组信息是最新的
        if (companyType == 0) { // 客户
            basicCompanyGroupService.syncFromErpCustomerGroups();
        } else { // 供应商
            basicCompanyGroupService.syncFromErpSupplierGroups();
        }

        // 1. 构建ERP查询请求
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(formId);
        // 注意：将分组字段加入查询
        queryReq.setFieldKeys(String.join(",", idField, nameField, codeField, descField, CommonConstant.ErpFieldKeys.Customer.GROUP));
        queryReq.setLimit(0);

        // 2. 从ERP获取数据
        List<Map<String, Object>> erpDataList = erpService.getErpDataList(queryReq, contextName);
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到[{}]，或查询失败。", contextName);
            return ResponseResult.getSuccessResult("ERP中未查询到" + contextName, null);
        }
        log.info("从ERP查询到 {} 条[{}]，准备进行同步...", erpDataList.size(), contextName);

        // 3. 获取所有涉及的分组信息，并创建ID到Code的映射
        List<String> groupErpIds = erpDataList.stream()
                .map(data -> data.get(CommonConstant.ErpFieldKeys.Customer.GROUP))
                .filter(Objects::nonNull)
                .map(idObj -> {
                    if (idObj instanceof Number) {
                        return String.valueOf(((Number) idObj).longValue());
                    }
                    return String.valueOf(idObj);
                })
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> groupCodeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(groupErpIds)) {
            // 注意：BasicCompanyGroup的ID字段是从ERP同步过来的，所以可以直接用erp的group id查询
            List<BasicCompanyGroup> groups = basicCompanyGroupService.listByIds(groupErpIds);
            if (!CollectionUtils.isEmpty(groups)) {
                // 使用 (k1, k2) -> k1 作为合并函数，以防万一有重复的ID
                groupCodeMap = groups.stream()
                        .collect(Collectors.toMap(BasicCompanyGroup::getId, BasicCompanyGroup::getGroupCode, (k1, k2) -> k1));
            }
        }

        // 4. 数据转换和处理
        List<BasicCompanyInfo> erpCompanyList = new ArrayList<>();
        for (Map<String, Object> dataMap : erpDataList) {
            BasicCompanyInfo company = new BasicCompanyInfo();
            Object erpIdObj = dataMap.get(idField);
            if(erpIdObj instanceof Number) {
                company.setId(String.valueOf(((Number) erpIdObj).longValue()));
            } else {
                company.setId(String.valueOf(erpIdObj));
            }
            company.setCompanyCode((String) dataMap.get(codeField));
            company.setCompanyName((String) dataMap.get(nameField));
            String remark = (String) dataMap.get(descField);
            if (remark != null && remark.length() > 255) {
                remark = remark.substring(0, 255);
            }
            company.setRemark(remark);
//            company.setCompanyAddress();
            company.setCompanyType(companyType);
            company.setCreateTime(new Date());

            // 根据分组ID设置分组Code
            Object groupObj = dataMap.get(CommonConstant.ErpFieldKeys.Customer.GROUP);
            if (groupObj != null) {
                String groupErpId;
                if (groupObj instanceof Number) {
                    groupErpId = String.valueOf(((Number) groupObj).longValue());
                } else {
                    groupErpId = String.valueOf(groupObj);
                }
                String groupCode = groupCodeMap.get(groupErpId);
                company.setGroupCode(groupCode);
                if (groupCode == null) {
                    log.warn("未能在本地找到ERP分组ID[{}]对应的分组信息，公司[{}]的分组代码将设置为空。", groupErpId, company.getCompanyCode());
                }
            }

            erpCompanyList.add(company);
        }

        // 5. 与本地数据比对，区分新增和更新
        List<String> erpIds = erpCompanyList.stream().map(BasicCompanyInfo::getId).collect(Collectors.toList());
        List<BasicCompanyInfo> existingCompanies = this.listByIds(erpIds);
        Map<String, BasicCompanyInfo> existingCompanyMap = existingCompanies.stream()
                .collect(Collectors.toMap(BasicCompanyInfo::getId, company -> company));

        // 比较并准备待更新/新增列表
        List<BasicCompanyInfo> toAddList = new ArrayList<>();
        List<BasicCompanyInfo> toUpdateList = new ArrayList<>();

        for (BasicCompanyInfo erpCompany : erpCompanyList) {
            BasicCompanyInfo localCompany = existingCompanyMap.get(erpCompany.getId());
            if (localCompany == null) {
                toAddList.add(erpCompany);
            } else {
                // 更新字段
                boolean needsUpdate = false;
                if (!Objects.equals(erpCompany.getCompanyCode(), localCompany.getCompanyCode())) {
                    localCompany.setCompanyCode(erpCompany.getCompanyCode());
                    needsUpdate = true;
                }
                if (!Objects.equals(erpCompany.getCompanyName(), localCompany.getCompanyName())) {
                    localCompany.setCompanyName(erpCompany.getCompanyName());
                    needsUpdate = true;
                }
                // 如果分组编码或分组名称不一致，则更新
                if (!Objects.equals(erpCompany.getGroupCode(), localCompany.getGroupCode())) {
                    localCompany.setGroupCode(erpCompany.getGroupCode());
                    needsUpdate = true;
                }

                if (needsUpdate) {
                    toUpdateList.add(localCompany);
                }
            }
        }

        // 5. 批量执行数据库操作
        if (!CollectionUtils.isEmpty(toAddList)) {
            this.saveBatch(toAddList);
            log.info("成功新增 {} 条[{}]。", toAddList.size(), contextName);
        }
        if (!CollectionUtils.isEmpty(toUpdateList)) {
            this.updateBatchById(toUpdateList);
            log.info("成功更新 {} 条[{}]。", toUpdateList.size(), contextName);
        }

        log.info("ERP[{}]同步完成。", contextName);

        return ResponseResult.getSuccessResult(String.format("同步%s完成，新增 %d 条，更新 %d 条。", contextName, toAddList.size(), toUpdateList.size()), "");
    }
}
