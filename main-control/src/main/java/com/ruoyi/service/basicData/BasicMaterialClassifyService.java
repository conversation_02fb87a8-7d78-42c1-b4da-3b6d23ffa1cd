package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.domain.basicData.BasicMaterialClassify;
import com.ruoyi.domain.basicData.MaterialClassifyTypeMapping;
import com.ruoyi.mapper.basicData.BasicMaterialClassifyMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.basicData.BasicMaterialClassifyWithTypeNameVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import com.ruoyi.vo.erp.dto.ErpMaterialGroupVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: lhb
 * @CreateDate: 2025/3/17 11:48
 * @Description: 物料分类
 */
@Service
public class BasicMaterialClassifyService extends ServiceImpl<BasicMaterialClassifyMapper, BasicMaterialClassify> {

    private static final Logger logger = LoggerFactory.getLogger(BasicMaterialClassifyService.class);
    @Resource
    private BasicMaterialClassifyMapper basicMaterialClassifyMapper;

    @Resource
    private ErpService erpService;

    @Resource
    private MaterialClassifyTypeMappingService materialClassifyTypeMappingService;


    /**
     * 从ERP同步物料分组信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErpGroups() throws Exception {
        // 1. 构建查询请求
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.MATERIAL_GROUP);
        queryReq.setFieldKeys(String.join(",",
                CommonConstant.ErpFieldKeys.MaterialGroup.ID,
                CommonConstant.ErpFieldKeys.MaterialGroup.CODE,
                CommonConstant.ErpFieldKeys.MaterialGroup.PARENT_ID,
                CommonConstant.ErpFieldKeys.MaterialGroup.NAME));
        queryReq.setFilterString("");
        queryReq.setOrderString("");
        queryReq.setTopRowCount(0);
        queryReq.setStartRow(0);
        queryReq.setLimit(0);

        // 2. 调用ERP服务获取物料分组列表
        List<Map<String, Object>> erpGroupsAsMaps = erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_MATERIAL_GROUP);

        // 3. 将Map列表转换为VO列表
        ObjectMapper objectMapper = new ObjectMapper();
        List<ErpMaterialGroupVo> erpGroups = erpGroupsAsMaps.stream()
                .map(map -> objectMapper.convertValue(map, ErpMaterialGroupVo.class))
                .collect(Collectors.toList());

        logger.info("[物料分类同步] 开始执行，接收到 {} 条ERP源数据。", erpGroups.size());

        if (CollectionUtils.isEmpty(erpGroups)) {
            logger.info("ERP物料分组信息为空，无需同步。");
            return ResponseResult.getSuccessResult("ERP物料分组信息为空，无需同步。", null);
        }

        // 4. 数据预处理：去重，使用FID作为唯一标识
        List<ErpMaterialGroupVo> distinctErpGroupList = erpGroups.stream()
                .filter(item -> item.getFid() != null)
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(ErpMaterialGroupVo::getFid, Function.identity(), (v1, v2) -> v1),
                        map -> new ArrayList<>(map.values())
                ));
        logger.info("[物料分类同步] 去重后，待处理的物料分类数量: {}", distinctErpGroupList.size());

        if (distinctErpGroupList.isEmpty()) {
            logger.info("[物料分类同步] 无物料分类信息需要同步。");
            return ResponseResult.getSuccessResult("无物料分类信息需要同步。", null);
        }

        // 5. 动态获取类型映射关系
        Map<String, String> tempStringTypeMap = materialClassifyTypeMappingService.getMaterialTypeMapping();
        final Map<String, String> nameToStringTypeMap;
        // 直接使用从数据库获取的映射关系，如果为空则在使用时自动添加
        nameToStringTypeMap = tempStringTypeMap;

        logger.info("使用物料分类类型映射配置，共{}条规则", nameToStringTypeMap.size());
        Map<Long, ErpMaterialGroupVo> fidToObjectMap = distinctErpGroupList.stream()
                .collect(Collectors.toMap(ErpMaterialGroupVo::getFid, Function.identity()));
        Map<Long, String> fidToTypeCache = new ConcurrentHashMap<>();

        // 6. 批量查询本地已存在的分类
        List<String> erpClassifyIds = distinctErpGroupList.stream()
                .map(groupVo -> String.valueOf(groupVo.getFid()))
                .collect(Collectors.toList());
        Map<String, BasicMaterialClassify> existingClassifyMap = this.list(new LambdaQueryWrapper<BasicMaterialClassify>()
                        .in(BasicMaterialClassify::getId, erpClassifyIds))
                .stream()
                .collect(Collectors.toMap(BasicMaterialClassify::getId, Function.identity()));
        logger.info("根据{}个ERP编码，从本地数据库查询到{}条已存在的物料分类。", erpClassifyIds.size(), existingClassifyMap.size());

        // 7. 分类为新增和更新列表
        List<BasicMaterialClassify> toAddList = new ArrayList<>();
        List<BasicMaterialClassify> toUpdateList = new ArrayList<>();

        // 8. 对比数据，准备新增和更新列表
        distinctErpGroupList.forEach(groupVo -> {
            try {
                if (groupVo.getFid() == null) {
                    logger.warn("[物料分类同步] 跳过FID为null的物料分组，名称：{}", groupVo.getName());
                    return;
                }

                String parentIdStr = (groupVo.getParentId() != null && groupVo.getParentId() != 0) ? String.valueOf(groupVo.getParentId()) : "0";
                String currentIdStr = String.valueOf(groupVo.getFid());
                String type = determineType(groupVo, fidToObjectMap, nameToStringTypeMap, fidToTypeCache);

                BasicMaterialClassify existingClassify = existingClassifyMap.get(currentIdStr);
                if (existingClassify != null) {
                    // 存在，准备更新
                    existingClassify.setClassifyName(groupVo.getName());
                    existingClassify.setParentId(parentIdStr);
                    existingClassify.setClassifyCode(groupVo.getNumber());
                    existingClassify.setType(type);
                    toUpdateList.add(existingClassify);
                } else {
                    // 不存在，准备新增
                    BasicMaterialClassify classify = new BasicMaterialClassify();
                    classify.setId(currentIdStr);
                    classify.setParentId(parentIdStr);
                    classify.setClassifyCode(groupVo.getNumber());
                    classify.setClassifyName(groupVo.getName());
                    classify.setType(type);
                    classify.setCreateTime(new Date());
                    toAddList.add(classify);
                }
            } catch (Exception e) {
                logger.error("[物料分类同步] 处理物料分组时发生异常，FID：{}，名称：{}，错误：{}",
                           groupVo.getFid(), groupVo.getName(), e.getMessage(), e);
            }
        });

        // 9. 批量执行数据库操作
        if (!toAddList.isEmpty()) {
            this.saveBatch(toAddList);
            logger.info("成功新增 {} 条物料分类。", toAddList.size());
        }
        if (!toUpdateList.isEmpty()) {
            this.updateBatchById(toUpdateList);
            logger.info("成功更新 {} 条物料分类。", toUpdateList.size());
        }
        logger.info("[物料分类同步] 执行完毕。");
        return ResponseResult.getSuccessResult(String.format("同步物料分类完成，新增 %d 条，更新 %d 条。", toAddList.size(), toUpdateList.size()), "");
    }

    /**
     * 递归确定物料类型
     */
    private String determineType(ErpMaterialGroupVo currentGroup, Map<Long, ErpMaterialGroupVo> fidToObjectMap, Map<String, String> nameToStringTypeMap, Map<Long, String> cache) {
        if (currentGroup == null) {
            logger.warn("[物料分类同步] 传入的物料分组对象为null，返回默认类型0");
            return "0"; // 默认类型
        }
        Long currentFid = currentGroup.getFid();
        if (currentFid == null) {
            logger.warn("[物料分类同步] 物料分组FID为null，物料名称：{}，返回默认类型0", currentGroup.getName());
            return "0";
        }
        if (cache.containsKey(currentFid)) {
            return cache.get(currentFid);
        }

        Long parentId = currentGroup.getParentId();
        // 检查是否为根节点
        if (parentId == null || parentId == 0) {
            String materialName = currentGroup.getName();
            String typeValue = nameToStringTypeMap.get(materialName);
            if (typeValue != null) {
                cache.put(currentFid, typeValue);
                return typeValue;
            } else if (materialName != null) {
                // 未找到映射配置，尝试自动添加到数据库
                String autoTypeValue = autoAddMaterialTypeMapping(materialName);
                logger.info("[物料分类同步] 未找到物料名称 '{}' 的类型映射配置，已自动添加映射关系：{} -> {}",
                           materialName, materialName, autoTypeValue);
                nameToStringTypeMap.put(materialName, autoTypeValue);
                cache.put(currentFid, autoTypeValue);
                return autoTypeValue;
            }
            cache.put(currentFid, "0");
            return "0";
        }

        // 非根节点，递归查找父节点类型
        ErpMaterialGroupVo parentGroup = fidToObjectMap.get(parentId);
        if (parentGroup == null) {
            logger.warn("[物料分类同步] 未找到父节点信息，parentId：{}，物料名称：{}，使用默认类型0", parentId, currentGroup.getName());
            cache.put(currentFid, "0");
            return "0";
        }

        String type = determineType(parentGroup, fidToObjectMap, nameToStringTypeMap, cache);
        cache.put(currentFid, type);
        return type;
    }

    /**
     * 新增物料分类
     */
    public ResponseResult insertBasicMaterialClassify(BasicMaterialClassify basicCustomerInfo){
        basicCustomerInfo.setId(LocalStringUtils.getDataUUID());
        basicCustomerInfo.setCreateTime(DateAndTimeUtil.getNowDate());
        int result = this.basicMaterialClassifyMapper.insert(basicCustomerInfo);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 删除物料分类
     */
    public ResponseResult deleteBasicMaterialClassify(BatchIdsReq req) {
        List<String> ids = req.getIds();
        for (String idTemp : ids){
            this.deleteCategoryRecursively(idTemp);
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 递归删除
     * @param idTemp 父类数据主键
     */
    public void deleteCategoryRecursively(String idTemp) {
        // 1. 查询所有子节点
        List<BasicMaterialClassify> children = list(new LambdaQueryWrapper<BasicMaterialClassify>()
                .eq(BasicMaterialClassify::getParentId, idTemp));
        // 2. 递归删除子节点
        for (BasicMaterialClassify child : children) {
            deleteCategoryRecursively(child.getId());
        }
        // 3. 删除当前节点
        removeById(idTemp);
    }

    /**
     * 更新物料分类
     */
    public ResponseResult uptBasicMaterialClassify(BasicMaterialClassify basicCustomerInfo) {
        int count = this.basicMaterialClassifyMapper.updateById(basicCustomerInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询（包含类型中文名称）
     */
    public List<BasicMaterialClassifyWithTypeNameVo> queryBasicMaterialClassify(QueryParamVO queryParamVO) {
        return basicMaterialClassifyMapper.queryMaterialClassifyWithTypeName(queryParamVO);
    }

    /**
     * 自动添加物料类型映射关系到数据库
     * @param materialName 物料名称
     * @return 分配的类型值
     */
    private String autoAddMaterialTypeMapping(String materialName) {
        try {
            // 1. 根据名称特征推断类型值
            String typeValue = getUnmatchedMaterialDefaultType(materialName);

            // 2. 创建新的映射配置
            MaterialClassifyTypeMapping newMapping = new MaterialClassifyTypeMapping();
            newMapping.setId(LocalStringUtils.getDataUUID());
            newMapping.setMaterialName(materialName);
            newMapping.setTypeValue(typeValue);
            newMapping.setIsActive(1);
            newMapping.setRemark("系统自动添加 - " + DateAndTimeUtil.getNowTimeNoSeparator());
            newMapping.setCreateTime(DateAndTimeUtil.getNowDate());

            // 3. 保存到数据库
            ResponseResult result = materialClassifyTypeMappingService.insertMapping(newMapping);
            if (result.getCode() == 200) {
                logger.info("[物料分类同步] 成功自动添加物料类型映射：{} -> {}", materialName, typeValue);
                return typeValue;
            } else {
                logger.error("[物料分类同步] 自动添加物料类型映射失败：{}, 错误：{}", materialName, result.getMsg());
                return "999"; // 未映射类型
            }

        } catch (Exception e) {
            logger.error("[物料分类同步] 自动添加物料类型映射时发生异常，物料名称：{}", materialName, e);
            return "999"; // 未映射类型
        }
    }

    /**
     * 获取未匹配物料的默认类型值（自增）
     * @param materialName 物料名称
     * @return 自增的类型值
     */
    private String getUnmatchedMaterialDefaultType(String materialName) {
        try {
            // 获取当前最大的类型值
            String maxTypeValue = materialClassifyTypeMappingService.getMaxTypeValue();
            int nextTypeValue;

            if (maxTypeValue == null || maxTypeValue.trim().isEmpty()) {
                // 如果没有任何配置，从1开始
                nextTypeValue = 1;
            } else {
                try {
                    // 尝试将最大值转换为数字并+1
                    int maxValue = Integer.parseInt(maxTypeValue);
                    nextTypeValue = maxValue + 1;
                } catch (NumberFormatException e) {
                    // 如果最大值不是数字（如包含字母），则从1开始
                    logger.warn("[物料分类同步] 最大类型值 '{}' 不是数字格式，从1开始自增", maxTypeValue);
                    nextTypeValue = 1;
                }
            }

            String newTypeValue = String.valueOf(nextTypeValue);
            logger.info("[物料分类同步] 物料 '{}' 未找到映射配置，分配新的类型值：{}", materialName, newTypeValue);
            return newTypeValue;

        } catch (Exception e) {
            logger.error("[物料分类同步] 获取自增类型值时发生异常，使用默认值999", e);
            return "999"; // 异常时的备用值
        }
    }
}
