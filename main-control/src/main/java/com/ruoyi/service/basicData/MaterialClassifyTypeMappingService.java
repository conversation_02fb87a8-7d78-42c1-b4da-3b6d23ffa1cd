package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.domain.basicData.MaterialClassifyTypeMapping;
import com.ruoyi.mapper.basicData.MaterialClassifyTypeMappingMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @CreateDate: 2025/07/10
 * @Description: 物料分类类型映射服务
 */
@Service
public class MaterialClassifyTypeMappingService extends ServiceImpl<MaterialClassifyTypeMappingMapper, MaterialClassifyTypeMapping> {

    private static final Logger logger = LoggerFactory.getLogger(MaterialClassifyTypeMappingService.class);

    @Resource
    private MaterialClassifyTypeMappingMapper materialClassifyTypeMappingMapper;

    /**
     * 获取物料分类类型映射关系
     * @return 物料名称到类型值的映射Map
     */
    public Map<String, String> getMaterialTypeMapping() {
        try {
            List<MaterialClassifyTypeMapping> mappingList = this.list(
                new LambdaQueryWrapper<MaterialClassifyTypeMapping>()
                    .eq(MaterialClassifyTypeMapping::getIsActive, 1)
                    .orderByAsc(MaterialClassifyTypeMapping::getMaterialName)
            );

            if (CollectionUtils.isEmpty(mappingList)) {
                logger.warn("未找到有效的物料分类类型映射配置，将返回空映射");
                return new HashMap<>();
            }

            Map<String, String> typeMapping = new HashMap<>();
            for (MaterialClassifyTypeMapping mapping : mappingList) {
                if (mapping.getMaterialName() == null || mapping.getMaterialName().trim().isEmpty()) {
                    logger.warn("跳过物料名称为空的映射配置，ID: {}", mapping.getId());
                    continue;
                }
                if (mapping.getTypeValue() == null || mapping.getTypeValue().trim().isEmpty()) {
                    logger.warn("跳过类型值为空的映射配置，物料名称: {}, ID: {}", mapping.getMaterialName(), mapping.getId());
                    continue;
                }

                String materialName = mapping.getMaterialName().trim();
                if (typeMapping.containsKey(materialName)) {
                    logger.warn("发现重复的物料名称映射: '{}', 使用第一个配置值: {}, 跳过ID: {}",
                               materialName, typeMapping.get(materialName), mapping.getId());
                } else {
                    typeMapping.put(materialName, mapping.getTypeValue().trim());
                }
            }

            logger.info("成功加载物料分类类型映射配置，共{}条记录", typeMapping.size());
            return typeMapping;

        } catch (Exception e) {
            logger.error("获取物料分类类型映射失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 新增物料分类类型映射
     */
    public ResponseResult insertMapping(MaterialClassifyTypeMapping mapping) {
        try {
            // 检查是否存在相同的物料名称
            long count = this.count(new LambdaQueryWrapper<MaterialClassifyTypeMapping>()
                .eq(MaterialClassifyTypeMapping::getMaterialName, mapping.getMaterialName())
                .eq(MaterialClassifyTypeMapping::getIsActive, 1));
            
            if (count > 0) {
                return ResponseResult.getErrorResult("物料名称已存在，请勿重复添加");
            }
            
            mapping.setId(LocalStringUtils.getDataUUID());
            mapping.setCreateTime(DateAndTimeUtil.getNowDate());
            mapping.setIsActive(1); // 默认有效
            
            int result = this.materialClassifyTypeMappingMapper.insert(mapping);
            if (result >= 1) {
                return ResponseResult.getSuccessResult("新增成功", null);
            }
            return ResponseResult.getErrorResult("新增失败，请重试");
            
        } catch (Exception e) {
            logger.error("新增物料分类类型映射失败", e);
            return ResponseResult.getErrorResult("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新物料分类类型映射
     */
    public ResponseResult updateMapping(MaterialClassifyTypeMapping mapping) {
        try {
            // 检查是否存在相同的物料名称（排除自己）
            long count = this.count(new LambdaQueryWrapper<MaterialClassifyTypeMapping>()
                .eq(MaterialClassifyTypeMapping::getMaterialName, mapping.getMaterialName())
                .eq(MaterialClassifyTypeMapping::getIsActive, 1)
                .ne(MaterialClassifyTypeMapping::getId, mapping.getId()));
            
            if (count > 0) {
                return ResponseResult.getErrorResult("物料名称已存在，请勿重复");
            }
            
            int result = this.materialClassifyTypeMappingMapper.updateById(mapping);
            if (result >= 1) {
                return ResponseResult.getSuccessResult("更新成功", null);
            }
            return ResponseResult.getErrorResult("更新失败，请重试");
            
        } catch (Exception e) {
            logger.error("更新物料分类类型映射失败", e);
            return ResponseResult.getErrorResult("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除物料分类类型映射
     */
    public ResponseResult deleteMapping(BatchIdsReq req) {
        try {
            List<String> ids = req.getIds();
            int count = this.materialClassifyTypeMappingMapper.deleteBatchIds(ids);
            if (count >= ids.size()) {
                return ResponseResult.getSuccessResult("删除成功", null);
            }
            return ResponseResult.getErrorResult("部分数据删除失败，请重试");
            
        } catch (Exception e) {
            logger.error("删除物料分类类型映射失败", e);
            return ResponseResult.getErrorResult("删除失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询物料分类类型映射
     */
    public List<MaterialClassifyTypeMapping> queryMappingList(QueryParamVO queryParamVO) {
        LambdaQueryWrapper<MaterialClassifyTypeMapping> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据物料名称模糊查询
        if (queryParamVO.getKeyWord() != null && !queryParamVO.getKeyWord().trim().isEmpty()) {
            queryWrapper.like(MaterialClassifyTypeMapping::getMaterialName, queryParamVO.getKeyWord().trim());
        }
        
        // 根据是否有效筛选
        if (queryParamVO.getKeySubWord() != null && !queryParamVO.getKeySubWord().trim().isEmpty()) {
            queryWrapper.eq(MaterialClassifyTypeMapping::getIsActive, Integer.parseInt(queryParamVO.getKeySubWord()));
        }
        
        queryWrapper.orderByAsc(MaterialClassifyTypeMapping::getMaterialName);
        
        return this.materialClassifyTypeMappingMapper.selectList(queryWrapper);
    }

    /**
     * 获取当前最大的类型值
     * @return 最大的类型值
     */
    public String getMaxTypeValue() {
        try {
            List<MaterialClassifyTypeMapping> allMappings = this.list(
                new LambdaQueryWrapper<MaterialClassifyTypeMapping>()
                    .eq(MaterialClassifyTypeMapping::getIsActive, 1)
                    .orderByDesc(MaterialClassifyTypeMapping::getTypeValue)
            );

            if (CollectionUtils.isEmpty(allMappings)) {
                return null;
            }

            // 找到最大的数字类型值
            String maxTypeValue = null;
            int maxNumericValue = -1;

            for (MaterialClassifyTypeMapping mapping : allMappings) {
                String typeValue = mapping.getTypeValue();
                if (typeValue != null && !typeValue.trim().isEmpty()) {
                    try {
                        int numericValue = Integer.parseInt(typeValue.trim());
                        if (numericValue > maxNumericValue) {
                            maxNumericValue = numericValue;
                            maxTypeValue = typeValue.trim();
                        }
                    } catch (NumberFormatException e) {
                        // 忽略非数字的类型值
                        logger.debug("跳过非数字类型值：{}", typeValue);
                    }
                }
            }

            return maxTypeValue;

        } catch (Exception e) {
            logger.error("获取最大类型值时发生异常", e);
            return null;
        }
    }
}
