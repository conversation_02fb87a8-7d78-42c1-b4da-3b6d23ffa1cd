package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.BasicWarehouseContainer;
import com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Service
public class BasicWarehouseContainerService extends ServiceImpl<BasicWarehouseContainerMapper, BasicWarehouseContainer> {

    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;
    @Resource
    BasicMaterialInfoService basicMaterialInfoService;
    @Resource
    BasicMaterialBatchInventoryMapper basicMaterialBatchInventoryMapper;
    /**
     * 新增容器信息
     */
    public ResponseResult addWarehouseContainer(BasicWarehouseContainer basicWarehouseContainer) {
        basicWarehouseContainer.setId(UUID.randomUUID().toString());
        basicWarehouseContainer.setContainerCode(this.getMaxIndex(CommonConstant.CodePrefix.CONTAINER_PREFIX));
        this.save(basicWarehouseContainer);
        return ResponseResult.getSuccessResult();
    }

    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = this.basicWarehouseContainerMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }

    /**
     * 更新容器信息
     */
    public ResponseResult updateWarehouseContainer(BasicWarehouseContainer basicWarehouseContainer) {
        String id = basicWarehouseContainer.getId();
        if (StringUtil.isEmpty(id)) {
            return ResponseResult.getErrorResult("更新主键不能为空!");
        }
        //判断容器是存在物料
        if (basicWarehouseContainer.getStatus().equals(CommonConstant.ContainerState.UN_USE)){
            List<BasicMaterialBatchInventory> basicMaterialBatchInventories = basicMaterialBatchInventoryMapper.selectByContainerCode(basicWarehouseContainer.getContainerCode());
            if (basicMaterialBatchInventories != null && basicMaterialBatchInventories.size()>0){
                return ResponseResult.getErrorResult("容器内存在物料，请勿更新为不可用");
            }
        }
        this.updateById(basicWarehouseContainer);
        return ResponseResult.getSuccessResult();
    }
    /**
     * 删除容器信息
     */
    public ResponseResult deleteWarehouseContainer(String id) {
        //判断容器是存在物料
        BasicWarehouseContainer basicWarehouseContainer = this.getById(id);
        List<BasicMaterialBatchInventory> basicMaterialBatchInventories = basicMaterialBatchInventoryMapper.selectByContainerCode(basicWarehouseContainer.getContainerCode());
        if (basicMaterialBatchInventories != null && basicMaterialBatchInventories.size()>0){
            return ResponseResult.getErrorResult("容器内存在物料，不允许进行删除操作");
        }
        this.removeById(id);
        return ResponseResult.getSuccessResult();
    }
    /**
     * 查询容器信息
     */
    public List<BasicWarehouseContainer> queryWarehouseContainer(QueryParamVO queryParamVO) {
        QueryWrapper<BasicWarehouseContainer> queryWrapper = new QueryWrapper<>();
        // 添加动态条件
        if (queryParamVO.getKeyWord() != null && !queryParamVO.getKeyWord().isEmpty()) {
            queryWrapper.lambda()
                    .eq(BasicWarehouseContainer::getLocationCode, queryParamVO.getKeyWord());
        }
        if (queryParamVO.getState() != null) {
            queryWrapper.lambda()
                    .eq(BasicWarehouseContainer::getStatus, queryParamVO.getState());
        }
        return this.list(queryWrapper);
    }
    /**
     * 通过库位编码删除仓库容器信息
     */
    public void deleteWarehouseContainerByCode(String location_code) {
        QueryWrapper<BasicWarehouseContainer> uptWrapper = new QueryWrapper<>();
        uptWrapper.eq("location_code", location_code);
        // 执行删除操作
        this.remove(uptWrapper);
    }

    public List<BasicWarehouseContainer> qryContainerByLocationCode(String location_code) {
        QueryWrapper<BasicWarehouseContainer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("location_code", location_code);
        return this.list(queryWrapper);
    }

    public ResponseResult queryContainerLocationInfo(QueryParamVO queryParamVO) {
        ContainerLocationInfoDto containerLocationInfoDto = null;
        if (StringUtils.isNotEmpty(queryParamVO.getKeyWord())){
            containerLocationInfoDto = basicWarehouseContainerMapper.queryContainerLocationInfo(queryParamVO.getKeyWord());
        }
        return ResponseResult.getSuccessResult("success",containerLocationInfoDto);
    }
}
