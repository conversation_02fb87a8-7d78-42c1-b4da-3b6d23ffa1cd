package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.domain.basicData.BasicBomCustomer;
import com.ruoyi.domain.basicData.BasicDocumentDetail;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.DocumentInventoryDetail;
import com.ruoyi.mapper.basicData.*;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.basicData.DocumentInventoryDetailDto;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DocumentInventoryDetailService extends ServiceImpl<DocumentInventoryDetailMapper, DocumentInventoryDetail> {

    @Resource
    private DocumentInventoryDetailMapper documentInventoryDetailMapper;

    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;

    @Resource
    BasicMaterialBatchInventoryMapper basicMaterialBatchInventoryMapper;

    public List<DocumentInventoryDetailDto> queryDocumentInventoryDetail(QueryParamVO queryParamVO) {
        List<DocumentInventoryDetailDto> documentInventoryDetails = documentInventoryDetailMapper.queryDocumentInventoryDetail(queryParamVO);
        for (DocumentInventoryDetailDto documentInventoryDetailDto : documentInventoryDetails){
            String inventoryCode = documentInventoryDetailDto.getInventoryCode();
                //查询容器
                ContainerLocationInfoDto containerLocationInfoDto = basicWarehouseContainerMapper.queryContainerLocationInfo(inventoryCode);
                if (containerLocationInfoDto != null){
                    documentInventoryDetailDto.setLocation(containerLocationInfoDto.getWarehouseName()+ "-" + containerLocationInfoDto.getShelfName() +
                            "-"+containerLocationInfoDto.getLevelName()+ "-"+containerLocationInfoDto.getPositionName());
                }
        }
        return documentInventoryDetails;
    }
}
