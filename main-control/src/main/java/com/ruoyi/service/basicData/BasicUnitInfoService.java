package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.basicData.BasicUnitInfo;
import com.ruoyi.mapper.basicData.BasicUnitInfoMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 计量单位服务
 */
@Slf4j
@Service
public class BasicUnitInfoService extends ServiceImpl<BasicUnitInfoMapper, BasicUnitInfo> {

    @Resource
    private ErpService erpService;

    /**
     * 分页查询
     */
    public List<BasicUnitInfo> queryBasicUnitInfo(QueryParamVO param) {
        QueryWrapper<BasicUnitInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(param.getKeyWord())) {
            queryWrapper.and(i -> i.like("unit_code", param.getKeyWord())
                    .or().like("unit_name", param.getKeyWord()));
        }
        if(StringUtils.isNotEmpty(param.getBdate())){
            queryWrapper.ge("create_time",param.getBdate());
        }
        if(StringUtils.isNotEmpty(param.getEdate())){
            queryWrapper.le("create_time",param.getEdate());
        }
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }

    /**
     * 从ERP同步计量单位信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP计量单位信息...");

        // 1. 构建ERP查询请求
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.UNIT);
        queryReq.setFieldKeys(String.join(",",
                CommonConstant.ErpFieldKeys.Unit.ID,
                CommonConstant.ErpFieldKeys.Unit.NAME,
                CommonConstant.ErpFieldKeys.Unit.CODE,
                CommonConstant.ErpFieldKeys.Unit.DESCRIPTION,
                CommonConstant.ErpFieldKeys.Unit.IS_BASE_UNIT
        ));
        queryReq.setLimit(0);

        // 2. 从ERP获取数据
        List<Map<String, Object>> erpDataList = erpService.getErpDataList(queryReq, "计量单位");
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到计量单位信息，或查询失败。");
            return ResponseResult.getSuccessResult("ERP中未查询到计量单位信息", null);
        }
        log.info("从ERP查询到 {} 条计量单位信息，准备进行同步...", erpDataList.size());

        // 3. 数据转换
        List<BasicUnitInfo> erpUnitList = erpDataList.stream()
                .map(this::mapToUnitInfo)
                .collect(Collectors.toList());

        // 4. 与本地数据比对，区分新增和更新
        List<String> erpIds = erpUnitList.stream().map(BasicUnitInfo::getId).collect(Collectors.toList());
        Map<String, BasicUnitInfo> existingUnitMap = this.listByIds(erpIds).stream()
                .collect(Collectors.toMap(BasicUnitInfo::getId, Function.identity()));

        List<BasicUnitInfo> toAddList = new ArrayList<>();
        List<BasicUnitInfo> toUpdateList = new ArrayList<>();

        for (BasicUnitInfo erpUnit : erpUnitList) {
            BasicUnitInfo localUnit = existingUnitMap.get(erpUnit.getId());
            if (localUnit == null) {
                erpUnit.setCreateTime(new Date());
                erpUnit.setCreateName("erp");
                toAddList.add(erpUnit);
            } else {
                if (isUnitInfoChanged(erpUnit, localUnit)) {
                    erpUnit.setUpdateTime(new Date());
                    erpUnit.setUpdateName("erp");
                    erpUnit.setCreateTime(localUnit.getCreateTime());
                    erpUnit.setCreateName(localUnit.getCreateName());
                    toUpdateList.add(erpUnit);
                }
            }
        }

        // 5. 批量执行数据库操作
        if (!toAddList.isEmpty()) {
            this.saveBatch(toAddList);
            log.info("成功新增 {} 条计量单位信息。", toAddList.size());
        }
        if (!toUpdateList.isEmpty()) {
            this.updateBatchById(toUpdateList);
            log.info("成功更新 {} 条计量单位信息。", toUpdateList.size());
        }

        String summary = String.format("同步计量单位信息完成，新增 %d 条，更新 %d 条。", toAddList.size(), toUpdateList.size());
        log.info(summary);
        return ResponseResult.getSuccessResult(summary, "");
    }

    /**
     * 将ERP数据转换为计量单位信息
     */
    private BasicUnitInfo mapToUnitInfo(Map<String, Object> dataMap) {
        BasicUnitInfo unit = new BasicUnitInfo();
        unit.setId(String.valueOf(dataMap.get(CommonConstant.ErpFieldKeys.Unit.ID)));
        unit.setUnitName((String) dataMap.get(CommonConstant.ErpFieldKeys.Unit.NAME));
        unit.setUnitCode((String) dataMap.get(CommonConstant.ErpFieldKeys.Unit.CODE));
        unit.setRemark((String) dataMap.get(CommonConstant.ErpFieldKeys.Unit.DESCRIPTION));
        unit.setIsBaseUnit((String.valueOf(dataMap.get(CommonConstant.ErpFieldKeys.Unit.IS_BASE_UNIT))));
        return unit;
    }

    /**
     * 判断计量单位信息是否发生变化
     */
    private boolean isUnitInfoChanged(BasicUnitInfo erpUnit, BasicUnitInfo localUnit) {
        return !Objects.equals(erpUnit.getUnitName(), localUnit.getUnitName()) ||
               !Objects.equals(erpUnit.getUnitCode(), localUnit.getUnitCode()) ||
               !Objects.equals(erpUnit.getRemark(), localUnit.getRemark()) ||
               !Objects.equals(erpUnit.getIsBaseUnit(), localUnit.getIsBaseUnit());
    }
}