package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicBomInfo;
import com.ruoyi.domain.basicData.BasicBomVersion;
import com.ruoyi.domain.basicData.BasicMaterialInfo;
import com.ruoyi.mapper.basicData.BasicBomInfoMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.GsonUtils;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.basicData.BomInfoAddDto;
import com.ruoyi.vo.basicData.BomInfoAddReq;
import com.ruoyi.vo.basicData.BomInfoDto;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/12 11:47
 * @Description: BOM数据
 */
@Service
public class BasicBomInfoService extends ServiceImpl<BasicBomInfoMapper, BasicBomInfo> {

    private static final Logger logger = LoggerFactory.getLogger(BasicBomInfoService.class);

    @Resource
    private BasicBomInfoMapper basicBomInfoMapper;

    @Resource
    private BasicBomVersionService bomVersionService;

    @Resource
    private BasicMaterialInfoService materialInfoService;

    /**
     * 新增物料/产品BOM清单
     */
    @Transactional
    public ResponseResult insertBasicBom(BomInfoAddReq req){

        String materialCode = req.getMaterialCode();
        String version = req.getVersion();
        //若BOM-Version存在匹配的数据，则表示存在该版本的BOM，需进行更新，否则按新版本进行BOM保存；
        BasicBomVersion bomVersion = this.bomVersionService.queryMatchBomVersion(materialCode,version);
        if(bomVersion != null){
            //先删除该物料和版本对应的BOM数据，再进行保存；
            LambdaQueryWrapper<BasicBomInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BasicBomInfo::getBelongMaterialCode,materialCode);
            wrapper.eq(BasicBomInfo::getVersionId,bomVersion.getId());
            this.basicBomInfoMapper.delete(wrapper);

            for (BomInfoAddDto temp: req.getBomDetails()){
                saveBomTree(temp,materialCode,req.getCompanyId(),bomVersion.getId(),
                        req.getVersion(),req.getCreateName());
            }
        }else{
            String versionId  = this.bomVersionService.insertBasicBomVersion(materialCode,version,req.getCreateName());
            for (BomInfoAddDto temp: req.getBomDetails()){
                saveBomTree(temp,materialCode,req.getCompanyId(),versionId,req.getVersion(),req.getCreateName());
            }
        }
        //处理其他物料中包含了此物料bom
        syncOtherMaterialBom(req);
        return ResponseResult.getSuccessResult();
    }

    @Transactional(rollbackFor = Exception.class)
    private void syncOtherMaterialBom(BomInfoAddReq req) {
        String rootMaterialCode = req.getMaterialCode();
        //获取最新标准版本的bom（无客户）
        List<BasicBomInfo> otherMaterials = this.basicBomInfoMapper.getLatestStandardVersionSameMaterial(rootMaterialCode);
        BasicBomVersion bomVersion = this.bomVersionService.queryMatchBomVersion(rootMaterialCode, req.getVersion());
        for (BasicBomInfo firstNodeTemp : otherMaterials){//————1.0层级的物料
            //递归删除该物料和其下阶物料数据
            boolean delSuccess = this.recursionDelBomInfo(firstNodeTemp.getId());
            if(delSuccess){
                //重新保存该物料
                BasicBomInfo firstNode = new BasicBomInfo();
                BeanUtils.copyProperties(firstNodeTemp,firstNode);
                String firstNodeId = LocalStringUtils.getDataUUID();
                firstNode.setId(firstNodeId);
                firstNode.setCreateTime(DateAndTimeUtil.getNowDate());

                firstNode.setVersionId(bomVersion.getId());
                System.out.println("syncOtherMaterialBom 方法保存的物料：" + GsonUtils.toJsonString(firstNode));
                this.basicBomInfoMapper.insert(firstNode);
                //重新计算该物料的下阶物料消耗量，并保存数据
                for (BomInfoAddDto childTemp: req.getBomDetails()){
                    calNumSaveBomChildTree(firstNode, bomVersion.getId(), req.getCreateName(),childTemp);
                }
            }
        }
    }

    /**
     * 递归删除该物料和其下阶物料数据
     * @param id 当前数据的主键
     * @return  删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean recursionDelBomInfo(String id) {
        // 1. 查询当前物料的所有子物料
        List<BasicBomInfo> childMenus = list(new LambdaQueryWrapper<BasicBomInfo>()
                .eq(BasicBomInfo::getParentId, id));
        // 2. 递归删除子物料
        for (BasicBomInfo child : childMenus) {
            recursionDelBomInfo(child.getId());
        }
        // 3. 删除当前物料
        return this.basicBomInfoMapper.deleteById(id) == 1;
    }

    /**
     * 计算消耗量并保存第一节点BOM数据
     * @param parentNodeTemp 第一阶物料数据
     * @param versionId   所属物料的BOM版本ID
     * @param createName    BOM的创建用户
     * @param childTemp 子节点数据
     */
    @Transactional
    private void calNumSaveBomChildTree(BasicBomInfo parentNodeTemp, String versionId,
                                        String createName, BomInfoAddDto childTemp) {
        BasicBomInfo firstChildNode = new BasicBomInfo();
        BeanUtils.copyProperties(childTemp,firstChildNode);

        String firstChildNodeId = LocalStringUtils.getDataUUID();
        firstChildNode.setId(firstChildNodeId);
        String belongMaterialCode = parentNodeTemp.getBelongMaterialCode();
        firstChildNode.setBelongMaterialCode(belongMaterialCode);
        firstChildNode.setParentId(parentNodeTemp.getId());
        float upperNum = parentNodeTemp.getMaterialNum();
        float initNum = childTemp.getMaterialNum();
        float scUseNum = initNum * upperNum;
        firstChildNode.setMaterialNum(scUseNum);
        firstChildNode.setCreateTime(DateAndTimeUtil.getNowDate());
        firstChildNode.setVersionId(versionId);
        System.out.println("calNumSaveBomChildTree 方法保存的物料：" + GsonUtils.toJsonString(firstChildNode));
        this.basicBomInfoMapper.insert(firstChildNode);

        syncUptBomChild(firstChildNodeId,belongMaterialCode,
                firstChildNode.getMaterialNum(),versionId,createName,childTemp); // ——保存1.1层级的物料
    }

    /**
     * 同步更新子节点数据
     * @param parentId 父类ID
     * @param belongMaterial 所属物料编码
     * @param versionId   父类版本号 ID
     * @param createName    创建者
     * @param childNode    子类数据
     */
    private void syncUptBomChild(String parentId,String belongMaterial,float materialNum,
                                 String versionId,String createName,
                                 BomInfoAddDto childNode) {
        List<BomInfoAddDto> children = childNode.getChildren();
        if (children == null || children.isEmpty()) {
            return;
        }
        for (BomInfoAddDto childTemp : children) {
            childTemp.setParentId(parentId);
            BasicBomInfo childBom = new BasicBomInfo();
            BeanUtils.copyProperties(childTemp,childBom);
            String childId = LocalStringUtils.getDataUUID();
            childBom.setId(childId);
            childTemp.setId(childId);
            float initNum = childTemp.getMaterialNum();
            float scUseNum = initNum * materialNum;
            childBom.setMaterialNum(scUseNum);

            childBom.setBelongMaterialCode(belongMaterial);
            childBom.setCreateTime(DateAndTimeUtil.getNowDate());
            childBom.setCreateName(createName);
            childBom.setVersionId(versionId);
            childBom.setParentId(parentId);
            System.out.println("syncUptBomChild 方法保存的物料：" + GsonUtils.toJsonString(childBom));
            this.basicBomInfoMapper.insert(childBom);
            syncUptBomChild(childId,belongMaterial,childBom.getMaterialNum(),versionId,createName,childTemp);
        }
    }

    /**
     * 保存物料BOM数据结构数据
     * @param firstNode 第一级节点
     * @param rootMaterialCode  父类物料编码
     * @param companyId 父类公司编码
     * @param versionId 版本号ID
     * @param version 版本号
     * @param createName    创建用户
     */
    private void saveBomTree(BomInfoAddDto firstNode,String rootMaterialCode,String companyId,
                            String versionId,String version,String createName) {
        if (firstNode == null) {
            return;
        }
        // 保存第一级节点
        BasicBomInfo firstLevelNode = new BasicBomInfo();
        BeanUtils.copyProperties(firstNode,firstLevelNode);
        String firstNodeId = LocalStringUtils.getDataUUID();
        firstLevelNode.setId(firstNodeId);
        firstLevelNode.setBelongMaterialCode(rootMaterialCode);
        firstLevelNode.setCompanyId(companyId);
        firstLevelNode.setCreateTime(DateAndTimeUtil.getNowDate());

        firstLevelNode.setVersionId(versionId);
        this.basicBomInfoMapper.insert(firstLevelNode);
        // 递归保存子节点
        firstNode.setId(firstNodeId);
        saveChildren(firstNode,rootMaterialCode,companyId,version,createName);
    }

    private void saveChildren(BomInfoAddDto parentNode,String rootMaterialCode,String companyId,
                              String version,String createName) {
        List<BomInfoAddDto> children = parentNode.getChildren();
        if (children == null || children.isEmpty()) {
            return;
        }
        String materialCode = parentNode.getMaterialCode();
        for (BomInfoAddDto child : children) {
            child.setParentId(parentNode.getId());
            BasicBomInfo bom = new BasicBomInfo();
            BeanUtils.copyProperties(child,bom);
            String childId = LocalStringUtils.getDataUUID();
            bom.setId(childId);
            child.setId(childId);
            bom.setBelongMaterialCode(rootMaterialCode);
            bom.setCompanyId(companyId);
            bom.setCreateTime(DateAndTimeUtil.getNowDate());
            String versionObjId = this.bomVersionService.insertBasicBomVersion(materialCode,version,createName);
            bom.setVersionId(versionObjId);
            bom.setParentId(parentNode.getId());
            this.basicBomInfoMapper.insert(bom);
            saveChildren(child,rootMaterialCode,companyId,version,createName);
        }
    }

    /**
     * 删除物料/产品BOM清单
     */
    public ResponseResult deleteBasicBom(BatchIdsReq req) {
        int count = this.basicBomInfoMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新物料/产品BOM清单
     */
    public ResponseResult uptBasicBom(BasicBomInfo basicBomInfo) {
        int count = this.basicBomInfoMapper.updateById(basicBomInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询
     */
    public List<BomInfoDto> queryBasicBom(int materialSort,QueryParamVO queryParamVO) {
        switch (materialSort){
            case CommonConstant.MaterialSort.SEMI:
                return basicBomInfoMapper.queryMaterialBom(queryParamVO);
            case CommonConstant.MaterialSort.PRODUCT:
                return basicBomInfoMapper.queryProductBom(queryParamVO);
        }
        return null;
    }

    public static List<String> splitAndExcludeFirst(String input) {
        List<String> values = new ArrayList<>();
        // 使用逗号拆分字符串
        String[] splitValues = input.split(",");
        // 从索引1开始添加到结果列表
        for (int i = 1; i < splitValues.length; i++) {
            values.add(splitValues[i]);
        }
        return values;
    }

    /**
     * 判断父节点下是否该物料/产品已存在
     */
    public boolean checkLocationCodeUnique(BasicBomInfo basicBomInfo) {
        BasicBomInfo info = basicBomInfoMapper.checkLocationCodeUnique(basicBomInfo.getParentId(), basicBomInfo.getMaterialCode());
        if (StringUtils.isNotNull(info)) {
            return false;
        }
        return true;
    }

    /**
     * 根据物料/产品号和版本ID 删除其对应的BOM清单数据
     */
    public void delBomsByMaterialCodeVersionId(String id, String materialCode) {

        LambdaQueryWrapper<BasicBomInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicBomInfo::getMaterialCode,materialCode);
        wrapper.eq(BasicBomInfo::getVersionId,id);
        this.remove(wrapper);
    }

    /**
     * 根据物料/产品号和公司ID以及版本ID 删除其对应的BOM清单数据
     */
    public void delBomsByMaterialCodeCompanyId(String companyId,String materialCode,String versionId) {

        LambdaQueryWrapper<BasicBomInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicBomInfo::getBelongMaterialCode,materialCode);
        wrapper.eq(BasicBomInfo::getVersionId,versionId);
        wrapper.eq(BasicBomInfo::getCompanyId,companyId);
        this.remove(wrapper);
    }


    /**
     * 递归查找物料
     * @param parentIdTemp 父类数据主键
     */
    public void recursiveQuery(String parentIdTemp,List<BomInfoDto> result) {
        // 1. 查询所有子节点
        List<BomInfoDto> children = this.basicBomInfoMapper.queryMaterialBomByParentId(parentIdTemp);
        // 2. 递归查询子节点
        if (!children.isEmpty()) {
            result.addAll(children);
            // 递归查询每个子节点的下级节点
            for (BomInfoDto child : children) {
                recursiveQuery(child.getId(),result);
            }
        }
    }

    public List<BomInfoDto> queryWholeMaterialBom(QueryParamVO queryParamVO) {
        List<BomInfoDto> result = new ArrayList<>();
        BasicMaterialInfo parentMaterialInfo = this.materialInfoService.queryBasicMaterialByCode(queryParamVO.getKeyWord());
        BomInfoDto parentBomDto = new BomInfoDto();
        BeanUtils.copyProperties(parentMaterialInfo,parentBomDto);
        result.add(parentBomDto);
        QueryParamVO queryBelongBom = new QueryParamVO();
        queryBelongBom.setKeyWord(queryParamVO.getKeyWord());
        List<BomInfoDto> list = this.basicBomInfoMapper.queryMaterialBom(queryBelongBom);
        result.addAll(list);
        // 递归查询
        recursiveQuery(parentMaterialInfo.getId(), result);
        return result;
    }

    /**
     * 根据物料和版本ID获取扁平的BOM列表
     * @param materialCode 所属物料编码
     * @param versionId BOM版本ID
     * @return
     */
    public List<BasicBomInfo> getFlatBomListByMaterialAndVersion(String materialCode, String versionId) {
        if (StringUtils.isAnyBlank(materialCode, versionId)) {
            return new ArrayList<>();
        }
        // 查找整个BOM树
        List<BasicBomInfo> wholeBomTree = this.list(new LambdaQueryWrapper<BasicBomInfo>()
                .eq(BasicBomInfo::getBelongMaterialCode, materialCode)
                .eq(BasicBomInfo::getVersionId, versionId));

        return wholeBomTree; // 直接返回查询到的整个BOM结构
    }

    /**
     * 获取一个物料最新的、可用于生产的BOM清单
     * @param materialCode 物料编码
     * @return List<BasicBomInfo>
     */
    public List<BasicBomInfo> getLatestActiveBom(String materialCode) {
        // 1. 查找最新的已激活BOM版本
        BasicBomVersion latestVersion = bomVersionService.getOne(new LambdaQueryWrapper<BasicBomVersion>()
                .eq(BasicBomVersion::getMaterialCode, materialCode)
                .eq(BasicBomVersion::getBomState, CommonConstant.BomState.PRODUCE_VERSION)
                .orderByDesc(BasicBomVersion::getCreateTime)
                .last("LIMIT 1"));

        if (latestVersion == null) {
            return new ArrayList<>();
        }

        // 2. 使用该版本ID获取完整的BOM列表
        return getFlatBomListByMaterialAndVersion(materialCode, latestVersion.getId());
    }
}
