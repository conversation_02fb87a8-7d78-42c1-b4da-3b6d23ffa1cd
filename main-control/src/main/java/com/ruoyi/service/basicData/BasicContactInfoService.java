package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.basicData.BasicCompanyInfo;
import com.ruoyi.domain.basicData.BasicContactInfo;
import com.ruoyi.mapper.basicData.BasicContactInfoMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 联系人信息服务
 */
@Slf4j
@Service
public class BasicContactInfoService extends ServiceImpl<BasicContactInfoMapper, BasicContactInfo> {

    @Resource
    private ErpService erpService;

    /**
     * 分页查询
     */
    public List<BasicContactInfo> queryBasicContactInfo(QueryParamVO param) {
        QueryWrapper<BasicContactInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(param.getKeyWord())) {
            queryWrapper.eq("company_code", param.getKeyWord());
        }
        return this.list(queryWrapper);
    }

    /**
     * 从ERP同步联系人信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP联系人信息...");

        // 1. 构建ERP查询请求
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.COMMON_CONTACT);
        queryReq.setFieldKeys(String.join(",",
                CommonConstant.ErpFieldKeys.Contact.ID,
                CommonConstant.ErpFieldKeys.Contact.NAME,
                CommonConstant.ErpFieldKeys.Contact.CODE,
                CommonConstant.ErpFieldKeys.Contact.DESCRIPTION,
                CommonConstant.ErpFieldKeys.Contact.POSITION,
                CommonConstant.ErpFieldKeys.Contact.MOBILE,
                CommonConstant.ErpFieldKeys.Contact.EMAIL,
                CommonConstant.ErpFieldKeys.Contact.ADDRESS,
                CommonConstant.ErpFieldKeys.Contact.CUSTOMER_ID,
                CommonConstant.ErpFieldKeys.Contact.COMPANY
        ));
        queryReq.setLimit(0);

        // 2. 从ERP获取数据
        List<Map<String, Object>> erpDataList = erpService.getErpDataList(queryReq, "联系人");
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到联系人信息，或查询失败。");
            return ResponseResult.getSuccessResult("ERP中未查询到联系人信息", null);
        }
        log.info("从ERP查询到 {} 条联系人信息，准备进行同步...", erpDataList.size());

        // 3. 数据转换
        List<BasicContactInfo> erpContactList = erpDataList.stream()
                .map(this::mapToContactInfo)
                .collect(Collectors.toList());

        // 4. 与本地数据比对，区分新增和更新
        List<String> erpIds = erpContactList.stream().map(BasicContactInfo::getId).collect(Collectors.toList());
        Map<String, BasicContactInfo> existingContactMap = this.listByIds(erpIds).stream()
                .collect(Collectors.toMap(BasicContactInfo::getId, Function.identity()));

        List<BasicContactInfo> toAddList = new ArrayList<>();
        List<BasicContactInfo> toUpdateList = new ArrayList<>();

        for (BasicContactInfo erpContact : erpContactList) {
            BasicContactInfo localContact = existingContactMap.get(erpContact.getId());
            if (localContact == null) {
                erpContact.setCreateTime(new Date());
                toAddList.add(erpContact);
            } else {
                // 存在，检查是否需要更新
                if (isContactInfoChanged(erpContact, localContact)) {
                    erpContact.setUpdateTime(new Date());
                    erpContact.setCreateTime(localContact.getCreateTime()); // 保持创建时间不变
                    toUpdateList.add(erpContact);
                }
            }
        }

        // 5. 批量执行数据库操作
        if (!toAddList.isEmpty()) {
            this.saveBatch(toAddList);
            log.info("成功新增 {} 条联系人信息。", toAddList.size());
        }
        if (!toUpdateList.isEmpty()) {
            this.updateBatchById(toUpdateList);
            log.info("成功更新 {} 条联系人信息。", toUpdateList.size());
        }

        String summary = String.format("同步联系人信息完成，新增 %d 条，更新 %d 条。", toAddList.size(), toUpdateList.size());
        log.info(summary);
        return ResponseResult.getSuccessResult(summary, "");
    }

    /**
     * 将ERP数据转换为联系人信息
     */
    private BasicContactInfo mapToContactInfo(Map<String, Object> dataMap) {
        BasicContactInfo contact = new BasicContactInfo();
        contact.setId(String.valueOf(dataMap.get(CommonConstant.ErpFieldKeys.Contact.ID)));
        contact.setContactName((String) dataMap.get(CommonConstant.ErpFieldKeys.Contact.NAME));
        contact.setContactCode((String) dataMap.get(CommonConstant.ErpFieldKeys.Contact.CODE));
        contact.setRemark((String) dataMap.get(CommonConstant.ErpFieldKeys.Contact.DESCRIPTION));
        contact.setContactPosition((String) dataMap.get(CommonConstant.ErpFieldKeys.Contact.POSITION));
        contact.setContactTel((String) dataMap.get(CommonConstant.ErpFieldKeys.Contact.MOBILE));
        contact.setContactEmail((String) dataMap.get(CommonConstant.ErpFieldKeys.Contact.EMAIL));
        contact.setAddress((String) dataMap.get(CommonConstant.ErpFieldKeys.Contact.ADDRESS));
        contact.setBelongCust(String.valueOf(dataMap.get(CommonConstant.ErpFieldKeys.Contact.CUSTOMER_ID)));
        contact.setCompanyCode(String.valueOf(dataMap.get(CommonConstant.ErpFieldKeys.Contact.COMPANY)));
        return contact;
    }

    /**
     * 判断联系人信息是否发生变化
     */
    private boolean isContactInfoChanged(BasicContactInfo erpContact, BasicContactInfo localContact) {
        return !Objects.equals(erpContact.getContactName(), localContact.getContactName()) ||
                !Objects.equals(erpContact.getContactCode(), localContact.getContactCode()) ||
                !Objects.equals(erpContact.getRemark(), localContact.getRemark()) ||
                !Objects.equals(erpContact.getContactPosition(), localContact.getContactPosition()) ||
                !Objects.equals(erpContact.getContactTel(), localContact.getContactTel()) ||
                !Objects.equals(erpContact.getContactEmail(), localContact.getContactEmail()) ||
                !Objects.equals(erpContact.getAddress(), localContact.getAddress()) ||
                !Objects.equals(erpContact.getBelongCust(), localContact.getBelongCust()) ||
                !Objects.equals(erpContact.getCompanyCode(), localContact.getCompanyCode());
    }
}
