package com.ruoyi.service.basicData;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicWarehouseInfo;
import com.ruoyi.mapper.basicData.BasicWarehouseInfoMapper;
import com.ruoyi.utils.*;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.warehouse.BasicWarehouseDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class BasicWarehouseInfoService extends ServiceImpl<BasicWarehouseInfoMapper, BasicWarehouseInfo> {

    @Resource
    BasicWarehouseInfoMapper basicWarehouseInfoMapper;
    @Resource
    BasicWarehouseLocationService basicWarehouseLocationService;
    @Resource
    BasicWarehouseContainerService basicWarehouseContainerService;


    /**
     * 新增仓库信息
     */
    public ResponseResult addBasicWarehouse(BasicWarehouseDto param) {
        String warehouseCode = param.getWarehouseCode();
        if (StringUtils.isEmpty(warehouseCode)) {
            warehouseCode = this.getMaxIndex(CommonConstant.CodePrefix.WAREHOUSE_PREFIX);
        }else {
            BasicWarehouseInfo basicWarehouseInfo = this.getOne
                    (new LambdaQueryWrapper<BasicWarehouseInfo>().eq(BasicWarehouseInfo::getWarehouseCode,param.getWarehouseCode()));
            if (basicWarehouseInfo != null){
                return ResponseResult.getErrorResult("仓库编码已存在，请勿重复添加!");
            }
        }
        BasicWarehouseInfo basicWarehouseInfo = new BasicWarehouseInfo();
        BeanUtils.copyProperties(param,basicWarehouseInfo);
        basicWarehouseInfo.setId(UUID.randomUUID().toString());
        basicWarehouseInfo.setWarehouseCode(warehouseCode);
        basicWarehouseInfo.setCreateTime(new Date());
        basicWarehouseInfo.setCreateName(SecurityUtils.getUsername());
        this.save(basicWarehouseInfo);
        return ResponseResult.getSuccessResult();
    }

    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = this.basicWarehouseInfoMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }

    /**
     * 更新仓库信息
     */
    public ResponseResult updateBasicWarehouse(BasicWarehouseInfo basicWarehouseInfo) {
        String id = basicWarehouseInfo.getId();
        if (StringUtil.isEmpty(id)) {
            return ResponseResult.getErrorResult("更新主键不能为空!");
        }
        this.updateById(basicWarehouseInfo);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 删除仓库信息
     */
    @Transactional
    public ResponseResult deleteBasicWarehouse(BasicWarehouseInfo basicWarehouseInfo) {
        String id = basicWarehouseInfo.getId();
        if (StringUtil.isEmpty(id)) {
            return ResponseResult.getErrorResult("删除主键不能为空!");
        }
        ResponseResult responseResult = basicWarehouseLocationService.deleteWarehouseLocationByCode(basicWarehouseInfo.getWarehouseCode());
        if (responseResult.getCode().equals(ResultMsg.errorCode)) {
            return responseResult;
        }
        this.removeById(id);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 查询仓库信息
     */
    public List<BasicWarehouseInfo> queryBasicWarehouse(QueryParamVO queryParamVO) {
        QueryWrapper<BasicWarehouseInfo> queryWrapper = new QueryWrapper<>();
        if (queryParamVO.getKeyWord() != null && !queryParamVO.getKeyWord().isEmpty()) {
            queryWrapper.lambda()
                    .like(BasicWarehouseInfo::getWarehouseName,queryParamVO.getKeyWord());
        }
        if (queryParamVO.getState() != null) {
            queryWrapper.lambda()
                    .eq(BasicWarehouseInfo::getState,queryParamVO.getState());
        }
        if (queryParamVO.getStateSub() != null) {
            queryWrapper.lambda()
                    .eq(BasicWarehouseInfo::getWarehouseType,queryParamVO.getStateSub());
        }
        if (queryParamVO.getState() != null) {
            queryWrapper.lambda()
                    .eq(BasicWarehouseInfo::getState,queryParamVO.getState());
        }
        if (queryParamVO.getBdate() != null) {
            queryWrapper.lambda()
                    .ge(BasicWarehouseInfo::getCreateTime,queryParamVO.getBdate());
        }
        if (queryParamVO.getEdate() != null) {
            queryWrapper.lambda()
                    .le(BasicWarehouseInfo::getCreateTime,queryParamVO.getEdate());
        }
        //按时间排序
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }
}
