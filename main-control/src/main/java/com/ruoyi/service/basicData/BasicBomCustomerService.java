package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.domain.basicData.BasicBomCustomer;
import com.ruoyi.mapper.basicData.BasicBomCustomerMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.basicData.BomCustomerDto;
import com.ruoyi.vo.webRequest.DeleteReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/17 11:48
 * @Description: 客户BOM
 */
@Service
public class BasicBomCustomerService extends ServiceImpl<BasicBomCustomerMapper, BasicBomCustomer> {

    @Resource
    private BasicBomCustomerMapper basicBomCustomerMapper;

    @Resource
    private BasicBomInfoService materialBomService;

    /**
     * 新增客户BOM
     */
    public ResponseResult insertBasicBomCustomer(BasicBomCustomer basicCustomerInfo){
        basicCustomerInfo.setId(LocalStringUtils.getDataUUID());
        int result = this.basicBomCustomerMapper.insert(basicCustomerInfo);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 删除客户BOM
     * 前端已限制单项删除
     */
    public ResponseResult deleteBasicBomCustomer(DeleteReq req) {
        int count = this.basicBomCustomerMapper.deleteById(req.getIds());
        //删除BOM详情  String companyId,String materialCode,String versionId
        this.materialBomService.delBomsByMaterialCodeCompanyId(req.getKeyFourWord(),req.getKeyWord(),req.getKeyThirdWord());
        return ResponseResult.getSuccessResult();
    }

    /**
     * 更新客户BOM
     */
    public ResponseResult uptBasicBomCustomer(BasicBomCustomer basicCustomerInfo) {
        int count = this.basicBomCustomerMapper.updateById(basicCustomerInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询
     */
    public List<BomCustomerDto> queryBasicBomCustomer(QueryParamVO queryParamVO) {
        return basicBomCustomerMapper.queryBasicBomCustomer(queryParamVO);
    }
}
