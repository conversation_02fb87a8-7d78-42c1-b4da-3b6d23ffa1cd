package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.basicData.BasicCompanyGroup;
import com.ruoyi.mapper.basicData.BasicCompanyGroupMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BasicCompanyGroupService extends ServiceImpl<BasicCompanyGroupMapper, BasicCompanyGroup> {

    @Resource
    private ErpService erpService;

    /**
     * 从ERP同步客户分组
     */
    public ResponseResult syncFromErpCustomerGroups() throws Exception {
        return syncGroups(CommonConstant.ErpFormId.CUSTOMER_GROUP, CommonConstant.CompanyGroupType.CUSTOMER, "客户分组");
    }

    /**
     * 从ERP同步供应商分组
     */
    public ResponseResult syncFromErpSupplierGroups() throws Exception {
        return syncGroups(CommonConstant.ErpFormId.SUPPLIER_GROUP,  CommonConstant.CompanyGroupType.SUPPLIER, "供应商分组");
    }

    /**
     * 通用分组同步方法
     * @param formId ERP表单ID
     * @param groupType 分组类型 (0: 客户, 1: 供应商)
     * @param contextName 日志上下文名称
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncGroups(String formId, int groupType, String contextName) throws Exception {
        log.info("开始同步ERP[{}]...", contextName);

        // 1. 构建ERP查询请求
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(formId);
        queryReq.setFieldKeys(String.join(",",
                CommonConstant.ErpFieldKeys.ID,
                CommonConstant.ErpFieldKeys.CODE,
                CommonConstant.ErpFieldKeys.NAME));
        queryReq.setLimit(0); // 获取所有数据

        // 2. 从ERP获取数据
        List<Map<String, Object>> erpDataList = erpService.getErpDataList(queryReq, contextName);
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("[{}]查询结果为空，无需同步。", contextName);
            return ResponseResult.getSuccessResult("success", "查询结果为空，无需同步");
        }

        // 3. 数据转换
        List<BasicCompanyGroup> erpGroupList = new ArrayList<>();
        for (Map<String, Object> dataMap : erpDataList) {
            BasicCompanyGroup group = new BasicCompanyGroup();
            // ERP返回的Fid是Long类型
            Object erpIdObj = dataMap.get(CommonConstant.ErpFieldKeys.ID);
            if(erpIdObj instanceof Number) {
                group.setId(String.valueOf(((Number) erpIdObj).longValue()));
            } else {
                group.setId(String.valueOf(erpIdObj));
            }
            group.setGroupCode((String) dataMap.get(CommonConstant.ErpFieldKeys.CODE));
            group.setGroupName((String) dataMap.get(CommonConstant.ErpFieldKeys.NAME));
            group.setGroupType(groupType);
            group.setCreateTime(new Date());
            erpGroupList.add(group);
        }

        // 4. 获取本地数据
        Map<String, BasicCompanyGroup> localGroupMap = this.list(new LambdaQueryWrapper<BasicCompanyGroup>().eq(BasicCompanyGroup::getGroupType, groupType))
                .stream()
                .collect(Collectors.toMap(BasicCompanyGroup::getId, Function.identity()));

        // 5. 比较并准备待更新/新增列表
        List<BasicCompanyGroup> toAddList = new ArrayList<>();
        List<BasicCompanyGroup> toUpdateList = new ArrayList<>();

        for (BasicCompanyGroup erpGroup : erpGroupList) {
            BasicCompanyGroup localGroup = localGroupMap.get(erpGroup.getId());
            if (localGroup == null) {
                toAddList.add(erpGroup);
            } else {
                // 检查是否需要更新
                if (!erpGroup.getGroupCode().equals(localGroup.getGroupCode()) ||
                    !erpGroup.getGroupName().equals(localGroup.getGroupName())) {
                    // 更新字段
                    localGroup.setGroupCode(erpGroup.getGroupCode());
                    localGroup.setGroupName(erpGroup.getGroupName());
                    toUpdateList.add(localGroup);
                }
            }
        }

        // 6. 批量处理
        if (!CollectionUtils.isEmpty(toAddList)) {
            this.saveBatch(toAddList);
            log.info("新增 {} 条[{}]记录。", toAddList.size(), contextName);
        }
        if (!CollectionUtils.isEmpty(toUpdateList)) {
            this.updateBatchById(toUpdateList);
            log.info("更新 {} 条[{}]记录。", toUpdateList.size(), contextName);
        }

        int addCount = toAddList.size();
        int updateCount = toUpdateList.size();
        String summary = String.format("同步完成，新增 %d 条，更新 %d 条。", addCount, updateCount);
        log.info("[{}]同步完成。{}", contextName, summary);

        return ResponseResult.getSuccessResult("success", summary);
    }
}