package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.domain.basicData.BasicBomVersion;
import com.ruoyi.mapper.basicData.BasicBomVersionMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.webRequest.DeleteReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/17 11:48
 * @Description: BOM版本
 */
@Service
public class BasicBomVersionService extends ServiceImpl<BasicBomVersionMapper, BasicBomVersion> {

    @Resource
    private BasicBomVersionMapper basicBomVersionMapper;

    @Resource
    private BasicBomInfoService materialBomService;

    /**
     * 新增BOM版本
     */
    public ResponseResult insertBasicBomVersion(BasicBomVersion bomVersion){
        bomVersion.setId(LocalStringUtils.getDataUUID());
        bomVersion.setCreateTime(DateAndTimeUtil.getNowDate());

        BasicBomVersion dbData = this.queryMatchBomVersion(bomVersion.getMaterialCode(),
                bomVersion.getVersion());
        int result = 0;
        if(dbData != null){
            dbData.setRemark(bomVersion.getRemark());
            result = this.basicBomVersionMapper.updateById(dbData);
        }else{
            result = this.basicBomVersionMapper.insert(bomVersion);
        }
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 删除BOM版本
     */
    public ResponseResult deleteBasicBomVersion(DeleteReq req) {
        int count = this.basicBomVersionMapper.deleteById(req.getIds());
        //删除BOM详情
        this.materialBomService.delBomsByMaterialCodeVersionId(req.getIds(),req.getKeyWord());
        return ResponseResult.getSuccessResult();
    }

    /**
     * 更新BOM版本
     */
    public ResponseResult uptBasicBomVersion(BasicBomVersion basicCustomerInfo) {
        int count = this.basicBomVersionMapper.updateById(basicCustomerInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询
     */
    public List<BasicBomVersion> queryBasicBomVersion(QueryParamVO queryParamVO) {
        QueryWrapper<BasicBomVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("material_code",queryParamVO.getKeyWord());
        return basicBomVersionMapper.selectList(queryWrapper);
    }

    public String insertBasicBomVersion(String materielCode, String version,String createName) {
        BasicBomVersion dBBomVersion = this.queryMatchBomVersion(materielCode,version);
        if(dBBomVersion != null){
            return dBBomVersion.getVersion();
        }
        BasicBomVersion bomVersion = new BasicBomVersion();
        String id = LocalStringUtils.getDataUUID();
        bomVersion.setId(id);
        bomVersion.setVersion(version);
        bomVersion.setBomState(CommonConstant.BomState.TEST_VERSION);
        bomVersion.setMaterialCode(materielCode);
        bomVersion.setCreateName(createName);
        bomVersion.setCreateTime(DateAndTimeUtil.getNowDate());
        this.basicBomVersionMapper.insert(bomVersion);
        return id;
    }

    public BasicBomVersion queryMatchBomVersion(String materialCode, String version) {
        QueryWrapper<BasicBomVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("material_code",materialCode);
        queryWrapper.eq("version",version);
        return basicBomVersionMapper.selectOne(queryWrapper);
    }
}
