package com.ruoyi.service.basicData;

import com.ruoyi.domain.basicData.UpperSignalData;
import com.ruoyi.mapper.basicData.UpperSignalDataMapper;
import com.ruoyi.utils.QueryParamVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 上游信号表(UpperSignalData)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-21 21:43:08
 */
@Service("upperSignalDataService")
public class UpperSignalDataService {
    @Resource
    private UpperSignalDataMapper upperSignalDataMapper;

    public List<UpperSignalData> querySignalData(QueryParamVO queryParamVO) {
        return upperSignalDataMapper.querySignalData(queryParamVO);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(String id) {
        return this.upperSignalDataMapper.deleteById(id) > 0;
    }

    public void insertData(String request, String response,
                           Date receTime, String signal_type, String product_id, int is_web) {
        UpperSignalData upperSignalData = commSignalData(request, response, receTime, signal_type, product_id,is_web);
        upperSignalDataMapper.insert(upperSignalData);
    }


    private UpperSignalData commSignalData(String request, String response, Date receTime, String signalType, String productId,int isWeb) {
        UpperSignalData upperSignalData = new UpperSignalData();
        upperSignalData.setRequest(request);
        upperSignalData.setResponse(response);
        upperSignalData.setReceTime(receTime);
        upperSignalData.setSignalType(signalType);
        upperSignalData.setProductId(productId);
        upperSignalData.setResponseTime(new Date());
        upperSignalData.setIsWeb(isWeb);
        return upperSignalData;
    }
}
