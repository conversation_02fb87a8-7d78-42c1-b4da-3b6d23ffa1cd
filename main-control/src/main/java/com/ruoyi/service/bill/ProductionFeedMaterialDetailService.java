package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ProductionFeedMaterialDetail;
import com.ruoyi.mapper.bill.production.ProductionFeedMaterialDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionFeedMaterialDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产补料单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductionFeedMaterialDetailService extends ServiceImpl<ProductionFeedMaterialDetailMapper, ProductionFeedMaterialDetail> {

    @Resource
    private ProductionFeedMaterialDetailMapper productionFeedMaterialDetailMapper;

    /**
     * 查询生产补料单明细列表
     */
    public List<ProductionFeedMaterialDetailVo> queryProductionFeedMaterialDetailList(QueryParamVO queryParamVO) {
        return productionFeedMaterialDetailMapper.queryProductionFeedMaterialDetailVo(queryParamVO);
    }

    /**
     * 根据生产补料单ID查询明细列表（使用LambdaQueryWrapper）
     */
    public List<ProductionFeedMaterialDetail> getDetailsByFeedId(String feedId) {
        return this.lambdaQuery()
                .eq(ProductionFeedMaterialDetail::getFeedId, feedId)
                .list();
    }
}
