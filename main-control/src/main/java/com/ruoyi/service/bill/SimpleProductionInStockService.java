package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.domain.bill.SimpleProductionInStock;
import com.ruoyi.domain.bill.SimpleProductionInStockDetail;
import com.ruoyi.mapper.bill.production.SimpleProductionInStockDetailMapper;
import com.ruoyi.mapper.bill.production.SimpleProductionInStockMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.SimpleProductionInStockVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 简单生产入库单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class SimpleProductionInStockService extends ServiceImpl<SimpleProductionInStockMapper, SimpleProductionInStock> {

    private static final Logger logger = LoggerFactory.getLogger(SimpleProductionInStockService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private SimpleProductionInStockMapper simpleProductionInStockMapper;

    @Resource
    private SimpleProductionInStockDetailMapper simpleProductionInStockDetailMapper;
    
    @Resource
    private SimpleProductionInStockDetailService simpleProductionInStockDetailService;

    @Resource
    private com.ruoyi.service.basicData.BasicDocumentInfoService basicDocumentInfoService;

    /**
     * 查询简单生产入库单列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产入库单集合
     */
    public List<SimpleProductionInStockVo> querySimpleProductionInStockList(QueryParamVO queryParamVO) {
        return simpleProductionInStockMapper.querySimpleProductionInStock(queryParamVO);
    }

    /**
     * 从ERP同步简单生产入库单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP简单生产入库单...");

        // 1. 从ERP拉取所有简单生产入库单数据
        List<Map<String, Object>> erpDataList = fetchAllErpSimpleProductionInStockData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到简单生产入库单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到简单生产入库单", null);
        }
        log.info("从ERP查询到 {} 条简单生产入库单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpInStockGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.ID))));
        List<String> erpInStockIds = new ArrayList<>(erpInStockGrouped.keySet());

        // 3. 查询本地主表数据
        LambdaQueryWrapper<SimpleProductionInStock> mainWrapper = new LambdaQueryWrapper<>();
        mainWrapper.in(SimpleProductionInStock::getId, erpInStockIds);
        List<SimpleProductionInStock> localInStocks = this.list(mainWrapper);
        Map<String, SimpleProductionInStock> localInStocksMap = localInStocks.stream()
                .collect(Collectors.toMap(SimpleProductionInStock::getId, Function.identity()));

        // 4. 查询本地明细数据
        LambdaQueryWrapper<SimpleProductionInStockDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(SimpleProductionInStockDetail::getInStockId, erpInStockIds);
        List<SimpleProductionInStockDetail> localDetails = simpleProductionInStockDetailMapper.selectList(detailWrapper);
        Map<String, List<SimpleProductionInStockDetail>> localDetailsGrouped = localDetails.stream()
                .collect(Collectors.groupingBy(SimpleProductionInStockDetail::getInStockId));
        Map<String, SimpleProductionInStockDetail> localDetailsMap = localDetails.stream()
                .collect(Collectors.toMap(SimpleProductionInStockDetail::getId, detail -> detail, (existing, replacement) -> existing));

        // 5. 准备批量操作的数据集合
        List<SimpleProductionInStock> inStocksToInsert = new ArrayList<>();
        List<SimpleProductionInStock> inStocksToUpdate = new ArrayList<>();
        List<String> inStocksIdsToDelete = new ArrayList<>();
        List<SimpleProductionInStockDetail> detailsToInsert = new ArrayList<>();
        List<SimpleProductionInStockDetail> detailsToUpdate = new ArrayList<>();
        List<String> detailsIdsToDelete = new ArrayList<>();

        // 6. 比对ERP数据与本地数据
        for (Map.Entry<String, List<Map<String, Object>>> entry : erpInStockGrouped.entrySet()) {
            String inStockId = entry.getKey();
            List<Map<String, Object>> erpRows = entry.getValue();
            Map<String, Object> erpFirstRow = erpRows.get(0);

            // 比对主表信息
            SimpleProductionInStock erpInStock = mapToSimpleProductionInStock(erpFirstRow);
            SimpleProductionInStock localInStock = localInStocksMap.get(inStockId);

            if (localInStock == null) {
                // 新单据
                erpInStock.setCreateTime(new Date());
                inStocksToInsert.add(erpInStock);
            } else {
                // 检查是否需要更新
                erpInStock.setId(inStockId);
                if (!erpInStock.equals(localInStock)) {
                    erpInStock.setUpdateTime(new Date());
                    erpInStock.setCreateTime(localInStock.getCreateTime());
                    inStocksToUpdate.add(erpInStock);
                }
            }

            // 比对明细信息
            List<SimpleProductionInStockDetail> localInStockDetails = localDetailsGrouped.getOrDefault(inStockId, new ArrayList<>());
            Map<String, SimpleProductionInStockDetail> localInStockDetailsMap = localInStockDetails.stream()
                    .collect(Collectors.toMap(SimpleProductionInStockDetail::getId, Function.identity()));

            for (Map<String, Object> erpRow : erpRows) {
                SimpleProductionInStockDetail erpDetail = mapToSimpleProductionInStockDetail(erpRow);
                erpDetail.setInStockId(inStockId);
                SimpleProductionInStockDetail localDetail = localDetailsMap.get(erpDetail.getId());

                if (localDetail == null) {
                    // 新明细
                    detailsToInsert.add(erpDetail);
                } else {
                    // 检查是否需要更新
                    if (!erpDetail.equals(localDetail)) {
                        detailsToUpdate.add(erpDetail);
                    }
                }
                localInStockDetailsMap.remove(erpDetail.getId());
            }

            // 标记需要删除的明细（ERP中不存在但本地存在）
            detailsIdsToDelete.addAll(localInStockDetailsMap.keySet());
        }

        // 7. 标记需要删除的主表（ERP中不存在但本地存在）
        Set<String> erpInStockIdsSet = new HashSet<>(erpInStockIds);
        List<String> deletedInStockIds = localInStocks.stream()
                .map(SimpleProductionInStock::getId)
                .filter(id -> !erpInStockIdsSet.contains(id))
                .collect(Collectors.toList());

        if (!deletedInStockIds.isEmpty()) {
            inStocksIdsToDelete.addAll(deletedInStockIds);
            // 同时删除相关的明细
            LambdaQueryWrapper<SimpleProductionInStockDetail> detailDeleteWrapper = new LambdaQueryWrapper<>();
            detailDeleteWrapper.in(SimpleProductionInStockDetail::getInStockId, deletedInStockIds);
            List<SimpleProductionInStockDetail> detailListForDeletedInStocks = simpleProductionInStockDetailMapper.selectList(detailDeleteWrapper);
            if(!CollectionUtils.isEmpty(detailListForDeletedInStocks)){
                detailsIdsToDelete.addAll(detailListForDeletedInStocks.stream().map(SimpleProductionInStockDetail::getId).collect(Collectors.toList()));
            }
        }

        // 8. 批量执行数据库操作
        if (!inStocksToInsert.isEmpty()) {
            this.saveBatch(inStocksToInsert);
        }
        if (!inStocksToUpdate.isEmpty()) {
            this.updateBatchById(inStocksToUpdate);
        }
        if (!inStocksIdsToDelete.isEmpty()){
            this.removeByIds(inStocksIdsToDelete);
        }

        if (!detailsToInsert.isEmpty()) {
            simpleProductionInStockDetailService.saveBatch(detailsToInsert);
        }
        if (!detailsToUpdate.isEmpty()) {
            simpleProductionInStockDetailService.updateBatchById(detailsToUpdate);
        }
        if (!detailsIdsToDelete.isEmpty()) {
            simpleProductionInStockDetailService.removeByIds(detailsIdsToDelete);
        }

        // 9. 为新增简单生产入库单创建生产入库单据
        if (!inStocksToInsert.isEmpty()) {
            log.info("为新增简单生产入库单创建生产入库单据，数量：{}", inStocksToInsert.size());
            try {
                ResponseResult batchResult = basicDocumentInfoService.createDocumentFromSourceBatch(
                        CommonConstant.SourceDocumentType.SIMPLE_PRODUCTION_IN_STOCK,
                        inStocksToInsert,
                        CommonConstant.BoundType.SCRK,
                        CommonConstant.InoutType.IN);

                if (batchResult.getCode() == HttpStatus.SUCCESS) {
                    log.info("简单生产入库单创建WMS单据成功：{}", batchResult.getMsg());
                } else {
                    log.warn("简单生产入库单创建WMS单据失败：{}", batchResult.getMsg());
                }
            } catch (Exception e) {
                log.error("简单生产入库单创建WMS单据异常", e);
            }
        }

        // 10. 构建返回结果
        String summary = String.format("主表[新增:%d,更新:%d,删除:%d]，明细[新增:%d,更新:%d,删除:%d]",
                inStocksToInsert.size(), inStocksToUpdate.size(), inStocksIdsToDelete.size(),
                detailsToInsert.size(), detailsToUpdate.size(), detailsIdsToDelete.size());

        log.info("简单生产入库单同步完成：{}", summary);
        return ResponseResult.getSuccessResult("简单生产入库单同步完成：" + summary, null);
    }

    /**
     * 从ERP获取简单生产入库单数据
     */
    private List<Map<String, Object>> fetchAllErpSimpleProductionInStockData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.SIMPLE_PRODUCTION_IN_STOCK);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.DESCRIPTION,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.DATE,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.STOCK_ORG_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.PRD_ORG_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.OWNER_TYPE_ID,
                // Entry
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.SPECIFICATION,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.IN_STOCK_TYPE,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.BASE_UNIT_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MUST_QTY,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.BASE_MUST_QTY,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.REAL_QTY,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.BASE_REAL_QTY,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.OWNER_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.STOCK_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.WORKSHOP_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MEMO,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.STOCK_STATUS_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.KEEPER_TYPE_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.KEEPER_ID
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_SIMPLE_PRODUCTION_IN_STOCK);
    }

    /**
     * 将ERP数据映射为简单生产入库单主表对象
     */
    private SimpleProductionInStock mapToSimpleProductionInStock(Map<String, Object> map) {
        SimpleProductionInStock inStock = new SimpleProductionInStock();
        inStock.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.ID)));
        inStock.setBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.BILL_NO)));
        inStock.setDescription(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.DESCRIPTION)));
        inStock.setStockOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.STOCK_ORG_ID)));
        inStock.setPrdOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.PRD_ORG_ID)));
        inStock.setOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.OWNER_TYPE_ID)));

        // 处理日期字段
        Object dateObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Head.DATE);
        if (dateObj != null) {
            inStock.setInStockDate(parseDate(dateObj));
        }

        return inStock;
    }

    /**
     * 将ERP数据映射为简单生产入库单明细对象
     */
    private SimpleProductionInStockDetail mapToSimpleProductionInStockDetail(Map<String, Object> map) {
        SimpleProductionInStockDetail detail = new SimpleProductionInStockDetail();
        detail.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.ENTRY_ID)));
        detail.setMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MATERIAL_NAME)));
        detail.setSpecification(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.SPECIFICATION)));
        detail.setInStockType(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.IN_STOCK_TYPE)));
        detail.setUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.UNIT_ID)));
        detail.setBaseUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.BASE_UNIT_ID)));
        detail.setOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.OWNER_TYPE_ID)));
        detail.setOwnerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.OWNER_ID)));
        detail.setStockId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.STOCK_ID)));
        detail.setWorkshopId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.WORKSHOP_ID)));
        detail.setMemo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MEMO)));
        detail.setStockStatusId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.STOCK_STATUS_ID)));
        detail.setKeeperTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.KEEPER_TYPE_ID)));
        detail.setKeeperId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.KEEPER_ID)));

        // 处理数量字段
        Object mustQtyObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.MUST_QTY);
        if (mustQtyObj instanceof Number) {
            detail.setMustQty(new BigDecimal(mustQtyObj.toString()));
        }

        Object baseMustQtyObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.BASE_MUST_QTY);
        if (baseMustQtyObj instanceof Number) {
            detail.setBaseMustQty(new BigDecimal(baseMustQtyObj.toString()));
        }

        Object realQtyObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.REAL_QTY);
        if (realQtyObj instanceof Number) {
            detail.setRealQty(new BigDecimal(realQtyObj.toString()));
        }

        Object baseRealQtyObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionInStock.Entry.BASE_REAL_QTY);
        if (baseRealQtyObj instanceof Number) {
            detail.setBaseRealQty(new BigDecimal(baseRealQtyObj.toString()));
        }

        return detail;
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        String dateStr = dateObj.toString();
        if (dateStr.trim().isEmpty() || "null".equals(dateStr)) {
            return null;
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            logger.warn("日期解析失败: {}", dateStr, e);
            return null;
        }
    }
}
