package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.domain.bill.ProductionFeedMaterial;
import com.ruoyi.domain.bill.ProductionFeedMaterialDetail;
import com.ruoyi.mapper.bill.production.ProductionFeedMaterialDetailMapper;
import com.ruoyi.mapper.bill.production.ProductionFeedMaterialMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.ProductionFeedMaterialDetailVo;
import com.ruoyi.vo.bill.ProductionFeedMaterialVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产补料单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductionFeedMaterialService extends ServiceImpl<ProductionFeedMaterialMapper, ProductionFeedMaterial> {

    private static final Logger logger = LoggerFactory.getLogger(ProductionFeedMaterialService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private ProductionFeedMaterialMapper productionFeedMaterialMapper;

    @Resource
    private ProductionFeedMaterialDetailMapper productionFeedMaterialDetailMapper;
    
    @Resource
    private ProductionFeedMaterialDetailService productionFeedMaterialDetailService;

    @Resource
    private com.ruoyi.service.basicData.BasicDocumentInfoService basicDocumentInfoService;

    public List<ProductionFeedMaterialVo> queryProductionFeedMaterialList(QueryParamVO queryParamVO) {
        return productionFeedMaterialMapper.queryProductionFeedMaterial(queryParamVO);
    }

    /**
     * 查询生产补料单明细列表
     */
    public List<ProductionFeedMaterialDetailVo> queryProductionFeedMaterialDetailList(QueryParamVO queryParamVO) {
        return productionFeedMaterialDetailService.queryProductionFeedMaterialDetailList(queryParamVO);
    }

    /**
     * 从ERP同步生产补料单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP生产补料单...");

        // 1. 从ERP拉取所有生产补料单数据
        List<Map<String, Object>> erpDataList = fetchAllErpProductionFeedMaterialData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到生产补料单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到生产补料单", null);
        }
        log.info("从ERP查询到 {} 条生产补料单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpFeedGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.ID))));
        List<String> erpFeedIds = new ArrayList<>(erpFeedGrouped.keySet());

        log.info("ERP生产补料单按ID分组后共 {} 个单据", erpFeedIds.size());

        // 3. 查询本地已存在的生产补料单
        Map<String, ProductionFeedMaterial> existingFeedMap = new HashMap<>();
        if (!erpFeedIds.isEmpty()) {
            List<ProductionFeedMaterial> existingFeeds = this.listByIds(erpFeedIds);
            existingFeedMap = existingFeeds.stream()
                    .collect(Collectors.toMap(ProductionFeedMaterial::getId, Function.identity()));
        }

        // 4. 分别处理新增和更新
        List<ProductionFeedMaterial> toInsertFeeds = new ArrayList<>();
        List<ProductionFeedMaterial> toUpdateFeeds = new ArrayList<>();
        List<ProductionFeedMaterialDetail> toInsertDetails = new ArrayList<>();
        List<ProductionFeedMaterialDetail> toUpdateDetails = new ArrayList<>();

        for (String feedId : erpFeedIds) {
            List<Map<String, Object>> feedRows = erpFeedGrouped.get(feedId);
            if (feedRows.isEmpty()) continue;

            // 使用第一行数据构建主表信息
            Map<String, Object> firstRow = feedRows.get(0);
            ProductionFeedMaterial feed = mapToProductionFeedMaterial(firstRow);

            if (existingFeedMap.containsKey(feedId)) {
                toUpdateFeeds.add(feed);
            } else {
                toInsertFeeds.add(feed);
            }

            // 构建明细信息
            for (Map<String, Object> row : feedRows) {
                ProductionFeedMaterialDetail detail = mapToProductionFeedMaterialDetail(row, feedId);
                toInsertDetails.add(detail);
            }
        }

        // 5. 执行数据库操作
        int insertedFeeds = 0, updatedFeeds = 0, insertedDetails = 0;

        if (!toInsertFeeds.isEmpty()) {
            this.saveBatch(toInsertFeeds);
            insertedFeeds = toInsertFeeds.size();
            log.info("新增生产补料单 {} 条", insertedFeeds);
        }

        if (!toUpdateFeeds.isEmpty()) {
            this.updateBatchById(toUpdateFeeds);
            updatedFeeds = toUpdateFeeds.size();
            log.info("更新生产补料单 {} 条", updatedFeeds);
        }

        // 删除现有明细，重新插入（简化处理）
        if (!erpFeedIds.isEmpty()) {
            LambdaQueryWrapper<ProductionFeedMaterialDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(ProductionFeedMaterialDetail::getFeedId, erpFeedIds);
            productionFeedMaterialDetailService.remove(deleteWrapper);
        }

        if (!toInsertDetails.isEmpty()) {
            productionFeedMaterialDetailService.saveBatch(toInsertDetails);
            insertedDetails = toInsertDetails.size();
            log.info("新增生产补料单明细 {} 条", insertedDetails);
        }

        // 6. 为新增生产补料单创建生产出库单据
        if (!toInsertFeeds.isEmpty()) {
            log.info("为新增生产补料单创建生产出库单据，数量：{}", toInsertFeeds.size());
            try {
                ResponseResult batchResult = basicDocumentInfoService.createDocumentFromSourceBatch(
                        CommonConstant.SourceDocumentType.PRODUCTION_FEED_MATERIAL,
                        toInsertFeeds,
                        CommonConstant.BoundType.SCBL,
                        CommonConstant.InoutType.OUT);

                if (batchResult.getCode() == HttpStatus.SUCCESS) {
                    log.info("生产补料单创建WMS单据成功：{}", batchResult.getMsg());
                } else {
                    log.warn("生产补料单创建WMS单据失败：{}", batchResult.getMsg());
                }
            } catch (Exception e) {
                log.error("生产补料单创建WMS单据异常", e);
            }
        }

        // 7. 构建返回结果
        String summary = String.format("同步完成：新增生产补料单%d条，更新%d条，明细%d条",
                insertedFeeds, updatedFeeds, insertedDetails);
        log.info(summary);

        Map<String, Object> resultData = new HashMap<>();
        resultData.put("insertedFeeds", insertedFeeds);
        resultData.put("updatedFeeds", updatedFeeds);
        resultData.put("insertedDetails", insertedDetails);
        resultData.put("totalFeeds", insertedFeeds + updatedFeeds);
        resultData.put("totalDetails", insertedDetails);

        return ResponseResult.getSuccessResult(summary, resultData);
    }

    private List<Map<String, Object>> fetchAllErpProductionFeedMaterialData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.PRODUCTION_FEED_MATERIAL);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.DESCRIPTION,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.DATE,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.PRD_ORG_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.STOCK_ORG_ID,
                // Entry
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.STOCK_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.SPECIFICATION,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.STOCK_LOC_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PRODUCE_DATE,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MO_BILL_NO,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MO_ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PP_BOM_ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.APP_QTY,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ACTUAL_QTY,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_DESCRIPTION,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.SCRAP_QTY,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_WORKSHOP_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.KEEPER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.KEEPER_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.OWNER_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_SRC_BILL_TYPE,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_SRC_BILL_NO,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PARENT_OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PARENT_OWNER_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.BASE_UNIT_ID,
                CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.STOCK_UNIT_ID
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_PRODUCTION_FEED_MATERIAL);
    }

    private ProductionFeedMaterial mapToProductionFeedMaterial(Map<String, Object> map) {
        ProductionFeedMaterial feed = new ProductionFeedMaterial();
        feed.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.ID)));
        feed.setBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.BILL_NO)));
        feed.setDescription(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.DESCRIPTION)));
        feed.setPrdOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.PRD_ORG_ID)));
        feed.setStockOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.STOCK_ORG_ID)));

        // 处理日期
        Object dateObj = map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Head.DATE);
        if (dateObj != null && !dateObj.toString().equals("null")) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                feed.setFeedDate(sdf.parse(dateObj.toString()));
            } catch (ParseException e) {
                log.warn("解析生产补料单日期失败: {}", dateObj, e);
            }
        }

        feed.setCreateName("ERP同步");
        feed.setCreateTime(new Date());
        feed.setUpdateName("ERP同步");
        feed.setUpdateTime(new Date());

        return feed;
    }

    private ProductionFeedMaterialDetail mapToProductionFeedMaterialDetail(Map<String, Object> map, String feedId) {
        ProductionFeedMaterialDetail detail = new ProductionFeedMaterialDetail();
        detail.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_ID)));
        detail.setFeedId(feedId);
        detail.setStockId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.STOCK_ID)));
        detail.setMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MATERIAL_NAME)));
        detail.setSpecification(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.SPECIFICATION)));
        detail.setStockLocId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.STOCK_LOC_ID)));
        detail.setMoBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MO_BILL_NO)));
        detail.setMoEntryId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.MO_ENTRY_ID)));
        detail.setPpBomEntryId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PP_BOM_ENTRY_ID)));
        detail.setOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.OWNER_TYPE_ID)));
        detail.setEntryDescription(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_DESCRIPTION)));
        detail.setEntryWorkshopId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_WORKSHOP_ID)));
        detail.setKeeperTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.KEEPER_TYPE_ID)));
        detail.setKeeperId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.KEEPER_ID)));
        detail.setOwnerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.OWNER_ID)));
        detail.setEntrySrcBillType(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_SRC_BILL_TYPE)));
        detail.setEntrySrcBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ENTRY_SRC_BILL_NO)));
        detail.setParentOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PARENT_OWNER_TYPE_ID)));
        detail.setParentOwnerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PARENT_OWNER_ID)));
        detail.setUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.UNIT_ID)));
        detail.setBaseUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.BASE_UNIT_ID)));
        detail.setStockUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.STOCK_UNIT_ID)));

        // 处理生产日期
        Object produceDateObj = map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.PRODUCE_DATE);
        if (produceDateObj != null && !produceDateObj.toString().equals("null")) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                detail.setProduceDate(sdf.parse(produceDateObj.toString()));
            } catch (ParseException e) {
                log.warn("解析生产日期失败: {}", produceDateObj, e);
            }
        }

        // 处理数量字段
        Object appQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.APP_QTY);
        if (appQtyObj instanceof Number) {
            detail.setAppQty(new BigDecimal(appQtyObj.toString()));
        }

        Object actualQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.ACTUAL_QTY);
        if (actualQtyObj instanceof Number) {
            detail.setActualQty(new BigDecimal(actualQtyObj.toString()));
        }

        Object scrapQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionFeedMaterial.Entry.SCRAP_QTY);
        if (scrapQtyObj instanceof Number) {
            detail.setScrapQty(new BigDecimal(scrapQtyObj.toString()));
        }

        return detail;
    }}
