package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.domain.bill.ProductionPickingMaterial;
import com.ruoyi.domain.bill.ProductionPickingMaterialDetail;
import com.ruoyi.mapper.bill.production.ProductionPickingMaterialDetailMapper;
import com.ruoyi.mapper.bill.production.ProductionPickingMaterialMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.ProductionPickingMaterialDetailVo;
import com.ruoyi.vo.bill.ProductionPickingMaterialVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产领料单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductionPickingMaterialService extends ServiceImpl<ProductionPickingMaterialMapper, ProductionPickingMaterial> {

    private static final Logger logger = LoggerFactory.getLogger(ProductionPickingMaterialService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private ProductionPickingMaterialMapper productionPickingMaterialMapper;

    @Resource
    private ProductionPickingMaterialDetailMapper productionPickingMaterialDetailMapper;
    
    @Resource
    private ProductionPickingMaterialDetailService productionPickingMaterialDetailService;

    @Resource
    private com.ruoyi.service.basicData.BasicDocumentInfoService basicDocumentInfoService;

    public List<ProductionPickingMaterialVo> queryProductionPickingMaterialList(QueryParamVO queryParamVO) {
        return productionPickingMaterialMapper.queryProductionPickingMaterial(queryParamVO);
    }

    /**
     * 查询生产领料单明细列表
     */
    public List<ProductionPickingMaterialDetailVo> queryProductionPickingMaterialDetailList(QueryParamVO queryParamVO) {
        return productionPickingMaterialDetailService.queryProductionPickingMaterialDetailList(queryParamVO);
    }

    /**
     * 从ERP同步生产领料单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP生产领料单...");

        // 1. 从ERP拉取所有生产领料单数据
        List<Map<String, Object>> erpDataList = fetchAllErpProductionPickingMaterialData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到生产领料单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到生产领料单", null);
        }
        log.info("从ERP查询到 {} 条生产领料单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpPickingGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.ID))));
        List<String> erpPickingIds = new ArrayList<>(erpPickingGrouped.keySet());

        log.info("ERP生产领料单按ID分组后共 {} 个单据", erpPickingIds.size());

        // 3. 查询本地已存在的生产领料单
        Map<String, ProductionPickingMaterial> existingPickingMap = new HashMap<>();
        if (!erpPickingIds.isEmpty()) {
            List<ProductionPickingMaterial> existingPickings = this.listByIds(erpPickingIds);
            existingPickingMap = existingPickings.stream()
                    .collect(Collectors.toMap(ProductionPickingMaterial::getId, Function.identity()));
        }

        // 4. 分别处理新增和更新
        List<ProductionPickingMaterial> toInsertPickings = new ArrayList<>();
        List<ProductionPickingMaterial> toUpdatePickings = new ArrayList<>();
        List<ProductionPickingMaterialDetail> toInsertDetails = new ArrayList<>();
        List<ProductionPickingMaterialDetail> toUpdateDetails = new ArrayList<>();

        for (String pickingId : erpPickingIds) {
            List<Map<String, Object>> pickingRows = erpPickingGrouped.get(pickingId);
            if (pickingRows.isEmpty()) continue;

            // 使用第一行数据构建主表信息
            Map<String, Object> firstRow = pickingRows.get(0);
            ProductionPickingMaterial picking = mapToProductionPickingMaterial(firstRow);

            if (existingPickingMap.containsKey(pickingId)) {
                toUpdatePickings.add(picking);
            } else {
                toInsertPickings.add(picking);
            }

            // 构建明细信息
            for (Map<String, Object> row : pickingRows) {
                ProductionPickingMaterialDetail detail = mapToProductionPickingMaterialDetail(row, pickingId);
                toInsertDetails.add(detail);
            }
        }

        // 5. 执行数据库操作
        int insertedPickings = 0, updatedPickings = 0, insertedDetails = 0;

        if (!toInsertPickings.isEmpty()) {
            this.saveBatch(toInsertPickings);
            insertedPickings = toInsertPickings.size();
            log.info("新增生产领料单 {} 条", insertedPickings);
        }

        if (!toUpdatePickings.isEmpty()) {
            this.updateBatchById(toUpdatePickings);
            updatedPickings = toUpdatePickings.size();
            log.info("更新生产领料单 {} 条", updatedPickings);
        }

        // 删除现有明细，重新插入（简化处理）
        if (!erpPickingIds.isEmpty()) {
            LambdaQueryWrapper<ProductionPickingMaterialDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(ProductionPickingMaterialDetail::getPickingId, erpPickingIds);
            productionPickingMaterialDetailService.remove(deleteWrapper);
        }

        if (!toInsertDetails.isEmpty()) {
            productionPickingMaterialDetailService.saveBatch(toInsertDetails);
            insertedDetails = toInsertDetails.size();
            log.info("新增生产领料单明细 {} 条", insertedDetails);
        }

        // 6. 为新增生产领料单创建生产出库单据
        if (!toInsertPickings.isEmpty()) {
            log.info("为新增生产领料单创建生产出库单据，数量：{}", toInsertPickings.size());
            try {
                ResponseResult batchResult = basicDocumentInfoService.createDocumentFromSourceBatch(
                        CommonConstant.SourceDocumentType.PRODUCTION_PICKING_MATERIAL,
                        toInsertPickings,
                        CommonConstant.BoundType.SCLL,
                        CommonConstant.InoutType.OUT);

                if (batchResult.getCode() == HttpStatus.SUCCESS) {
                    log.info("生产领料单创建WMS单据成功：{}", batchResult.getMsg());
                } else {
                    log.warn("生产领料单创建WMS单据失败：{}", batchResult.getMsg());
                }
            } catch (Exception e) {
                log.error("生产领料单创建WMS单据异常", e);
            }
        }

        // 7. 构建返回结果
        String summary = String.format("同步完成：新增生产领料单%d条，更新%d条，明细%d条",
                insertedPickings, updatedPickings, insertedDetails);
        log.info(summary);

        Map<String, Object> resultData = new HashMap<>();
        resultData.put("insertedPickings", insertedPickings);
        resultData.put("updatedPickings", updatedPickings);
        resultData.put("insertedDetails", insertedDetails);
        resultData.put("totalPickings", insertedPickings + updatedPickings);
        resultData.put("totalDetails", insertedDetails);

        return ResponseResult.getSuccessResult(summary, resultData);
    }

    private List<Map<String, Object>> fetchAllErpProductionPickingMaterialData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.PRODUCTION_PICKING_MATERIAL);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.DESCRIPTION,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.DATE,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.PRD_ORG_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.STOCK_ORG_ID,
                // Entry
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.SPECIFICATION,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_LOC_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_STATUS_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_BILL_NO,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.PP_BOM_ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.APP_QTY,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ACTUAL_QTY,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ENTRY_MEMO,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_ENTRY_SEQ,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.BASE_UNIT_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_UNIT_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.KEEPER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.KEEPER_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.OWNER_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.PARENT_OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.PARENT_OWNER_ID,
                CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ENTRY_WORKSHOP_ID
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_PRODUCTION_PICKING_MATERIAL);
    }

    private ProductionPickingMaterial mapToProductionPickingMaterial(Map<String, Object> map) {
        ProductionPickingMaterial picking = new ProductionPickingMaterial();
        picking.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.ID)));
        picking.setBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.BILL_NO)));
        picking.setDescription(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.DESCRIPTION)));
        picking.setPrdOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.PRD_ORG_ID)));
        picking.setStockOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.STOCK_ORG_ID)));

        // 处理日期
        Object dateObj = map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Head.DATE);
        if (dateObj != null && !dateObj.toString().equals("null")) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                picking.setPickingDate(sdf.parse(dateObj.toString()));
            } catch (ParseException e) {
                log.warn("解析生产领料单日期失败: {}", dateObj, e);
            }
        }

        picking.setCreateName("ERP同步");
        picking.setCreateTime(new Date());
        picking.setUpdateName("ERP同步");
        picking.setUpdateTime(new Date());

        return picking;
    }

    private ProductionPickingMaterialDetail mapToProductionPickingMaterialDetail(Map<String, Object> map, String pickingId) {
        ProductionPickingMaterialDetail detail = new ProductionPickingMaterialDetail();
        detail.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ENTRY_ID)));
        detail.setPickingId(pickingId);
        detail.setMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MATERIAL_NAME)));
        detail.setSpecification(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.SPECIFICATION)));
        detail.setStockId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_ID)));
        detail.setStockLocId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_LOC_ID)));
        detail.setStockStatusId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_STATUS_ID)));
        detail.setMoBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_BILL_NO)));
        detail.setMoEntryId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_ENTRY_ID)));
        detail.setPpBomEntryId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.PP_BOM_ENTRY_ID)));
        detail.setOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.OWNER_TYPE_ID)));
        detail.setEntryMemo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ENTRY_MEMO)));
        detail.setMoId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_ID)));
        detail.setMoEntrySeq(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.MO_ENTRY_SEQ)));
        detail.setUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.UNIT_ID)));
        detail.setBaseUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.BASE_UNIT_ID)));
        detail.setStockUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.STOCK_UNIT_ID)));
        detail.setKeeperTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.KEEPER_TYPE_ID)));
        detail.setKeeperId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.KEEPER_ID)));
        detail.setOwnerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.OWNER_ID)));
        detail.setParentOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.PARENT_OWNER_TYPE_ID)));
        detail.setParentOwnerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.PARENT_OWNER_ID)));
        detail.setEntryWorkshopId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ENTRY_WORKSHOP_ID)));

        // 处理数量字段
        Object appQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.APP_QTY);
        if (appQtyObj instanceof Number) {
            detail.setAppQty(new BigDecimal(appQtyObj.toString()));
        }

        Object actualQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionPickingMaterial.Entry.ACTUAL_QTY);
        if (actualQtyObj instanceof Number) {
            detail.setActualQty(new BigDecimal(actualQtyObj.toString()));
        }

        return detail;
    }}
