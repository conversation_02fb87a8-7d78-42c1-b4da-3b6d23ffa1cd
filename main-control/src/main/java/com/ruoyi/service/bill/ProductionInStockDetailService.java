package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ProductionInStockDetail;
import com.ruoyi.mapper.bill.production.ProductionInStockDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionInStockDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产入库单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductionInStockDetailService extends ServiceImpl<ProductionInStockDetailMapper, ProductionInStockDetail> {

    @Resource
    private ProductionInStockDetailMapper productionInStockDetailMapper;

    /**
     * 查询生产入库单明细列表
     */
    public List<ProductionInStockDetailVo> queryProductionInStockDetailList(QueryParamVO queryParamVO) {
        return productionInStockDetailMapper.queryProductionInStockDetailVo(queryParamVO);
    }

    /**
     * 根据生产入库单ID查询明细列表（使用LambdaQueryWrapper）
     */
    public List<ProductionInStockDetail> getDetailsByInStockId(String inStockId) {
        return this.lambdaQuery()
                .eq(ProductionInStockDetail::getInStockId, inStockId)
                .list();
    }
}
