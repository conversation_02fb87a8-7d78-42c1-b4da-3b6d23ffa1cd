package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.SimpleProductionPickingMaterialDetail;
import com.ruoyi.mapper.bill.production.SimpleProductionPickingMaterialDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionPickingMaterialDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 简单生产领料单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class SimpleProductionPickingMaterialDetailService extends ServiceImpl<SimpleProductionPickingMaterialDetailMapper, SimpleProductionPickingMaterialDetail> {

    @Resource
    private SimpleProductionPickingMaterialDetailMapper simpleProductionPickingMaterialDetailMapper;

    /**
     * 查询简单生产领料单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产领料单明细集合
     */
    public List<SimpleProductionPickingMaterialDetailVo> querySimpleProductionPickingMaterialDetailList(QueryParamVO queryParamVO) {
        return simpleProductionPickingMaterialDetailMapper.querySimpleProductionPickingMaterialDetail(queryParamVO);
    }

    /**
     * 根据简单生产领料单ID查询明细列表（使用LambdaQueryWrapper）
     */
    public List<SimpleProductionPickingMaterialDetail> getDetailsByPickingId(String pickingId) {
        return this.lambdaQuery()
                .eq(SimpleProductionPickingMaterialDetail::getPickingId, pickingId)
                .list();
    }
}
