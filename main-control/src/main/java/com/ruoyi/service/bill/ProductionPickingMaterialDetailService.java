package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ProductionPickingMaterialDetail;
import com.ruoyi.mapper.bill.production.ProductionPickingMaterialDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionPickingMaterialDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产领料单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductionPickingMaterialDetailService extends ServiceImpl<ProductionPickingMaterialDetailMapper, ProductionPickingMaterialDetail> {

    @Resource
    private ProductionPickingMaterialDetailMapper productionPickingMaterialDetailMapper;

    /**
     * 查询生产领料单明细列表
     */
    public List<ProductionPickingMaterialDetailVo> queryProductionPickingMaterialDetailList(QueryParamVO queryParamVO) {
        return productionPickingMaterialDetailMapper.queryProductionPickingMaterialDetailVo(queryParamVO);
    }

    /**
     * 根据生产领料单ID查询明细列表（使用LambdaQueryWrapper）
     */
    public List<ProductionPickingMaterialDetail> getDetailsByPickingId(String pickingId) {
        return this.lambdaQuery()
                .eq(ProductionPickingMaterialDetail::getPickingId, pickingId)
                .list();
    }
}
