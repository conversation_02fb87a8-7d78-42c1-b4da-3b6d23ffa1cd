package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.ReturnNotice;
import com.ruoyi.domain.bill.ReturnNoticeDetail;
import com.ruoyi.mapper.bill.ReturnNoticeDetailMapper;
import com.ruoyi.mapper.bill.ReturnNoticeMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.ReturnNoticeDetailVo;
import com.ruoyi.vo.bill.ReturnNoticeVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退货通知单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReturnNoticeService extends ServiceImpl<ReturnNoticeMapper, ReturnNotice> {

    private static final Logger logger = LoggerFactory.getLogger(ReturnNoticeService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private ReturnNoticeMapper returnNoticeMapper;

    @Resource
    private ReturnNoticeDetailMapper returnNoticeDetailMapper;

    @Resource
    private ReturnNoticeDetailService returnNoticeDetailService;

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    /**
     * 查询退货通知单列表
     */
    public List<ReturnNoticeVo> queryReturnNoticeList(QueryParamVO queryParamVO) {
        return returnNoticeMapper.queryReturnNotice(queryParamVO);
    }

    /**
     * 查询退货通知单明细列表
     */
    public List<ReturnNoticeDetailVo> queryReturnNoticeDetail(QueryParamVO queryParamVO) {
        return returnNoticeDetailService.queryReturnNoticeDetailList(queryParamVO);
    }

    /**
     * 从ERP同步退货通知单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP退货通知单...");

        // 1. 从ERP拉取所有退货通知单数据
        List<Map<String, Object>> erpDataList = fetchAllErpReturnNoticeData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到退货通知单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到退货通知单", null);
        }
        log.info("从ERP查询到 {} 条退货通知单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpNoticesGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.ID))));
        List<String> erpNoticeIds = new ArrayList<>(erpNoticesGrouped.keySet());

        // 3. 查询现有的退货通知单
        Map<String, ReturnNotice> existingNoticesMap = new HashMap<>();
        if (!erpNoticeIds.isEmpty()) {
            List<ReturnNotice> existingNotices = this.lambdaQuery()
                    .in(ReturnNotice::getId, erpNoticeIds)
                    .list();
            existingNoticesMap = existingNotices.stream()
                    .collect(Collectors.toMap(ReturnNotice::getId, notice -> notice));
        }

        // 4. 准备数据集合
        List<ReturnNotice> toInsertNotices = new ArrayList<>();
        List<ReturnNotice> toUpdateNotices = new ArrayList<>();
        List<ReturnNoticeDetail> toInsertDetails = new ArrayList<>();

        // 5. 处理每个退货通知单
        for (String noticeId : erpNoticeIds) {
            List<Map<String, Object>> noticeRows = erpNoticesGrouped.get(noticeId);
            if (noticeRows.isEmpty()) continue;

            // 使用第一行数据构建主表信息
            Map<String, Object> firstRow = noticeRows.get(0);
            ReturnNotice notice = mapToReturnNotice(firstRow);

            if (existingNoticesMap.containsKey(noticeId)) {
                toUpdateNotices.add(notice);
            } else {
                toInsertNotices.add(notice);
            }

            // 构建明细信息
            for (Map<String, Object> row : noticeRows) {
                ReturnNoticeDetail detail = mapToReturnNoticeDetail(row, noticeId);
                toInsertDetails.add(detail);
            }
        }

        // 6. 执行数据库操作
        int insertedNotices = 0, updatedNotices = 0, insertedDetails = 0;

        if (!toInsertNotices.isEmpty()) {
            this.saveBatch(toInsertNotices);
            insertedNotices = toInsertNotices.size();
            log.info("新增退货通知单 {} 条", insertedNotices);
        }

        if (!toUpdateNotices.isEmpty()) {
            this.updateBatchById(toUpdateNotices);
            updatedNotices = toUpdateNotices.size();
            log.info("更新退货通知单 {} 条", updatedNotices);
        }

        // 删除现有明细，重新插入（简化处理）
        if (!erpNoticeIds.isEmpty()) {
            LambdaQueryWrapper<ReturnNoticeDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(ReturnNoticeDetail::getNoticeId, erpNoticeIds);
            returnNoticeDetailService.remove(deleteWrapper);
        }

        if (!toInsertDetails.isEmpty()) {
            returnNoticeDetailService.saveBatch(toInsertDetails);
            insertedDetails = toInsertDetails.size();
            log.info("新增退货通知单明细 {} 条", insertedDetails);
        }

        // 7. 生成销售退货入库单
        int generatedSaleReturnInStocks = 0;
        for (ReturnNotice notice : toInsertNotices) {
            try {
                basicDocumentInfoService.generateSaleReturnInStockFromReturnNotice(notice);
                generatedSaleReturnInStocks++;
            } catch (Exception e) {
                log.error("为退货通知单 {} 生成销售退货入库单失败: {}", notice.getBillNo(), e.getMessage());
            }
        }

        String summary = String.format("同步完成。退货通知单[新增:%d, 更新:%d], 明细[新增:%d], 生成销售退货入库单[%d]",
                insertedNotices, updatedNotices, insertedDetails, generatedSaleReturnInStocks);
        log.info(summary);

        return ResponseResult.getSuccessResult(summary, "");
    }

    /**
     * 从ERP获取退货通知单数据
     */
    private List<Map<String, Object>> fetchAllErpReturnNoticeData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.RETURN_NOTICE);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.DATE,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.SALE_ORG_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.RET_ORG_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.RET_DEPT_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.STOCKER_GROUP_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.STOCKER_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.RET_CUST_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.BILL_TYPE_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.BUSINESS_TYPE,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.RETURN_REASON,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.RECEIVE_ADDRESS,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.HEAD_LOC_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.DESCRIPTION,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.LINK_MAN,
                CommonConstant.ErpFieldKeys.ReturnNotice.Head.LINK_PHONE,
                // Entry
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.MATERIAL_MODEL,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.QTY,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.BASE_UNIT_ID,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.ENTRY_DESCRIPTION,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.ORDER_NO,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.LOT,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.RM_TYPE,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.DELIVERY_DATE,
                CommonConstant.ErpFieldKeys.ReturnNotice.Entry.UNIT_ID
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_RETURN_NOTICE);
    }

    /**
     * 将ERP数据映射为退货通知单实体
     */
    private ReturnNotice mapToReturnNotice(Map<String, Object> erpData) {
        ReturnNotice notice = new ReturnNotice();

        notice.setId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.ID)));
        notice.setBillNo(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.BILL_NO)));

        // 处理日期字段
        Object dateObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.DATE);
        if (dateObj != null) {
            notice.setDate(new Date(Long.parseLong(dateObj.toString())));
        }

        notice.setSaleOrgId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.SALE_ORG_ID)));
        notice.setRetOrgId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.RET_ORG_ID)));
        notice.setRetDeptId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.RET_DEPT_ID)));
        notice.setStockerGroupId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.STOCKER_GROUP_ID)));
        notice.setStockerId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.STOCKER_ID)));
        notice.setRetCustId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.RET_CUST_ID)));
        notice.setBillTypeId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.BILL_TYPE_ID)));
        notice.setBusinessType(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.BUSINESS_TYPE)));
        notice.setReturnReason(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.RETURN_REASON)));
        notice.setReceiveAddress(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.RECEIVE_ADDRESS)));
        notice.setHeadLocId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.HEAD_LOC_ID)));
        notice.setDescription(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.DESCRIPTION)));
        notice.setLinkMan(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.LINK_MAN)));
        notice.setLinkPhone(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Head.LINK_PHONE)));

        // 设置审计字段
        notice.setCreateTime(new Date());
        notice.setCreateName("ERP同步");
        notice.setUpdateTime(new Date());
        notice.setUpdateName("ERP同步");

        return notice;
    }

    /**
     * 将ERP数据映射为退货通知单明细实体
     */
    private ReturnNoticeDetail mapToReturnNoticeDetail(Map<String, Object> erpData, String noticeId) {
        ReturnNoticeDetail detail = new ReturnNoticeDetail();

        detail.setId(UUID.randomUUID().toString());
        detail.setNoticeId(noticeId);
        detail.setMaterialId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.MATERIAL_NAME)));
        detail.setMaterialModel(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.MATERIAL_MODEL)));

        // 处理数量字段
        Object qtyObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.QTY);
        if (qtyObj != null) {
            detail.setQty(new BigDecimal(qtyObj.toString()));
        }

        detail.setBaseUnitId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.BASE_UNIT_ID)));
        detail.setEntryDescription(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.ENTRY_DESCRIPTION)));
        detail.setOrderNo(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.ORDER_NO)));
        detail.setLot(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.LOT)));
        detail.setRmType(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.RM_TYPE)));

        // 处理退货日期
        Object deliveryDateObj = erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.DELIVERY_DATE);
        if (deliveryDateObj != null) {
            detail.setDeliveryDate(new Date(Long.parseLong(deliveryDateObj.toString())));
        }

        detail.setUnitId(String.valueOf(erpData.get(CommonConstant.ErpFieldKeys.ReturnNotice.Entry.UNIT_ID)));

        // 设置审计字段
        detail.setCreateTime(new Date());
        detail.setCreateName("ERP同步");
        detail.setUpdateTime(new Date());
        detail.setUpdateName("ERP同步");

        return detail;
    }
}
