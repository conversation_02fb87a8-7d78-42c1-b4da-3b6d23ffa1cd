package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.domain.bill.DeliveryNotice;
import com.ruoyi.domain.bill.DeliveryNoticeDetail;
import com.ruoyi.mapper.bill.delivery.DeliveryNoticeDetailMapper;
import com.ruoyi.mapper.bill.delivery.DeliveryNoticeMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.DeliveryNoticeDetailVo;
import com.ruoyi.vo.bill.DeliveryNoticeVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发货通知单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeliveryNoticeService extends ServiceImpl<DeliveryNoticeMapper, DeliveryNotice> {

    @Resource
    private ErpService erpService;

    @Resource
    private DeliveryNoticeMapper deliveryNoticeMapper;

    @Resource
    private DeliveryNoticeDetailMapper deliveryNoticeDetailMapper;
    
    @Resource
    private DeliveryNoticeDetailService deliveryNoticeDetailService;

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    public List<DeliveryNoticeVo> queryDeliveryNoticeList(QueryParamVO queryParamVO) {
        return deliveryNoticeMapper.queryDeliveryNotice(queryParamVO);
    }

    public List<DeliveryNoticeDetailVo> queryDeliveryNoticeDetail(QueryParamVO queryParamVO) {
        return deliveryNoticeDetailMapper.queryDeliveryNoticeDetailVo(queryParamVO);
    }

    /**
     * 从ERP同步发货通知单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP发货通知单...");

        // 1. 从ERP拉取所有发货通知单数据
        List<Map<String, Object>> erpDataList = fetchAllErpDeliveryNoticeData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到发货通知单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到发货通知单", null);
        }
        log.info("从ERP查询到 {} 条发货通知单行记录", erpDataList.size());

        // 2. 按通知单ID进行分组
        Map<String, List<Map<String, Object>>> erpNoticesGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.ID))));
        List<String> erpNoticeIds = new ArrayList<>(erpNoticesGrouped.keySet());

        // 3. 一次性获取本地相关数据
        List<DeliveryNotice> localNotices = this.listByIds(erpNoticeIds);
        Map<String, DeliveryNotice> localNoticesMap = localNotices.stream()
                .collect(Collectors.toMap(DeliveryNotice::getId, notice -> notice));

        LambdaQueryWrapper<DeliveryNoticeDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(DeliveryNoticeDetail::getNoticeId, erpNoticeIds);
        List<DeliveryNoticeDetail> localDetails = deliveryNoticeDetailMapper.selectList(detailWrapper);
        Map<String, List<DeliveryNoticeDetail>> localDetailsGrouped = localDetails.stream()
                .collect(Collectors.groupingBy(DeliveryNoticeDetail::getNoticeId));
        Map<String, DeliveryNoticeDetail> localDetailsMap = localDetails.stream()
                .collect(Collectors.toMap(DeliveryNoticeDetail::getId, detail -> detail, (existing, replacement) -> existing));

        // 4. 准备批量操作的列表
        List<DeliveryNotice> noticesToInsert = new ArrayList<>();
        List<DeliveryNotice> noticesToUpdate = new ArrayList<>();
        List<DeliveryNoticeDetail> detailsToInsert = new ArrayList<>();
        List<DeliveryNoticeDetail> detailsToUpdate = new ArrayList<>();
        List<String> detailsIdsToDelete = new ArrayList<>();

        // 5. 内存中比对数据
        for (Map.Entry<String, List<Map<String, Object>>> entry : erpNoticesGrouped.entrySet()) {
            String noticeId = entry.getKey();
            List<Map<String, Object>> erpRows = entry.getValue();
            Map<String, Object> erpFirstRow = erpRows.get(0);

            DeliveryNotice erpNotice = mapToDeliveryNotice(erpFirstRow);
            DeliveryNotice localNotice = localNoticesMap.get(noticeId);

            if (localNotice == null) {
                erpNotice.setCreateTime(new Date());
                noticesToInsert.add(erpNotice);
            } else {
                erpNotice.setId(noticeId);
                if (!erpNotice.equals(localNotice)) {
                    erpNotice.setUpdateTime(new Date());
                    erpNotice.setUpdateName("erp");
                    erpNotice.setCreateTime(localNotice.getCreateTime());
                    noticesToUpdate.add(erpNotice);
                }
            }

            List<DeliveryNoticeDetail> erpDetails = erpRows.stream()
                    .map(row -> mapToDeliveryNoticeDetail(row, noticeId))
                    .collect(Collectors.toList());
            Map<String, DeliveryNoticeDetail> erpDetailsMap = erpDetails.stream().collect(Collectors.toMap(DeliveryNoticeDetail::getId, d -> d));
            List<DeliveryNoticeDetail> localNoticeDetails = localDetailsGrouped.getOrDefault(noticeId, new ArrayList<>());

            for (DeliveryNoticeDetail erpDetail : erpDetails) {
                DeliveryNoticeDetail localDetail = localDetailsMap.get(erpDetail.getId());
                if (localDetail == null) {
                    detailsToInsert.add(erpDetail);
                } else {
                    if (!erpDetail.equals(localDetail)) {
                        erpDetail.setNoticeId(noticeId);
                        detailsToUpdate.add(erpDetail);
                    }
                }
            }

            for (DeliveryNoticeDetail localDetail : localNoticeDetails) {
                if (!erpDetailsMap.containsKey(localDetail.getId())) {
                    detailsIdsToDelete.add(localDetail.getId());
                }
            }
        }
        
        // 6. 处理在ERP中已删除的通知单
        List<String> noticesIdsToDelete = new ArrayList<>();
        LambdaQueryWrapper<DeliveryNotice> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(erpNoticeIds)) {
            queryWrapper.notIn(DeliveryNotice::getId, erpNoticeIds);
        }
        List<DeliveryNotice> deletedNotices = deliveryNoticeMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(deletedNotices)) {
            List<String> deletedNoticeIds = deletedNotices.stream().map(DeliveryNotice::getId).collect(Collectors.toList());
            noticesIdsToDelete.addAll(deletedNoticeIds);
            
            if (!deletedNoticeIds.isEmpty()) {
                LambdaQueryWrapper<DeliveryNoticeDetail> detailDeleteWrapper = new LambdaQueryWrapper<>();
                detailDeleteWrapper.in(DeliveryNoticeDetail::getNoticeId, deletedNoticeIds);
                List<DeliveryNoticeDetail> detailListForDeleted = deliveryNoticeDetailMapper.selectList(detailDeleteWrapper);
                if (!CollectionUtils.isEmpty(detailListForDeleted)) {
                    detailsIdsToDelete.addAll(detailListForDeleted.stream().map(DeliveryNoticeDetail::getId).collect(Collectors.toList()));
                }
            }
        }
        
        // 7. 批量执行DB操作
        if (!noticesToInsert.isEmpty()) this.saveBatch(noticesToInsert);
        if (!noticesToUpdate.isEmpty()) this.updateBatchById(noticesToUpdate);
        if (!noticesIdsToDelete.isEmpty()) this.removeByIds(noticesIdsToDelete);
        
        if (!detailsToInsert.isEmpty()) deliveryNoticeDetailService.saveBatch(detailsToInsert);
        if (!detailsToUpdate.isEmpty()) deliveryNoticeDetailService.updateBatchById(detailsToUpdate);
        if (!detailsIdsToDelete.isEmpty()) deliveryNoticeDetailService.removeByIds(detailsIdsToDelete);

        String summary = String.format("同步完成。发货通知单[新增:%d, 更新:%d, 删除:%d], 明细[新增:%d, 更新:%d, 删除:%d]",
                noticesToInsert.size(), noticesToUpdate.size(), noticesIdsToDelete.size(),
                detailsToInsert.size(), detailsToUpdate.size(), detailsIdsToDelete.size());
        log.info(summary);

        // 为新增发货通知单创建销售出库单据
        if (!noticesToInsert.isEmpty()) {
            log.info("为新增发货通知单创建销售出库单据，数量：{}", noticesToInsert.size());
            try {
                ResponseResult batchResult = basicDocumentInfoService.createDocumentFromSourceBatch(
                        CommonConstant.SourceDocumentType.DELIVERY_NOTICE,
                        noticesToInsert,
                        CommonConstant.BoundType.XSCK,
                        CommonConstant.InoutType.OUT);

                if (batchResult.getCode() == HttpStatus.SUCCESS) {
                    log.info("销售出库单据创建成功");
                    summary += String.format("，出库单据[成功:%d]", noticesToInsert.size());
                } else {
                    log.warn("销售出库单据创建失败：{}", batchResult.getMsg());
                    summary += String.format("，出库单据[失败:%d]", noticesToInsert.size());
                }
            } catch (Exception e) {
                log.error("销售出库单据创建异常", e);
                summary += String.format("，出库单据[异常:%d]", noticesToInsert.size());
            }
        }

        return ResponseResult.getSuccessResult(summary, "");
    }

    private List<Map<String, Object>> fetchAllErpDeliveryNoticeData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.DELIVERY_NOTICE);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SALESMAN_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SALE_DEPT_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.CUSTOMER_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.DELIVERY_WAY,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.DATE,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SETTLE_CURR_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.STOCKER_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.RECEIVER_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SETTLE_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.RECEIVER_CONTACT_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.PAYER_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Head.RECEIVE_ADDRESS,
                // Entry
                CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.MATERIAL_CODE,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.PARENT_MATERIAL_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.QTY,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.DELIVERY_DATE,
                CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.MATERIAL_TYPE
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, "发货通知单");
    }

    private DeliveryNotice mapToDeliveryNotice(Map<String, Object> map) {
        DeliveryNotice notice = new DeliveryNotice();
        notice.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.ID)));
        notice.setBillNo((String) map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.BILL_NO));
        notice.setSalesmanId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SALESMAN_ID)));
        notice.setSaleDeptId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SALE_DEPT_ID)));
        notice.setCustomerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.CUSTOMER_ID)));
        notice.setDeliveryWay((String) map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.DELIVERY_WAY));
        notice.setNoticeDate(parseDate(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.DATE)));
        notice.setSettleCurrId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SETTLE_CURR_ID)));
        notice.setStockerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.STOCKER_ID)));
        notice.setReceiverId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.RECEIVER_ID)));
        notice.setSettleId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.SETTLE_ID)));
        notice.setReceiverContactId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.RECEIVER_CONTACT_ID)));
        notice.setPayerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.PAYER_ID)));
        notice.setReceiveAddress((String) map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Head.RECEIVE_ADDRESS));
        notice.setCreateName("erp");
        return notice;
    }

    private DeliveryNoticeDetail mapToDeliveryNoticeDetail(Map<String, Object> map, String noticeId) {
        DeliveryNoticeDetail detail = new DeliveryNoticeDetail();
        detail.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.ENTRY_ID)));
        detail.setNoticeId(noticeId);
        detail.setMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.MATERIAL_CODE)));
        detail.setParentMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.PARENT_MATERIAL_ID)));
        detail.setUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.UNIT_ID)));
        Object qtyObj = map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.QTY);
        if (qtyObj instanceof Number) {
            detail.setQty(new BigDecimal(((Number) qtyObj).doubleValue()));
        } else if (qtyObj != null) {
            detail.setQty(new BigDecimal(qtyObj.toString()));
        }
        detail.setDeliveryDate(parseDate(map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.DELIVERY_DATE)));
        detail.setMaterialType((String) map.get(CommonConstant.ErpFieldKeys.DeliveryNotice.Entry.MATERIAL_TYPE));
        return detail;
    }

    private Date parseDate(Object dateObj) {
        if (dateObj == null) return null;
        if (dateObj instanceof Date) return (Date) dateObj;
        if (dateObj instanceof String) {
            String dateStr = (String) dateObj;
            try {
                return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(dateStr);
            } catch (ParseException e) {
                try {
                    return new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
                } catch (ParseException e2) {
                    log.error("无法解析日期字符串: {}", dateStr, e2);
                    return null;
                }
            }
        }
        return null;
    }
}