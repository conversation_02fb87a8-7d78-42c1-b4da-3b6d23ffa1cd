package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.domain.bill.SimpleProductionPickingMaterial;
import com.ruoyi.domain.bill.SimpleProductionPickingMaterialDetail;
import com.ruoyi.mapper.bill.production.SimpleProductionPickingMaterialDetailMapper;
import com.ruoyi.mapper.bill.production.SimpleProductionPickingMaterialMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.SimpleProductionPickingMaterialVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 简单生产领料单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class SimpleProductionPickingMaterialService extends ServiceImpl<SimpleProductionPickingMaterialMapper, SimpleProductionPickingMaterial> {

    private static final Logger logger = LoggerFactory.getLogger(SimpleProductionPickingMaterialService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private SimpleProductionPickingMaterialMapper simpleProductionPickingMaterialMapper;

    @Resource
    private SimpleProductionPickingMaterialDetailMapper simpleProductionPickingMaterialDetailMapper;
    
    @Resource
    private SimpleProductionPickingMaterialDetailService simpleProductionPickingMaterialDetailService;

    @Resource
    private com.ruoyi.service.basicData.BasicDocumentInfoService basicDocumentInfoService;

    /**
     * 查询简单生产领料单列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产领料单集合
     */
    public List<SimpleProductionPickingMaterialVo> querySimpleProductionPickingMaterialList(QueryParamVO queryParamVO) {
        return simpleProductionPickingMaterialMapper.querySimpleProductionPickingMaterial(queryParamVO);
    }

    /**
     * 从ERP同步简单生产领料单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP简单生产领料单...");

        // 1. 从ERP拉取所有简单生产领料单数据
        List<Map<String, Object>> erpDataList = fetchAllErpSimpleProductionPickingMaterialData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到简单生产领料单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到简单生产领料单", null);
        }
        log.info("从ERP查询到 {} 条简单生产领料单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpPickingGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.ID))));
        List<String> erpPickingIds = new ArrayList<>(erpPickingGrouped.keySet());

        // 3. 查询本地主表数据
        LambdaQueryWrapper<SimpleProductionPickingMaterial> mainWrapper = new LambdaQueryWrapper<>();
        mainWrapper.in(SimpleProductionPickingMaterial::getId, erpPickingIds);
        List<SimpleProductionPickingMaterial> localPickings = this.list(mainWrapper);
        Map<String, SimpleProductionPickingMaterial> localPickingsMap = localPickings.stream()
                .collect(Collectors.toMap(SimpleProductionPickingMaterial::getId, Function.identity()));

        // 4. 查询本地明细数据
        LambdaQueryWrapper<SimpleProductionPickingMaterialDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(SimpleProductionPickingMaterialDetail::getPickingId, erpPickingIds);
        List<SimpleProductionPickingMaterialDetail> localDetails = simpleProductionPickingMaterialDetailMapper.selectList(detailWrapper);
        Map<String, List<SimpleProductionPickingMaterialDetail>> localDetailsGrouped = localDetails.stream()
                .collect(Collectors.groupingBy(SimpleProductionPickingMaterialDetail::getPickingId));
        Map<String, SimpleProductionPickingMaterialDetail> localDetailsMap = localDetails.stream()
                .collect(Collectors.toMap(SimpleProductionPickingMaterialDetail::getId, detail -> detail, (existing, replacement) -> existing));

        // 5. 准备批量操作的数据集合
        List<SimpleProductionPickingMaterial> pickingsToInsert = new ArrayList<>();
        List<SimpleProductionPickingMaterial> pickingsToUpdate = new ArrayList<>();
        List<String> pickingsIdsToDelete = new ArrayList<>();
        List<SimpleProductionPickingMaterialDetail> detailsToInsert = new ArrayList<>();
        List<SimpleProductionPickingMaterialDetail> detailsToUpdate = new ArrayList<>();
        List<String> detailsIdsToDelete = new ArrayList<>();

        // 6. 比对ERP数据与本地数据
        for (Map.Entry<String, List<Map<String, Object>>> entry : erpPickingGrouped.entrySet()) {
            String pickingId = entry.getKey();
            List<Map<String, Object>> erpRows = entry.getValue();
            Map<String, Object> erpFirstRow = erpRows.get(0);

            // 比对主表信息
            SimpleProductionPickingMaterial erpPicking = mapToSimpleProductionPickingMaterial(erpFirstRow);
            SimpleProductionPickingMaterial localPicking = localPickingsMap.get(pickingId);

            if (localPicking == null) {
                // 新单据
                erpPicking.setCreateTime(new Date());
                pickingsToInsert.add(erpPicking);
            } else {
                // 检查是否需要更新
                erpPicking.setId(pickingId);
                if (!erpPicking.equals(localPicking)) {
                    erpPicking.setUpdateTime(new Date());
                    erpPicking.setCreateTime(localPicking.getCreateTime());
                    pickingsToUpdate.add(erpPicking);
                }
            }

            // 比对明细信息
            List<SimpleProductionPickingMaterialDetail> localPickingDetails = localDetailsGrouped.getOrDefault(pickingId, new ArrayList<>());
            Map<String, SimpleProductionPickingMaterialDetail> localPickingDetailsMap = localPickingDetails.stream()
                    .collect(Collectors.toMap(SimpleProductionPickingMaterialDetail::getId, Function.identity()));

            for (Map<String, Object> erpRow : erpRows) {
                SimpleProductionPickingMaterialDetail erpDetail = mapToSimpleProductionPickingMaterialDetail(erpRow);
                erpDetail.setPickingId(pickingId);
                SimpleProductionPickingMaterialDetail localDetail = localDetailsMap.get(erpDetail.getId());

                if (localDetail == null) {
                    // 新明细
                    detailsToInsert.add(erpDetail);
                } else {
                    // 检查是否需要更新
                    if (!erpDetail.equals(localDetail)) {
                        detailsToUpdate.add(erpDetail);
                    }
                }
                localPickingDetailsMap.remove(erpDetail.getId());
            }

            // 标记需要删除的明细（ERP中不存在但本地存在）
            detailsIdsToDelete.addAll(localPickingDetailsMap.keySet());
        }

        // 7. 标记需要删除的主表（ERP中不存在但本地存在）
        Set<String> erpPickingIdsSet = new HashSet<>(erpPickingIds);
        List<String> deletedPickingIds = localPickings.stream()
                .map(SimpleProductionPickingMaterial::getId)
                .filter(id -> !erpPickingIdsSet.contains(id))
                .collect(Collectors.toList());

        if (!deletedPickingIds.isEmpty()) {
            pickingsIdsToDelete.addAll(deletedPickingIds);
            // 同时删除相关的明细
            LambdaQueryWrapper<SimpleProductionPickingMaterialDetail> detailDeleteWrapper = new LambdaQueryWrapper<>();
            detailDeleteWrapper.in(SimpleProductionPickingMaterialDetail::getPickingId, deletedPickingIds);
            List<SimpleProductionPickingMaterialDetail> detailListForDeletedPickings = simpleProductionPickingMaterialDetailMapper.selectList(detailDeleteWrapper);
            if(!CollectionUtils.isEmpty(detailListForDeletedPickings)){
                detailsIdsToDelete.addAll(detailListForDeletedPickings.stream().map(SimpleProductionPickingMaterialDetail::getId).collect(Collectors.toList()));
            }
        }

        // 8. 批量执行数据库操作
        if (!pickingsToInsert.isEmpty()) {
            this.saveBatch(pickingsToInsert);
        }
        if (!pickingsToUpdate.isEmpty()) {
            this.updateBatchById(pickingsToUpdate);
        }
        if (!pickingsIdsToDelete.isEmpty()){
            this.removeByIds(pickingsIdsToDelete);
        }

        if (!detailsToInsert.isEmpty()) {
            simpleProductionPickingMaterialDetailService.saveBatch(detailsToInsert);
        }
        if (!detailsToUpdate.isEmpty()) {
            simpleProductionPickingMaterialDetailService.updateBatchById(detailsToUpdate);
        }
        if (!detailsIdsToDelete.isEmpty()) {
            simpleProductionPickingMaterialDetailService.removeByIds(detailsIdsToDelete);
        }

        // 9. 为新增简单生产领料单创建生产出库单据
        if (!pickingsToInsert.isEmpty()) {
            log.info("为新增简单生产领料单创建生产出库单据，数量：{}", pickingsToInsert.size());
            try {
                ResponseResult batchResult = basicDocumentInfoService.createDocumentFromSourceBatch(
                        CommonConstant.SourceDocumentType.SIMPLE_PRODUCTION_PICKING_MATERIAL,
                        pickingsToInsert,
                        CommonConstant.BoundType.SCLL,
                        CommonConstant.InoutType.OUT);

                if (batchResult.getCode() == HttpStatus.SUCCESS) {
                    log.info("简单生产领料单创建WMS单据成功：{}", batchResult.getMsg());
                } else {
                    log.warn("简单生产领料单创建WMS单据失败：{}", batchResult.getMsg());
                }
            } catch (Exception e) {
                log.error("简单生产领料单创建WMS单据异常", e);
            }
        }

        // 10. 构建返回结果
        String summary = String.format("主表[新增:%d,更新:%d,删除:%d]，明细[新增:%d,更新:%d,删除:%d]",
                pickingsToInsert.size(), pickingsToUpdate.size(), pickingsIdsToDelete.size(),
                detailsToInsert.size(), detailsToUpdate.size(), detailsIdsToDelete.size());

        log.info("简单生产领料单同步完成：{}", summary);
        return ResponseResult.getSuccessResult("简单生产领料单同步完成：" + summary, null);
    }

    /**
     * 从ERP获取简单生产领料单数据
     */
    private List<Map<String, Object>> fetchAllErpSimpleProductionPickingMaterialData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.SIMPLE_PRODUCTION_PICKING_MATERIAL);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.DESCRIPTION,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.DATE,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.STOCK_ORG_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.PRD_ORG_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.WORKSHOP_ID,
                // Entry
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.SPECIFICATION,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.STOCK_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.STOCK_STATUS_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.APP_QTY,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.ACTUAL_QTY,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.ENTRY_MEMO,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.BASE_UNIT_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.STOCK_UNIT_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.KEEPER_TYPE_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.KEEPER_ID,
                CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.OWNER_ID
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_SIMPLE_PRODUCTION_PICKING_MATERIAL);
    }

    /**
     * 将ERP数据映射为简单生产领料单主表对象
     */
    private SimpleProductionPickingMaterial mapToSimpleProductionPickingMaterial(Map<String, Object> map) {
        SimpleProductionPickingMaterial picking = new SimpleProductionPickingMaterial();
        picking.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.ID)));
        picking.setBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.BILL_NO)));
        picking.setDescription(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.DESCRIPTION)));
        picking.setStockOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.STOCK_ORG_ID)));
        picking.setPrdOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.PRD_ORG_ID)));
        picking.setOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.OWNER_TYPE_ID)));
        picking.setWorkshopId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.WORKSHOP_ID)));

        // 处理日期字段
        Object dateObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Head.DATE);
        if (dateObj != null) {
            picking.setPickingDate(parseDate(dateObj));
        }

        return picking;
    }

    /**
     * 将ERP数据映射为简单生产领料单明细对象
     */
    private SimpleProductionPickingMaterialDetail mapToSimpleProductionPickingMaterialDetail(Map<String, Object> map) {
        SimpleProductionPickingMaterialDetail detail = new SimpleProductionPickingMaterialDetail();
        detail.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.ENTRY_ID)));
        detail.setMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.MATERIAL_NAME)));
        detail.setSpecification(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.SPECIFICATION)));
        detail.setStockId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.STOCK_ID)));
        detail.setStockStatusId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.STOCK_STATUS_ID)));
        detail.setOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.OWNER_TYPE_ID)));
        detail.setEntryMemo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.ENTRY_MEMO)));
        detail.setUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.UNIT_ID)));
        detail.setBaseUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.BASE_UNIT_ID)));
        detail.setStockUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.STOCK_UNIT_ID)));
        detail.setKeeperTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.KEEPER_TYPE_ID)));
        detail.setKeeperId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.KEEPER_ID)));
        detail.setOwnerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.OWNER_ID)));

        // 处理数量字段
        Object appQtyObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.APP_QTY);
        if (appQtyObj instanceof Number) {
            detail.setAppQty(new BigDecimal(appQtyObj.toString()));
        }

        Object actualQtyObj = map.get(CommonConstant.ErpFieldKeys.SimpleProductionPickingMaterial.Entry.ACTUAL_QTY);
        if (actualQtyObj instanceof Number) {
            detail.setActualQty(new BigDecimal(actualQtyObj.toString()));
        }

        return detail;
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        String dateStr = dateObj.toString();
        if (dateStr.trim().isEmpty() || "null".equals(dateStr)) {
            return null;
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            logger.warn("日期解析失败: {}", dateStr, e);
            return null;
        }
    }
}
