package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.SimpleProductionInStockDetail;
import com.ruoyi.mapper.bill.production.SimpleProductionInStockDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionInStockDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 简单生产入库单明细服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class SimpleProductionInStockDetailService extends ServiceImpl<SimpleProductionInStockDetailMapper, SimpleProductionInStockDetail> {

    @Resource
    private SimpleProductionInStockDetailMapper simpleProductionInStockDetailMapper;

    /**
     * 查询简单生产入库单明细列表
     *
     * @param queryParamVO 查询参数
     * @return 简单生产入库单明细集合
     */
    public List<SimpleProductionInStockDetailVo> querySimpleProductionInStockDetailList(QueryParamVO queryParamVO) {
        return simpleProductionInStockDetailMapper.querySimpleProductionInStockDetail(queryParamVO);
    }

    /**
     * 根据简单生产入库单ID查询明细列表（使用LambdaQueryWrapper）
     */
    public List<SimpleProductionInStockDetail> getDetailsByInStockId(String inStockId) {
        return this.lambdaQuery()
                .eq(SimpleProductionInStockDetail::getInStockId, inStockId)
                .list();
    }
}
