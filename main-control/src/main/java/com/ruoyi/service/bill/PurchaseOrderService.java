package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.domain.bill.PurchaseOrder;
import com.ruoyi.domain.bill.PurchaseOrderDetail;
import com.ruoyi.mapper.bill.purchase.PurchaseOrderDetailMapper;
import com.ruoyi.mapper.bill.purchase.PurchaseOrderMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.PurchaseOrderDetailVo;
import com.ruoyi.vo.bill.PurchaseOrderVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购订单服务
 */
@Slf4j
@Service
public class PurchaseOrderService extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder> {

    private static final Logger logger = LoggerFactory.getLogger(PurchaseOrderService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    private PurchaseOrderDetailService purchaseOrderDetailService;

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    public List<PurchaseOrderVo> queryPurchaseOrderList(QueryParamVO queryParamVO) {
        return purchaseOrderMapper.queryPurchaseOrder(queryParamVO);
    }

    public List<PurchaseOrderDetailVo> queryPurchaseOrderDetail(QueryParamVO queryParamVO) {
        return purchaseOrderDetailMapper.queryPurchaseOrderDetailVo(queryParamVO);
    }

    /**
     * 从ERP同步采购订单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        logger.info("开始同步ERP采购订单...");

        // 从ERP获取采购订单数据
        List<Map<String, Object>> erpDataList = fetchAllErpPurchaseData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            logger.info("ERP中未查询到采购订单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到采购订单", null);
        }
        logger.info("从ERP查询到 {} 条采购订单行记录", erpDataList.size());

        // 按订单ID分组ERP数据
        Map<String, List<Map<String, Object>>> erpOrdersGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.ID))));
        List<String> erpOrderIds = new ArrayList<>(erpOrdersGrouped.keySet());

        // 查询本地已有的订单数据
        List<PurchaseOrder> localOrders = this.listByIds(erpOrderIds);
        Map<String, PurchaseOrder> localOrdersMap = localOrders.stream()
                .collect(Collectors.toMap(PurchaseOrder::getId, order -> order));

        // 查询本地订单明细数据
        LambdaQueryWrapper<PurchaseOrderDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.in(PurchaseOrderDetail::getOrderId, erpOrderIds);
        List<PurchaseOrderDetail> localDetails = purchaseOrderDetailMapper.selectList(detailWrapper);
        Map<String, List<PurchaseOrderDetail>> localDetailsGrouped = localDetails.stream()
                .collect(Collectors.groupingBy(PurchaseOrderDetail::getOrderId));
        Map<String, PurchaseOrderDetail> localDetailsMap = localDetails.stream()
                .collect(Collectors.toMap(PurchaseOrderDetail::getId, detail -> detail, (existing, replacement) -> existing));

        // 准备批量操作的数据列表
        List<PurchaseOrder> ordersToInsert = new ArrayList<>();
        List<PurchaseOrder> ordersToUpdate = new ArrayList<>();
        List<PurchaseOrderDetail> detailsToInsert = new ArrayList<>();
        List<PurchaseOrderDetail> detailsToUpdate = new ArrayList<>();
        List<String> detailsIdsToDelete = new ArrayList<>();

        // 比对ERP数据与本地数据
        for (Map.Entry<String, List<Map<String, Object>>> entry : erpOrdersGrouped.entrySet()) {
            String orderId = entry.getKey();
            List<Map<String, Object>> erpRows = entry.getValue();
            Map<String, Object> erpFirstRow = erpRows.get(0);

            // 比对订单主信息
            PurchaseOrder erpOrder = mapToPurchaseOrder(erpFirstRow);
            PurchaseOrder localOrder = localOrdersMap.get(orderId);

            if (localOrder == null) {
                // 新订单
                erpOrder.setCreateTime(new Date());
                ordersToInsert.add(erpOrder);
            } else {
                // 检查是否需要更新
                erpOrder.setId(orderId);
                if (!erpOrder.equals(localOrder)) {
                    erpOrder.setUpdateTime(new Date());
                    erpOrder.setCreateTime(localOrder.getCreateTime());
                    ordersToUpdate.add(erpOrder);
                }
            }

            // 比对订单明细
            List<PurchaseOrderDetail> erpDetails = erpRows.stream()
                    .map(row -> mapToPurchaseOrderDetail(row, orderId))
                    .collect(Collectors.toList());
            Map<String, PurchaseOrderDetail> erpDetailsMap = erpDetails.stream().collect(Collectors.toMap(PurchaseOrderDetail::getId, d -> d));

            List<PurchaseOrderDetail> localOrderDetails = localDetailsGrouped.getOrDefault(orderId, new ArrayList<>());

            // 检查新增和更新的明细
            for(PurchaseOrderDetail erpDetail : erpDetails) {
                PurchaseOrderDetail localDetail = localDetailsMap.get(erpDetail.getId());
                if (localDetail == null) {
                    detailsToInsert.add(erpDetail);
                } else {
                     if (!erpDetail.equals(localDetail)) {
                        erpDetail.setOrderId(orderId);
                        detailsToUpdate.add(erpDetail);
                    }
                }
            }

            // 检查需要删除的明细
            for (PurchaseOrderDetail localDetail : localOrderDetails) {
                if (!erpDetailsMap.containsKey(localDetail.getId())) {
                    detailsIdsToDelete.add(localDetail.getId());
                }
            }
        }

        // 处理ERP中已删除的订单
        List<String> ordersIdsToDelete = new ArrayList<>();
        LambdaQueryWrapper<PurchaseOrder> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(erpOrderIds)) {
            queryWrapper.notIn(PurchaseOrder::getId, erpOrderIds);
        }
        List<PurchaseOrder> deletedOrders = purchaseOrderMapper.selectList(queryWrapper);
        if(!CollectionUtils.isEmpty(deletedOrders)){
            List<String> deletedOrderIds = deletedOrders.stream().map(PurchaseOrder::getId).collect(Collectors.toList());
            ordersIdsToDelete.addAll(deletedOrderIds);

            // 同时删除相关的订单明细
            if (!deletedOrderIds.isEmpty()) {
                LambdaQueryWrapper<PurchaseOrderDetail> detailDeleteWrapper = new LambdaQueryWrapper<>();
                detailDeleteWrapper.in(PurchaseOrderDetail::getOrderId, deletedOrderIds);
                List<PurchaseOrderDetail> detailListForDeletedOrders = purchaseOrderDetailMapper.selectList(detailDeleteWrapper);
                if(!CollectionUtils.isEmpty(detailListForDeletedOrders)){
                    detailsIdsToDelete.addAll(detailListForDeletedOrders.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList()));
                }
            }
        }

        // 批量执行数据库操作
        if (!ordersToInsert.isEmpty()) {
            this.saveBatch(ordersToInsert);
        }
        if (!ordersToUpdate.isEmpty()) {
            this.updateBatchById(ordersToUpdate);
        }
        if (!ordersIdsToDelete.isEmpty()){
            this.removeByIds(ordersIdsToDelete);
        }

        if (!detailsToInsert.isEmpty()) {
            purchaseOrderDetailService.saveBatch(detailsToInsert);
        }
        if (!detailsToUpdate.isEmpty()) {
            purchaseOrderDetailService.updateBatchById(detailsToUpdate);
        }
        if (!detailsIdsToDelete.isEmpty()) {
            purchaseOrderDetailService.removeByIds(detailsIdsToDelete);
        }

        String summary = String.format("同步完成。订单[新增:%d, 更新:%d, 删除:%d], 明细[新增:%d, 更新:%d, 删除:%d]",
                ordersToInsert.size(), ordersToUpdate.size(), ordersIdsToDelete.size(),
                detailsToInsert.size(), detailsToUpdate.size(), detailsIdsToDelete.size());
        logger.info(summary);

        // 为新增订单创建入库单据
        if (!ordersToInsert.isEmpty()) {
            logger.info("为新增采购订单创建入库单据，数量：{}", ordersToInsert.size());
            try {
                ResponseResult batchResult = basicDocumentInfoService.createDocumentFromSourceBatch(
                        CommonConstant.SourceDocumentType.PURCHASE_ORDER,
                        ordersToInsert,
                        CommonConstant.BoundType.CGRK,
                        CommonConstant.InoutType.IN);

                if (batchResult.getCode() == HttpStatus.SUCCESS) {
                    logger.info("入库单据创建成功");
                    summary += String.format("，入库单据[成功:%d]", ordersToInsert.size());
                } else {
                    logger.warn("入库单据创建失败：{}", batchResult.getMsg());
                    summary += String.format("，入库单据[失败:%d]", ordersToInsert.size());
                }
            } catch (Exception e) {
                logger.error("入库单据创建异常", e);
                summary += String.format("，入库单据[异常:%d]", ordersToInsert.size());
            }
        }

        return ResponseResult.getSuccessResult(summary, "");
    }

    private List<Map<String, Object>> fetchAllErpPurchaseData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.PURCHASE_ORDER);
        queryReq.setFieldKeys(String.join(",",
                // 主表字段
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.DATE,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.SUPPLIER_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PURCHASE_DEPT_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PURCHASER_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.CREATOR_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.BUSINESS_TYPE,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PROVIDER_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PROVIDER_CONTACT_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.SETTLE_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Head.CHARGE_ID,
                // 明细字段
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.PRICE_UNIT_ID,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.QTY,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.ROW_TYPE,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.ENTRY_NOTE,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.DELIVERY_DATE,
                CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.STOCK_UNIT_ID
        ));
        queryReq.setLimit(0); // 获取所有数据
        return erpService.getErpDataList(queryReq, "采购订单");
    }

    private PurchaseOrder mapToPurchaseOrder(Map<String, Object> map) {
        PurchaseOrder order = new PurchaseOrder();
        order.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.ID)));
        order.setBillNo((String) map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.BILL_NO));
        order.setPurchaseDate(parseDate(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.DATE)));
        order.setSupplierId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.SUPPLIER_ID)));
        order.setPurchaseDeptId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PURCHASE_DEPT_ID)));
        order.setPurchaserId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PURCHASER_ID)));
        order.setCreateName(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.CREATOR_ID)));
        order.setBusinessType((String) map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.BUSINESS_TYPE));
        order.setProviderId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PROVIDER_ID)));
        order.setProviderContactId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.PROVIDER_CONTACT_ID)));
        order.setSettleId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.SETTLE_ID)));
        order.setChargeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Head.CHARGE_ID)));
        return order;
    }

    private PurchaseOrderDetail mapToPurchaseOrderDetail(Map<String, Object> map, String orderId) {
        PurchaseOrderDetail detail = new PurchaseOrderDetail();
        // 注意这里的ID用的是特殊的组合键
        detail.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.ENTRY_ID)));
        detail.setOrderId(orderId);
        detail.setMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.MATERIAL_ID)));
        detail.setUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.UNIT_ID)));
        detail.setPriceUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.PRICE_UNIT_ID)));

        Object qtyObj = map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.QTY);
        if (qtyObj instanceof Number) {
            detail.setQty(new BigDecimal(((Number) qtyObj).doubleValue()));
        } else if (qtyObj != null) {
            detail.setQty(new BigDecimal(qtyObj.toString()));
        }

        detail.setRowType((String) map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.ROW_TYPE));
        detail.setEntryNote((String) map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.ENTRY_NOTE));
        detail.setDeliveryDate(parseDate(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.DELIVERY_DATE)));
        detail.setStockUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.PurchaseOrder.Entry.STOCK_UNIT_ID)));
        return detail;
    }

    private Date parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }
        if (dateObj instanceof Date) {
            return (Date) dateObj;
        }
        if (dateObj instanceof String) {
            String dateStr = (String) dateObj;
            try {
                // 首先尝试解析 "yyyy-MM-dd'T'HH:mm:ss" 格式
                return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(dateStr);
            } catch (ParseException e) {
                // 如果失败，尝试解析 "yyyy-MM-dd" 格式
                try {
                    return new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
                } catch (ParseException e2) {
                    logger.error("无法解析日期字符串: {}", dateStr, e2);
                    return null;
                }
            }
        }
        return null;
    }
}