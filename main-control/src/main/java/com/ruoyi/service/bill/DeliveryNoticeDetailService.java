package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.domain.bill.DeliveryNoticeDetail;
import com.ruoyi.mapper.bill.delivery.DeliveryNoticeDetailMapper;
import org.springframework.stereotype.Service;

/**
 * 发货通知单明细服务实现
 * <AUTHOR>
 */
@Service
public class DeliveryNoticeDetailService extends ServiceImpl<DeliveryNoticeDetailMapper, DeliveryNoticeDetail> {

    /**
     * 根据发货通知单ID和物料编码获取发货通知单明细
     *
     * @param noticeId 发货通知单ID
     * @param materialCode 物料编码
     * @return 发货通知单明细
     */
    public DeliveryNoticeDetail getByNoticeIdAndMaterialCode(String noticeId, String materialCode) {
        return this.lambdaQuery()
                .eq(DeliveryNoticeDetail::getNoticeId, noticeId)
                .eq(DeliveryNoticeDetail::getMaterialId, materialCode)
                .one();
    }
}