package com.ruoyi.service.bill;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.domain.bill.ProductionInStock;
import com.ruoyi.domain.bill.ProductionInStockDetail;
import com.ruoyi.mapper.bill.production.ProductionInStockMapper;
import com.ruoyi.mapper.bill.production.ProductionInStockDetailMapper;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.bill.ProductionInStockDetailVo;
import com.ruoyi.vo.bill.ProductionInStockVo;
import com.ruoyi.vo.erp.common.ErpQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产入库单服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductionInStockService extends ServiceImpl<ProductionInStockMapper, ProductionInStock> {

    private static final Logger logger = LoggerFactory.getLogger(ProductionInStockService.class);

    @Resource
    private ErpService erpService;

    @Resource
    private ProductionInStockMapper productionInStockMapper;

    @Resource
    private ProductionInStockDetailMapper productionInStockDetailMapper;

    @Resource
    private ProductionInStockDetailService productionInStockDetailService;

    @Resource
    private com.ruoyi.service.basicData.BasicDocumentInfoService basicDocumentInfoService;

    public List<ProductionInStockVo> queryProductionInStockList(QueryParamVO queryParamVO) {
        return productionInStockMapper.queryProductionInStock(queryParamVO);
    }

    /**
     * 查询生产入库单明细列表
     */
    public List<ProductionInStockDetailVo> queryProductionInStockDetailList(QueryParamVO queryParamVO) {
        return productionInStockDetailService.queryProductionInStockDetailList(queryParamVO);
    }

    /**
     * 从ERP同步生产入库单
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult syncFromErp() throws Exception {
        log.info("开始同步ERP生产入库单...");

        // 1. 从ERP拉取所有生产入库单数据
        List<Map<String, Object>> erpDataList = fetchAllErpProductionInStockData();
        if (CollectionUtils.isEmpty(erpDataList)) {
            log.info("ERP中未查询到生产入库单数据。");
            return ResponseResult.getSuccessResult("ERP中未查询到生产入库单", null);
        }
        log.info("从ERP查询到 {} 条生产入库单行记录", erpDataList.size());

        // 2. 按单据ID进行分组
        Map<String, List<Map<String, Object>>> erpInStockGrouped = erpDataList.stream()
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.ID))));
        List<String> erpInStockIds = new ArrayList<>(erpInStockGrouped.keySet());

        log.info("ERP生产入库单按ID分组后共 {} 个单据", erpInStockIds.size());

        // 3. 查询本地已存在的生产入库单
        Map<String, ProductionInStock> existingInStockMap = new HashMap<>();
        if (!erpInStockIds.isEmpty()) {
            List<ProductionInStock> existingInStocks = this.listByIds(erpInStockIds);
            existingInStockMap = existingInStocks.stream()
                    .collect(Collectors.toMap(ProductionInStock::getId, Function.identity()));
        }

        // 4. 分别处理新增和更新
        List<ProductionInStock> toInsertInStocks = new ArrayList<>();
        List<ProductionInStock> toUpdateInStocks = new ArrayList<>();
        List<ProductionInStockDetail> toInsertDetails = new ArrayList<>();

        for (String inStockId : erpInStockIds) {
            List<Map<String, Object>> inStockRows = erpInStockGrouped.get(inStockId);
            if (inStockRows.isEmpty()) continue;

            // 使用第一行数据构建主表信息
            Map<String, Object> firstRow = inStockRows.get(0);
            ProductionInStock inStock = mapToProductionInStock(firstRow);

            if (existingInStockMap.containsKey(inStockId)) {
                toUpdateInStocks.add(inStock);
            } else {
                toInsertInStocks.add(inStock);
            }

            // 构建明细信息
            for (Map<String, Object> row : inStockRows) {
                ProductionInStockDetail detail = mapToProductionInStockDetail(row, inStockId);
                toInsertDetails.add(detail);
            }
        }

        // 5. 执行数据库操作
        int insertedInStocks = 0, updatedInStocks = 0, insertedDetails = 0;

        if (!toInsertInStocks.isEmpty()) {
            this.saveBatch(toInsertInStocks);
            insertedInStocks = toInsertInStocks.size();
            log.info("新增生产入库单 {} 条", insertedInStocks);
        }

        if (!toUpdateInStocks.isEmpty()) {
            this.updateBatchById(toUpdateInStocks);
            updatedInStocks = toUpdateInStocks.size();
            log.info("更新生产入库单 {} 条", updatedInStocks);
        }

        // 删除现有明细，重新插入（简化处理）
        if (!erpInStockIds.isEmpty()) {
            LambdaQueryWrapper<ProductionInStockDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(ProductionInStockDetail::getInStockId, erpInStockIds);
            productionInStockDetailService.remove(deleteWrapper);
        }

        if (!toInsertDetails.isEmpty()) {
            productionInStockDetailService.saveBatch(toInsertDetails);
            insertedDetails = toInsertDetails.size();
            log.info("新增生产入库单明细 {} 条", insertedDetails);
        }

        // 6. 为新增生产入库单创建生产入库单据
        if (!toInsertInStocks.isEmpty()) {
            log.info("为新增生产入库单创建生产入库单据，数量：{}", toInsertInStocks.size());
            try {
                ResponseResult batchResult = basicDocumentInfoService.createDocumentFromSourceBatch(
                        CommonConstant.SourceDocumentType.PRODUCTION_IN_STOCK,
                        toInsertInStocks,
                        CommonConstant.BoundType.SCRK,
                        CommonConstant.InoutType.IN);

                if (batchResult.getCode() == HttpStatus.SUCCESS) {
                    log.info("生产入库单创建WMS单据成功：{}", batchResult.getMsg());
                } else {
                    log.warn("生产入库单创建WMS单据失败：{}", batchResult.getMsg());
                }
            } catch (Exception e) {
                log.error("生产入库单创建WMS单据异常", e);
            }
        }

        // 7. 构建返回结果
        String summary = String.format("同步完成：新增生产入库单%d条，更新%d条，明细%d条",
                insertedInStocks, updatedInStocks, insertedDetails);
        log.info(summary);

        Map<String, Object> resultData = new HashMap<>();
        resultData.put("insertedInStocks", insertedInStocks);
        resultData.put("updatedInStocks", updatedInStocks);
        resultData.put("insertedDetails", insertedDetails);
        resultData.put("totalInStocks", insertedInStocks + updatedInStocks);
        resultData.put("totalDetails", insertedDetails);

        return ResponseResult.getSuccessResult(summary, resultData);
    }

    private List<Map<String, Object>> fetchAllErpProductionInStockData() throws Exception {
        ErpQueryReq queryReq = new ErpQueryReq();
        queryReq.setFormId(CommonConstant.ErpFormId.PRODUCTION_IN_STOCK);
        queryReq.setFieldKeys(String.join(",",
                // Head
                CommonConstant.ErpFieldKeys.ProductionInStock.Head.ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Head.BILL_NO,
                CommonConstant.ErpFieldKeys.ProductionInStock.Head.DESCRIPTION,
                CommonConstant.ErpFieldKeys.ProductionInStock.Head.DATE,
                CommonConstant.ErpFieldKeys.ProductionInStock.Head.PRD_ORG_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Head.STOCK_ORG_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Head.OWNER_ID_0,
                // Entry
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MATERIAL_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MATERIAL_NAME,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.SPECIFICATION,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.PRODUCT_TYPE,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.IN_STOCK_TYPE,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.UNIT_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.BASE_UNIT_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MUST_QTY,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.BASE_MUST_QTY,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.REAL_QTY,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.BASE_REAL_QTY,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.OWNER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.OWNER_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.STOCK_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_BILL_NO,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_ENTRY_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_ENTRY_SEQ,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MEMO,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.STOCK_STATUS_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.KEEPER_TYPE_ID,
                CommonConstant.ErpFieldKeys.ProductionInStock.Entry.KEEPER_ID
        ));
        queryReq.setLimit(0);
        return erpService.getErpDataList(queryReq, CommonConstant.ErpQueryContext.ERP_PRODUCTION_IN_STOCK);
    }

    private ProductionInStock mapToProductionInStock(Map<String, Object> map) {
        ProductionInStock inStock = new ProductionInStock();
        inStock.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.ID)));
        inStock.setBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.BILL_NO)));
        inStock.setDescription(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.DESCRIPTION)));
        inStock.setPrdOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.PRD_ORG_ID)));
        inStock.setStockOrgId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.STOCK_ORG_ID)));
        inStock.setOwnerId0(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.OWNER_ID_0)));

        // 处理日期
        Object dateObj = map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Head.DATE);
        if (dateObj != null && !dateObj.toString().equals("null")) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                inStock.setInStockDate(sdf.parse(dateObj.toString()));
            } catch (ParseException e) {
                log.warn("解析生产入库单日期失败: {}", dateObj, e);
            }
        }

        inStock.setCreateName("ERP同步");
        inStock.setCreateTime(new Date());
        inStock.setUpdateName("ERP同步");
        inStock.setUpdateTime(new Date());

        return inStock;
    }

    private ProductionInStockDetail mapToProductionInStockDetail(Map<String, Object> map, String inStockId) {
        ProductionInStockDetail detail = new ProductionInStockDetail();
        detail.setId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.ENTRY_ID)));
        detail.setInStockId(inStockId);
        detail.setMaterialId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MATERIAL_ID)));
        detail.setMaterialName(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MATERIAL_NAME)));
        detail.setSpecification(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.SPECIFICATION)));
        detail.setProductType(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.PRODUCT_TYPE)));
        detail.setInStockType(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.IN_STOCK_TYPE)));
        detail.setUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.UNIT_ID)));
        detail.setBaseUnitId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.BASE_UNIT_ID)));
        detail.setOwnerTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.OWNER_TYPE_ID)));
        detail.setOwnerId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.OWNER_ID)));
        detail.setStockId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.STOCK_ID)));
        detail.setMoBillNo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_BILL_NO)));
        detail.setMoId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_ID)));
        detail.setMoEntryId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_ENTRY_ID)));
        detail.setMoEntrySeq(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MO_ENTRY_SEQ)));
        detail.setMemo(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MEMO)));
        detail.setStockStatusId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.STOCK_STATUS_ID)));
        detail.setKeeperTypeId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.KEEPER_TYPE_ID)));
        detail.setKeeperId(String.valueOf(map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.KEEPER_ID)));

        // 处理数量字段
        Object mustQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.MUST_QTY);
        if (mustQtyObj instanceof Number) {
            detail.setMustQty(new BigDecimal(mustQtyObj.toString()));
        }

        Object baseMustQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.BASE_MUST_QTY);
        if (baseMustQtyObj instanceof Number) {
            detail.setBaseMustQty(new BigDecimal(baseMustQtyObj.toString()));
        }

        Object realQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.REAL_QTY);
        if (realQtyObj instanceof Number) {
            detail.setRealQty(new BigDecimal(realQtyObj.toString()));
        }

        Object baseRealQtyObj = map.get(CommonConstant.ErpFieldKeys.ProductionInStock.Entry.BASE_REAL_QTY);
        if (baseRealQtyObj instanceof Number) {
            detail.setBaseRealQty(new BigDecimal(baseRealQtyObj.toString()));
        }

        return detail;
    }
}