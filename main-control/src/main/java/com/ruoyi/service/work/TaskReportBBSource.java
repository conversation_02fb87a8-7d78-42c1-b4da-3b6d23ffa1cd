package com.ruoyi.service.work;

import com.ruoyi.vo.webResponse.dto.MaterialDetailExcelData;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;

import java.util.ArrayList;
import java.util.List;

public class TaskReportBBSource implements JRDataSource {
    private int index = -1;
    List<MaterialDetailExcelData> reportList;
    public TaskReportBBSource() {
        this.reportList = new ArrayList<MaterialDetailExcelData>();
    }

    public List<MaterialDetailExcelData> getReportList() {
        return reportList;
    }

    public void setReportList(List<MaterialDetailExcelData> reportList) {
        this.reportList = reportList;
    }

    public TaskReportBBSource(List<MaterialDetailExcelData> excelList) {
        this.reportList = excelList;
    }


    @Override
    public boolean next() throws JRException {
        index++;
        return index < reportList.size();
    }

    @Override
    public Object getFieldValue(JRField jrField) throws JRException {
        Object value = null;
        String fieldName = jrField.getName();

        if ("MATERIAL_CODE".equals(fieldName)) {
            value = reportList.get(index).getMaterialCode();
        }else if ("MATERIAL_NAME".equals(fieldName)) {
            value = reportList.get(index).getMaterialName();
        }else if ("WAREHOUSE_NAME".equals(fieldName)) {
            value = reportList.get(index).getWarehouseName();
        }else if ("BOUND_INDEX".equals(fieldName)) {
            value = reportList.get(index).getUpperIndex();
        }else if ("MATERIAL_NUM".equals(fieldName)) {
            value = reportList.get(index).getMaterialNum();
        }else if ("BATCH".equals(fieldName)) {
            value = reportList.get(index).getBatch();
        }else if ("PRODUCE_DATE".equals(fieldName)) {
            value = reportList.get(index).getProduceDate();
        }else if ("REMARK".equals(fieldName)) {
            value = reportList.get(index).getRemark();
        }else if ("IS_COMPLIANT".equals(fieldName)) {
            value = reportList.get(index).getIsCompliant();
        }else if ("MATERIAL_LOCATION".equals(fieldName)) {
            value = reportList.get(index).getMaterialLocation();
        }else if ("ORIGN_MATERIAL_CODE".equals(fieldName)) {
            value = reportList.get(index).getOrignMaterialCode();
        }else if ("ORIGN_CONTAINER".equals(fieldName)) {
            value = reportList.get(index).getOrignContainer();
        }else if ("ORIGN_BATCH".equals(fieldName)) {
            value = reportList.get(index).getOrignBatch();
        }else if ("ORIGN_PRODUCE_DATE".equals(fieldName)) {
            value = reportList.get(index).getOrignProduceDate();
        }else if ("ORIGN_UPPER_INDEX".equals(fieldName)) {
            value = reportList.get(index).getOrignUpperIndex();
        }else if ("DEST_MATERIAL_CODE".equals(fieldName)) {
            value = reportList.get(index).getDestMaterialCode();
        }else if ("DEST_CONTAINER".equals(fieldName)) {
            value = reportList.get(index).getDestContainer();
        }else if ("DEST_BATCH".equals(fieldName)) {
            value = reportList.get(index).getDestBatch();
        }else if ("DEST_PRODUCE_DATE".equals(fieldName)) {
            value = reportList.get(index).getDestProduceDate();
        }else if ("DEST_UPPER_INDEX".equals(fieldName)) {
            value = reportList.get(index).getDestUpperIndex();
        }else if ("ORIGN_MATERIAL_NAME".equals(fieldName)) {
            value = reportList.get(index).getOrignMaterialName();
        }else if ("DEST_MATERIAL_NAME".equals(fieldName)) {
            value = reportList.get(index).getDestMaterialName();
        }
        return value;
    }
}
