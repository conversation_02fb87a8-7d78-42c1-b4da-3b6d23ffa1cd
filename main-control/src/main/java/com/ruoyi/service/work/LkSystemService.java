package com.ruoyi.service.work;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ruoyi.service.basicData.UpperSignalDataService;
import com.ruoyi.service.sys.SysInterConfigService;
import com.ruoyi.utils.*;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.utils.constant.InterConstant;
import com.ruoyi.vo.lk.InventoryNumResponse;
import com.ruoyi.vo.lk.InventoryStatisticsDetailVo;
import com.ruoyi.vo.lk.InventoryStatisticsVo;
import com.ruoyi.vo.lk.LkSendTaskRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

//立库服务类
@Service
public class LkSystemService {
    @Resource
    SysInterConfigService sysInterConfigService;

    @Resource
    UpperSignalDataService upperSignalDataService;


    protected final Logger logger = LoggerFactory.getLogger(LkSystemService.class);


    /**
     * 立库数量查询
     **/
    public Integer inventoryStatistics(String interType,String materielCode) {
        InventoryStatisticsVo inventoryStatisticsVo = new InventoryStatisticsVo();
        inventoryStatisticsVo.setId(UUID.randomUUID().toString());
        inventoryStatisticsVo.setTimestamp(new Date());
        InventoryStatisticsDetailVo inventoryStatisticsDetailVo = new InventoryStatisticsDetailVo();
        inventoryStatisticsDetailVo.setMaterielCode(materielCode);
        inventoryStatisticsVo.setData(inventoryStatisticsDetailVo);
        String interConfigValByType = sysInterConfigService.getInterConfigValByType(interType);
        ResponseResult responseResult = HttpUtils.sendRequestGetResponseResult(interConfigValByType, inventoryStatisticsVo);
        if (responseResult != null && responseResult.getCode().equals(ResultMsg.successCode)){
            List<InventoryNumResponse> inventoryNumResponses = new Gson().fromJson(GsonUtils.toJsonString(responseResult.getData()), new TypeToken<List<InventoryNumResponse>>(){}.getType());
            if (inventoryNumResponses !=null && inventoryNumResponses.size()>0){
                InventoryNumResponse inventoryNumResponse = inventoryNumResponses.get(0);
                return inventoryNumResponse.getQuantity();
            }else {
                logger.info("查询立库数据失败" + responseResult.getMsg());
                return 0;
            }
        }else {
            logger.info("查询立库数据失败" + responseResult.getMsg());
            return 0;
        }
    }
    /**
     * 发送立库入库任务
     **/
    public ResponseResult sendLkInTask(LkSendTaskRequest lkSendTaskRequest, String lkName) {
        String interType = null;
        if (lkName.equals(CommonConstant.LkName.BOX)){
            interType = InterConstant.InterType.BOX_SEND_IN;
        }
        if (lkName.equals(CommonConstant.LkName.PLATE)){
            interType = InterConstant.InterType.PLATE_SEND_IN;
        }
        if (lkName.equals(CommonConstant.LkName.PROFILE)){
            interType = InterConstant.InterType.PROFILE_SEND_IN;
        }
        String interConfigValByType = sysInterConfigService.getInterConfigValByType(interType);
        ResponseResult responseResult = HttpUtils.sendRequestGetResponseResult(interConfigValByType, lkSendTaskRequest);
        upperSignalDataService.insertData(GsonUtils.toJsonString(lkSendTaskRequest),GsonUtils.toJsonString(responseResult),new Date(), CommonConstant.SignalType.MES_RK_SEND,"WMS",CommonConstant.IsWeb.AUTO);
        if (responseResult != null && responseResult.getCode().equals(ResultMsg.successCode)){
            return ResponseResult.getSuccessResult();
        }else {
            logger.info("下发立库入库任务失败" + responseResult.getMsg());
            return ResponseResult.getErrorResult("下发立库入库任务失败" + responseResult.getMsg());
        }
    }


    /**
     * 发送立库出库任务
     **/
    public ResponseResult sendLkOutTask(LkSendTaskRequest lkSendTaskRequest, String lkName) {
        String interType = null;
        if (lkName.equals(CommonConstant.LkName.BOX)){
            interType = InterConstant.InterType.BOX_SEND_OUT;
        }
        if (lkName.equals(CommonConstant.LkName.PLATE)){
            interType = InterConstant.InterType.PLATE_SEND_OUT;
        }
        if (lkName.equals(CommonConstant.LkName.PROFILE)){
            interType = InterConstant.InterType.PROFILE_SEND_OUT;
        }
        String interConfigValByType = sysInterConfigService.getInterConfigValByType(interType);
        ResponseResult responseResult = HttpUtils.sendRequestGetResponseResult(interConfigValByType, lkSendTaskRequest);
        upperSignalDataService.insertData(GsonUtils.toJsonString(lkSendTaskRequest),GsonUtils.toJsonString(responseResult),new Date(), CommonConstant.SignalType.MES_CK_SEND,"WMS",CommonConstant.IsWeb.AUTO);
        if (responseResult != null && responseResult.getCode().equals(ResultMsg.successCode)){
            return ResponseResult.getSuccessResult();
        }else {
            logger.info("下发立库出库任务失败" + responseResult.getMsg());
            return ResponseResult.getErrorResult("下发立库出库任务失败" + responseResult.getMsg());
        }
    }
}
