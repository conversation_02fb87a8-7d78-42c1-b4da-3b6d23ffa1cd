package com.ruoyi.service.qc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.qc.QcTemplateDetail;
import com.ruoyi.domain.qc.QcTemplateInfo;
import com.ruoyi.domain.qc.QcTemplateMaterial;
import com.ruoyi.mapper.qc.QcTemplateInfoMapper;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.qc.QcTemplateAndDetailReq;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webRequest.qc.BatchAddTemplateDetailReq;
import com.ruoyi.vo.webRequest.qc.BatchAddTemplateMaterialReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [质检模板信息]
 * @date 2024/5/9 18:27
 */
@Service
@Transactional
public class QcTemplateInfoService {

    private static final Logger logger = LoggerFactory.getLogger(QcTemplateInfoService.class);

    @Resource
    private QcTemplateInfoMapper qcTemplateInfoMapper;
    @Resource
    private QcTemplateMaterialService qcTemplateMaterialService;
    @Resource
    private QcTemplateDetailService qcTemplateDetailService;

    /**
     * 新增
     */
    public ResponseResult addQcTemplateInfo(QcTemplateInfo qcTemplateInfo){
        qcTemplateInfo.setId(LocalStringUtils.getDataUUID());
        qcTemplateInfo.setTemplateCode(getMaxTemplateCode());
        qcTemplateInfo.setCreateTime(new Date());
        qcTemplateInfo.setUpdateTime(new Date());
        int result = this.qcTemplateInfoMapper.insert(qcTemplateInfo);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 获取最大的质检模板
     */
    private String getMaxTemplateCode() {
        String prefix = CommonConstant.TaskPrefix.QC_TEMPLATE_NO_HEAD;
        String maxCode = this.qcTemplateInfoMapper.getMaxTemplateCode(prefix);
        return AutoNum.geMaxCode(prefix,maxCode);
    }

    /**
     * 删除质检模板（级联删除对应的详情和物料配置）
     */
    @Transactional
    public ResponseResult deleteQcTemplateInfo(BatchIdsReq req) {
        try {
            List<String> ids = req.getIds();
            int successCount = 0;

            for (String id : ids) {
                // 1. 根据ID获取质检模板信息
                QcTemplateInfo templateInfo = this.qcTemplateInfoMapper.selectById(id);
                if (templateInfo == null) {
                    logger.warn("质检模板删除失败：未找到ID为 {} 的模板", id);
                    continue;
                }

                String templateCode = templateInfo.getTemplateCode();
                logger.info("开始删除质检模板：{} ({})", templateInfo.getTemplateName(), templateCode);

                // 2. 删除对应的质检模板详情
                LambdaQueryWrapper<QcTemplateDetail> detailWrapper = new LambdaQueryWrapper<>();
                detailWrapper.eq(QcTemplateDetail::getTemplateCode, templateCode);
                int detailCount = qcTemplateDetailService.count(detailWrapper);

                if (detailCount > 0) {
                    boolean detailDeleted = qcTemplateDetailService.remove(detailWrapper);
                    if (detailDeleted) {
                        logger.info("成功删除质检模板 {} 的 {} 条详情记录", templateCode, detailCount);
                    } else {
                        logger.error("删除质检模板 {} 的详情记录失败", templateCode);
                        continue;
                    }
                } else {
                    logger.info("质检模板 {} 没有关联的详情记录", templateCode);
                }

                // 3. 删除对应的质检模板物料配置
                LambdaQueryWrapper<QcTemplateMaterial> materialWrapper = new LambdaQueryWrapper<>();
                materialWrapper.eq(QcTemplateMaterial::getTemplateCode, templateCode);
                int materialCount = qcTemplateMaterialService.count(materialWrapper);

                if (materialCount > 0) {
                    boolean materialDeleted = qcTemplateMaterialService.remove(materialWrapper);
                    if (materialDeleted) {
                        logger.info("成功删除质检模板 {} 的 {} 条物料配置记录", templateCode, materialCount);
                    } else {
                        logger.error("删除质检模板 {} 的物料配置记录失败", templateCode);
                        continue;
                    }
                } else {
                    logger.info("质检模板 {} 没有关联的物料配置记录", templateCode);
                }

                // 4. 删除质检模板主记录
                int templateDeleted = this.qcTemplateInfoMapper.deleteById(id);
                if (templateDeleted > 0) {
                    logger.info("成功删除质检模板：{} ({})", templateInfo.getTemplateName(), templateCode);
                    successCount++;
                } else {
                    logger.error("删除质检模板主记录失败：{} ({})", templateInfo.getTemplateName(), templateCode);
                }
            }

            if (successCount >= ids.size()) {
                return ResponseResult.getSuccessResult("删除成功", null);
            } else if (successCount > 0) {
                return ResponseResult.getErrorResult(String.format("部分删除成功，成功删除 %d/%d 条记录", successCount, ids.size()));
            } else {
                return ResponseResult.getErrorResult("删除失败，请重试");
            }

        } catch (Exception e) {
            logger.error("删除质检模板时发生异常", e);
            return ResponseResult.getErrorResult("删除失败：" + e.getMessage());
        }
    }

    /**
     * 更新质检模板
     */
    public ResponseResult uptQcTemplateInfo(QcTemplateInfo qcTemplateInfo) {
        qcTemplateInfo.setUpdateTime(new Date());
        int count = this.qcTemplateInfoMapper.updateById(qcTemplateInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    public List<QcTemplateInfo> queryQcTemplateInfo(QueryParamVO param) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotEmpty(param.getKeyWord())){
            queryWrapper.eq("template_code",param.getKeyWord());
//            queryWrapper.eq("item_code",param.getKeySubWord());
            queryWrapper.or();
            queryWrapper.eq("template_name",param.getKeyWord());
        }
        queryWrapper.orderByDesc("create_time");
        return this.qcTemplateInfoMapper.selectList(queryWrapper);
    }

    /**
     * 批量插入质检模板信息
     * 模板详情信息
     * 模板物料信息
     */
    public ResponseResult insertQcTemplateAndDetail(QcTemplateAndDetailReq req) {
        QcTemplateInfo qcTemplateInfo = new QcTemplateInfo();
        BeanUtils.copyProperties(req,qcTemplateInfo);
        addQcTemplateInfo(qcTemplateInfo);
        //物料详情
        BatchAddTemplateMaterialReq batchAddTemplateMaterialReq = new BatchAddTemplateMaterialReq();
        batchAddTemplateMaterialReq.setTemplateCode(qcTemplateInfo.getTemplateCode());
        batchAddTemplateMaterialReq.setDetails(req.getMaterialList());
        qcTemplateMaterialService.insertQcTemplateMaterial(batchAddTemplateMaterialReq);

        //模板详情
        BatchAddTemplateDetailReq batchAddTemplateDetailReq = new BatchAddTemplateDetailReq();
        batchAddTemplateDetailReq.setTemplateCode(qcTemplateInfo.getTemplateCode());
        batchAddTemplateDetailReq.setDetails(req.getTemplateDetails());
        qcTemplateDetailService.addQcTemplateDetail(batchAddTemplateDetailReq);
        return ResponseResult.getSuccessResult();
    }
}
