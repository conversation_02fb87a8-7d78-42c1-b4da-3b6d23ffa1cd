package com.ruoyi.service.qc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.qc.QcTemplateDetail;
import com.ruoyi.mapper.qc.QcTemplateDetailMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.qc.QcTemplateDetailVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webRequest.qc.BatchAddTemplateDetailReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [质检模板-详情]
 * @date 2024/5/9 18:27
 */
@Service
public class QcTemplateDetailService extends ServiceImpl<QcTemplateDetailMapper, QcTemplateDetail> {

    @Resource
    private QcTemplateDetailMapper qcTemplateDetailMapper;

    /**
     * 批量新增
     */
    public ResponseResult addQcTemplateDetail(BatchAddTemplateDetailReq req){
        String templateCode = req.getTemplateCode();
        List<QcTemplateDetail> templateDetails = req.getDetails();
        for (int i = 0; i < templateDetails.size(); i++) {
            QcTemplateDetail qcTemplateDetail = templateDetails.get(i);
            qcTemplateDetail.setId(LocalStringUtils.getDataUUID());
            qcTemplateDetail.setCreateTime(new Date());
            qcTemplateDetail.setTemplateCode(templateCode);
        }
        boolean result = this.saveBatch(templateDetails);
        if(result){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }


    /**
     * 删除
     */
    public ResponseResult deleteQcTemplateDetail(BatchIdsReq req) {
        int count = this.qcTemplateDetailMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新
     */
    public ResponseResult uptQcTemplateDetail(QcTemplateDetail qcTemplateDetail) {
        int count = this.qcTemplateDetailMapper.updateById(qcTemplateDetail);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询
     */
    public List<QcTemplateDetailVo> queryQcTemplateDetail(QueryParamVO param) {
        return this.qcTemplateDetailMapper.queryQcTemplateDetail(param);
    }

    public List<QcTemplateDetail> getQcTemplateDetailByTemplateCode(String templateCode) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("template_code",templateCode);
        return this.qcTemplateDetailMapper.selectList(queryWrapper);
    }
}
