package com.ruoyi.service.qc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.qc.QcIpqcDetailInfo;
import com.ruoyi.mapper.qc.QcIpqcDetailInfoMapper;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.qc.QcIpqcDetailInfoVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 14:31
 * @Description: 类描述
 */
@Service
public class QcIpqcDetailInfoService extends ServiceImpl<QcIpqcDetailInfoMapper, QcIpqcDetailInfo> {

    @Resource
    private QcIpqcDetailInfoMapper qcIpqcDetailInfoMapper;

    /**
     * 新增
     */
    public ResponseResult addQcIpqcDetailInfo(QcIpqcDetailInfo qcIpqcDetailInfo){
        qcIpqcDetailInfo.setId(LocalStringUtils.getDataUUID());
        qcIpqcDetailInfo.setCreateTime(new Date());
        qcIpqcDetailInfo.setUpdateTime(new Date());
        int result = this.qcIpqcDetailInfoMapper.insert(qcIpqcDetailInfo);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 删除
     */
    public ResponseResult deleteQcIpqcDetailInfo(BatchIdsReq req) {
        int count = this.qcIpqcDetailInfoMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新质检详情
     */
    public ResponseResult uptQcIpqcDetailInfo(QcIpqcDetailInfo qcIpqcDetailInfo) {
        qcIpqcDetailInfo.setUpdateTime(new Date());
        int count = this.qcIpqcDetailInfoMapper.updateById(qcIpqcDetailInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页
     */
    public List<QcIpqcDetailInfoVo> queryQcIpqcDetailInfo(QueryParamVO param) {
        return this.qcIpqcDetailInfoMapper.queryQcIpqcDetailInfo(param);
    }
    
}
