package com.ruoyi.service.qc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.qc.QcTemplateDetail;
import com.ruoyi.domain.qc.QcTemplateMaterial;
import com.ruoyi.mapper.qc.QcTemplateMaterialMapper;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.qc.QcTemplateMaterialVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webRequest.qc.BatchAddTemplateMaterialReq;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/23 10:56
 * @Description: 质检物料服务类
 */
@Service
public class QcTemplateMaterialService extends ServiceImpl<QcTemplateMaterialMapper, QcTemplateMaterial> {

    @Resource
    private QcTemplateMaterialMapper qcTemplateMaterialMapper;


    /**
     * 新增质检物料
     */
    public ResponseResult insertQcTemplateMaterial(BatchAddTemplateMaterialReq req){
        String templateCode = req.getTemplateCode();
        List<QcTemplateMaterial> templateDetails = req.getDetails();
        // 检查重复物料
        List<String> duplicateMaterials = new ArrayList<>();
        for (QcTemplateMaterial qcTemplateMaterial : templateDetails) {
            String materialCode = qcTemplateMaterial.getMaterialCode();
            if (StringUtils.isNotEmpty(materialCode)) {
                LambdaQueryWrapper<QcTemplateMaterial> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(QcTemplateMaterial::getTemplateCode, templateCode)
                           .eq(QcTemplateMaterial::getMaterialCode, materialCode);
                long count = this.count(queryWrapper);
                if (count > 0) {
                    duplicateMaterials.add(materialCode);
                }
            }
        }
        // 如果存在重复物料，返回错误信息
        if (!duplicateMaterials.isEmpty()) {
            String duplicateCodesStr = String.join("、", duplicateMaterials);
            return ResponseResult.getErrorResult("以下物料编码在当前模板中已存在，不允许重复添加：" + duplicateCodesStr);
        }
        // 设置基本信息
        for (int i = 0; i < templateDetails.size(); i++) {
            QcTemplateMaterial qcTemplateMaterial = templateDetails.get(i);
            qcTemplateMaterial.setId(LocalStringUtils.getDataUUID());
            qcTemplateMaterial.setCreateTime(new Date());
            qcTemplateMaterial.setTemplateCode(templateCode);
        }

        boolean result = this.saveBatch(templateDetails);
        if(result){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }


    /**
     * 删除质检物料
     */
    public ResponseResult deleteQcTemplateMaterial(BatchIdsReq req) {
        int count = this.qcTemplateMaterialMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新质检物料
     */
    public ResponseResult uptQcTemplateMaterial(QcTemplateMaterial qcTemplateMaterial) {
        int count = this.qcTemplateMaterialMapper.updateById(qcTemplateMaterial);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询
     */
    public List<QcTemplateMaterialVo> queryQcTemplateMaterial(QueryParamVO param) {
        return this.qcTemplateMaterialMapper.queryQcTemplateMaterial(param);
    }

    /**
     * 根据质检物料编码查询
     */
    public QcTemplateMaterial queryQcTemplateMaterial(String template_code) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("template_code",template_code);
        queryWrapper.last("limit 1");
        return this.qcTemplateMaterialMapper.selectOne(queryWrapper);
    }

    /**
     * 获取启用的质检物料
     */
    public QcTemplateMaterial getTemplateMaterialByMaterialCodeAndQcType(String materialCode, Integer qcType) {
        return this.qcTemplateMaterialMapper.getTemplateMaterialByMaterialCodeAndQcType(materialCode,qcType);
    }
}
