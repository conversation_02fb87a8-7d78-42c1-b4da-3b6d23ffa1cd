package com.ruoyi.service.qc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.qc.QcTemplateItem;
import com.ruoyi.mapper.qc.QcTemplateItemMapper;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/19 10:07
 * @Description: 类描述
 */
@Service
public class QcTemplateItemService extends ServiceImpl<QcTemplateItemMapper, QcTemplateItem> {

    @Resource
    private QcTemplateItemMapper qcTemplateItemMapper;


    /**
     * 新增质检项
     */
    public ResponseResult insertQcTemplateItem(QcTemplateItem qcTemplateItem){
        qcTemplateItem.setId(LocalStringUtils.getDataUUID());
        qcTemplateItem.setItemCode(getMaxItemCode());
        qcTemplateItem.setCreateTime(new Date());
        qcTemplateItem.setUpdateTime(new Date());
        int result = this.qcTemplateItemMapper.insert(qcTemplateItem);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    /**
     * 获取最大的质检项编码
     */
    private String getMaxItemCode() {
        String prefix = CommonConstant.TaskPrefix.QC_ITEM_NO_HEAD;
        // 查询已有最大编码
        String maxCode = this.qcTemplateItemMapper.getMaxItemCode(prefix);
        return AutoNum.geMaxCode(prefix,maxCode);
    }

    /**
     * 删除质检项
     */
    public ResponseResult deleteQcTemplateItem(BatchIdsReq req) {
        int count = this.qcTemplateItemMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    /**
     * 更新质检项
     */
    public ResponseResult uptQcTemplateItem(QcTemplateItem qcTemplateItem) {
        qcTemplateItem.setUpdateTime(new Date());
        int count = this.qcTemplateItemMapper.updateById(qcTemplateItem);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页查询
     */
    public List<QcTemplateItem> queryQcTemplateItem(QueryParamVO param) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotEmpty(param.getKeyWord())){
            queryWrapper.eq("item_code",param.getKeySubWord());
            queryWrapper.or();
            queryWrapper.eq("item_name",param.getKeySubWord());
        }
        if(StringUtils.isNotEmpty(param.getKeySubWord())){
            queryWrapper.like("qc_tool",param.getKeySubWord());
        }
        if(StringUtils.isNotEmpty(param.getKeyThirdWord())){
            queryWrapper.eq("enable_flag",param.getKeyThirdWord());
        }
        queryWrapper.orderByAsc("create_time");
        return this.qcTemplateItemMapper.selectList(queryWrapper);
    }

    /**
     * 根据质检项编码查询
     */
    public QcTemplateItem queryQcTemplateItem(String itemCode) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("item_code",itemCode);
        queryWrapper.last("limit 1");
        return this.qcTemplateItemMapper.selectOne(queryWrapper);
    }

}
