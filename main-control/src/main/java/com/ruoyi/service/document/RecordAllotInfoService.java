package com.ruoyi.service.document;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.RecordMaterialInout;
import com.ruoyi.domain.document.RecordAllotDetail;
import com.ruoyi.domain.document.RecordAllotInfo;
import com.ruoyi.mapper.document.RecordAllotInfoMapper;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.work.RecordMaterialInoutService;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.AllotDetailList;
import com.ruoyi.vo.document.RecordAllotInfoDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 物料调拨Service业务层处理
 */
@Service
public class RecordAllotInfoService extends ServiceImpl<RecordAllotInfoMapper, RecordAllotInfo> {

    @Resource
    RecordAllotInfoMapper recordAllotInfoMapper;
    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    RecordAllotDetailService recordAllotDetailService;
    @Resource
    RecordMaterialInoutService recordMaterialInoutService;

    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = recordAllotInfoMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }

    /**
     * 新增物料调拨信息
     *
     * @param dto 物料调拨信息
     * @return 结果
     */
    @Transactional
    public ResponseResult addRecordAllotInfo(RecordAllotInfoDto dto) {
        //生成单据
        String boundIndex = getMaxIndex(CommonConstant.CodePrefix.MATERIAL_ALLOT_PREFIX);
        List<AllotDetailList> detailList = dto.getDetailList();
        if (detailList == null) {
            return ResponseResult.getErrorResult("物料调拨明细不能为空");
        }
        //获取明细当中的所有物料数量
        int totalNum = 0;
        for (AllotDetailList allotDetailList : detailList) {
            totalNum += allotDetailList.getMaterialNum();
        }
        RecordAllotInfo recordAllotInfo = new RecordAllotInfo();
        recordAllotInfo.setRecorder(SecurityUtils.getUsername());
        recordAllotInfo.setRecordDate(new Date());
        recordAllotInfo.setState(CommonConstant.BoundStatus.PENDING_RE);
        recordAllotInfo.setTotalNum(totalNum);
        recordAllotInfo.setReasons(dto.getReasons());
        recordAllotInfo.setBoundIndex(boundIndex);
        recordAllotInfo.setTransferRule(dto.getTransferRule());
        recordAllotInfo.setRemark(dto.getRemark());
        //处理物料调拨明细
        for (AllotDetailList allotDetail : detailList) {
            BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getById(allotDetail.getInventoryId());
            if (materialBatchInventory == null) {
                return ResponseResult.getErrorResult("调拨明细中的调出物料不存在！");
            }
            // 校验物料的可用数量是否足够
            if (materialBatchInventory.getAvailNum() < allotDetail.getMaterialNum()) {
                return ResponseResult.getErrorResult("物料可用量为: " + materialBatchInventory.getAvailNum() + ", 数量不能大于可用数量!");
            }
            //冻结物料

            basicMaterialBatchInventoryService.freezeMaterial(allotDetail.getInventoryId(), allotDetail.getMaterialNum());
            //保存明细
            RecordAllotDetail recordAllotDetail = new RecordAllotDetail();
            BeanUtils.copyProperties(allotDetail, recordAllotDetail);
            recordAllotDetail.setBoundIndex(boundIndex);
            recordAllotDetail.setInventoryId(allotDetail.getInventoryId());
            recordAllotDetailService.save(recordAllotDetail);
        }
        //保存入库
        this.save(recordAllotInfo);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 修改物料调拨信息
     *
     * @param dto 物料调拨信息
     * @return 结果
     */
    @Transactional
    public ResponseResult updateRecordAllotInfo(RecordAllotInfo dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordAllotInfo recordAllotInfo = recordAllotInfoMapper.selectById(dto.getId());
        if (recordAllotInfo == null) {
            return ResponseResult.getErrorResult("该单据不存在，请刷新页面!");
        }
        if (recordAllotInfo.getState().equals(CommonConstant.BoundStatus.LOCKED)) {
            return ResponseResult.getErrorResult("该单据已锁单,不能修改!");
        }
        Date currentTime = new Date();
        String username = SecurityUtils.getUsername();
        if (dto.getState().equals(CommonConstant.BoundStatus.LOCKED)) {
            dto.setLockDate(currentTime);
            //获取该单据下的调出调入物料详情
            List<RecordAllotDetail> list = recordAllotDetailService.list(new LambdaQueryWrapper<RecordAllotDetail>().eq(RecordAllotDetail::getBoundIndex, recordAllotInfo.getBoundIndex()));
            if (list.isEmpty()) {
                return ResponseResult.getErrorResult("该调拨单没有明细数据，无法锁单!");
            }
            for (RecordAllotDetail detail : list) {
                // 调出物料核减库存
                BasicMaterialBatchInventory outBatchInventory = basicMaterialBatchInventoryService.getById(detail.getInventoryId());
                if (outBatchInventory == null) {
                    return ResponseResult.getErrorResult("调出物料批次信息不存在，物料编码: " + detail.getOrignMaterialCode());
                }
                basicMaterialBatchInventoryService.updateMaterialNum(outBatchInventory, detail.getMaterialNum());
                // 调入物料增加库存 
                BasicMaterialBatchInventory inBatchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(detail.getDestContainer(), detail.getDestBatch(), detail.getDestMaterialCode(), detail.getDestProduceDate());
                if (inBatchInventory != null) {
                    Integer newAvailNum = inBatchInventory.getAvailNum() + detail.getMaterialNum();
                    inBatchInventory.setAvailNum(newAvailNum);
                    inBatchInventory.setMaterialNum(inBatchInventory.getMaterialNum() + detail.getMaterialNum());
                    basicMaterialBatchInventoryService.updateById(inBatchInventory);
                } else {
                    BasicMaterialBatchInventory batchInventory = new BasicMaterialBatchInventory();
                    batchInventory.setMaterialCode(detail.getDestMaterialCode());
                    batchInventory.setBatch(detail.getDestBatch());
                    batchInventory.setProduceDate(detail.getDestProduceDate());
                    batchInventory.setUpperIndex(detail.getBoundIndex());
                    batchInventory.setAvailNum(detail.getMaterialNum());
                    batchInventory.setFreezeNum(0);
                    batchInventory.setMaterialNum(detail.getMaterialNum());
                    batchInventory.setContainerCode(detail.getDestContainer());
                    batchInventory.setInDate(currentTime);
                    batchInventory.setCreateTime(currentTime);
                    basicMaterialBatchInventoryService.save(batchInventory);
                }

                // 增加调出物料出库记录
                RecordMaterialInout outMaterial = new RecordMaterialInout();
                outMaterial.setBoundIndex(detail.getBoundIndex());
                outMaterial.setUpperIndex(detail.getOrignUpperIndex());
                outMaterial.setMaterialCode(detail.getOrignMaterialCode());
                outMaterial.setTotalNum(detail.getMaterialNum());
                outMaterial.setInoutType(CommonConstant.InoutType.OUT);
                outMaterial.setDataOrigin(CommonConstant.BusinessSource.WEB);
                outMaterial.setRemark(detail.getRemark());
                outMaterial.setContainerCode(detail.getOrignContainer());
                outMaterial.setRecorder(username);
                outMaterial.setBoundType(CommonConstant.BoundType.WLDB);
                outMaterial.setBatch(detail.getOrignBatch());
                outMaterial.setProduceDate(detail.getOrignProduceDate());
                outMaterial.setRecordDate(currentTime);
                outMaterial.setLockTime(currentTime);
                recordMaterialInoutService.save(outMaterial);

                // 增加调入物料入库记录
                RecordMaterialInout inMaterial = new RecordMaterialInout();
                inMaterial.setBoundIndex(detail.getBoundIndex());
                inMaterial.setUpperIndex(detail.getDestUpperIndex());
                inMaterial.setMaterialCode(detail.getDestMaterialCode());
                inMaterial.setTotalNum(detail.getMaterialNum());
                inMaterial.setInoutType(CommonConstant.InoutType.IN);
                inMaterial.setDataOrigin(CommonConstant.BusinessSource.WEB);
                inMaterial.setRemark(detail.getRemark());
                inMaterial.setContainerCode(detail.getDestContainer());
                inMaterial.setRecorder(username);
                inMaterial.setBoundType(CommonConstant.BoundType.WLDB);
                inMaterial.setBatch(detail.getDestBatch());
                inMaterial.setProduceDate(detail.getDestProduceDate());
                inMaterial.setRecordDate(currentTime);
                inMaterial.setLockTime(currentTime);
                recordMaterialInoutService.save(inMaterial);
            }

        }
        this.updateByPrimaryKey(dto);
        return ResponseResult.getSuccessResult();

    }

    /**
     * 删除调拨单信息
     */
    @Transactional
    public ResponseResult deleteRecordAllotInfo(RecordAllotInfo dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordAllotInfo info = this.getById(dto.getId());
        if (info == null) {
            return ResponseResult.getErrorResult("该单据不存在!");
        }
        if (!info.getState().equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单,不能删除!");
        }
        // 冻结的物料进行解冻
        List<RecordAllotDetail> list = recordAllotDetailService.list(new LambdaQueryWrapper<RecordAllotDetail>().eq(RecordAllotDetail::getBoundIndex, info.getBoundIndex()));
        for (RecordAllotDetail detail : list) {
            BasicMaterialBatchInventory batchInventory = basicMaterialBatchInventoryService.getById(detail.getInventoryId());
            if (batchInventory == null){
                return ResponseResult.getErrorResult("调拨原物料批次信息不存在");
            }
            basicMaterialBatchInventoryService.unfreezeMaterial(batchInventory.getId(), detail.getMaterialNum());
        }

        this.removeById(info.getId());

        // 删除物料调拨详情记录
        recordAllotDetailService.removeByBoundIndex(info.getBoundIndex());
        return ResponseResult.getSuccessResult();

    }

    /**
     * 通过主键修改
     */
    public void updateByPrimaryKey(RecordAllotInfo record) {
        recordAllotInfoMapper.updateByPrimaryKey(record);
    }

    /**
     * 查询调拨单信息
     */
    public List<RecordAllotInfo> queryRecordAllotInfo(QueryParamVO queryParamVO) {
        return recordAllotInfoMapper.queryRecordAllotInfo(queryParamVO);
    }
}
