package com.ruoyi.service.document;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *单据明细详情表
 */
@Data
public class DocumentDetailResponse {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 父表单据编号
     */
    private String documentCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料编码
     */
    private String materialName;

    /**
     * 物料类型
     */
    private Integer materialSort;


    /**
     * 规格型号
     */
    private String specifications;

    /**
     * 材质
     */
    private String texture;


    /**
     * 全部数量
     */
    private Integer quantity;
    /**
     * 已完成数量
     */
    private Integer completedNum;
    /**
     * 本次数量
     */
    private Integer currentNum;


    /**
     * 单据编号
     */
    private String transactionCode;

    /**
     * 供销编码
     */
    private String supplySalesCode;
}