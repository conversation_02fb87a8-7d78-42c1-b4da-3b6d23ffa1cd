package com.ruoyi.service.document;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.mapper.document.RecordInoutDetailMapper;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.document.RecordInoutDetailVo;
import com.ruoyi.vo.warehouse.ContainerLocationInfoDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 出入库记录详情service
 */
@Service
public class RecordInoutDetailService extends ServiceImpl<RecordInoutDetailMapper, RecordInoutDetail> {

    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;

    @Resource
    private RecordInoutDetailMapper recordInoutDetailMapper;
    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = this.recordInoutDetailMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }


    /**
     * 通过主键修改
     */
    public void updateByPrimaryKey(RecordInoutDetail record) {
         recordInoutDetailMapper.updateByPrimaryKey(record);
    }

    /**
     * 通过单据编号删除出入库记录详情信息
     * @param boundIndex 单据编号
     */
    public void removeByBoundIndex(String boundIndex) {
        recordInoutDetailMapper.removeByBoundIndex(boundIndex);
    }

    /**
     * 查询出入库详情记录
     * @param queryParamVO
     * @return
     */
    public List<RecordInoutDetailVo> queryRecordInoutDetail(QueryParamVO queryParamVO) {
        List<RecordInoutDetailVo> recordInoutDetailVos = recordInoutDetailMapper.queryRecordInoutDetail(queryParamVO);
        for (RecordInoutDetailVo recordInoutDetailVo : recordInoutDetailVos){
            RecordInoutDetail recordInoutDetail = new RecordInoutDetail();
            BeanUtils.copyProperties(recordInoutDetailVo,recordInoutDetail);
            BasicMaterialBatchInventory inventoryByRecordInoutDetail = basicMaterialBatchInventoryService.getInventoryByRecordInoutDetail(recordInoutDetail);
            if (inventoryByRecordInoutDetail == null){
                recordInoutDetailVo.setAvailNum(0);
            }else {
                recordInoutDetailVo.setAvailNum(inventoryByRecordInoutDetail.getAvailNum());
            }
            //补充位置信息
            ContainerLocationInfoDto containerLocationInfoDto = basicWarehouseContainerMapper.queryContainerLocationInfo(recordInoutDetailVo.getContainerCode());
            BeanUtils.copyProperties(containerLocationInfoDto,recordInoutDetailVo);
        }
        return recordInoutDetailVos;
    }

}
