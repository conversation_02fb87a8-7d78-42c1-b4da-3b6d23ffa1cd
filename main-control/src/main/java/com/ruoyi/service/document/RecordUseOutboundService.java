package com.ruoyi.service.document;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.domain.document.RecordUseOutbound;
import com.ruoyi.mapper.document.RecordUseOutboundMapper;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.work.RecordMaterialInoutService;
import com.ruoyi.utils.*;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.MaterialDetailDto;
import com.ruoyi.vo.document.RecordUseOutboundDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class RecordUseOutboundService extends ServiceImpl<RecordUseOutboundMapper, RecordUseOutbound> {

    @Resource
    RecordUseOutboundMapper recordUseOutboundMapper;
    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    RecordInoutDetailService recordInoutDetailService;
    @Resource
    RecordMaterialInoutService recordMaterialInoutService;

    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = recordUseOutboundMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }

    /**
     * 添加仓库用料单信息
     *
     * @param dto
     * @return
     */
    @Transactional
    public ResponseResult addRecordUseOutbound(RecordUseOutboundDto dto) {
        // 生成单据
        String boundIndex = getMaxIndex(CommonConstant.CodePrefix.WAREHOUSE_OUTBOUND_PREFIX);
        List<MaterialDetailDto> detailList = dto.getDetailList();
        if (detailList == null) {
            return ResponseResult.getErrorResult("仓库用料物料明细不能为空");
        }
        // 获取明细当中的所有物料数量
        int totalNum = 0;
        for (MaterialDetailDto detailDto : detailList) {
            totalNum += detailDto.getMaterialNum();
        }
        RecordUseOutbound inOut = new RecordUseOutbound();
        BeanUtils.copyProperties(dto, inOut);
        inOut.setRecorder(SecurityUtils.getUsername());
        inOut.setRecordDate(new Date());
        inOut.setState(CommonConstant.BoundStatus.PENDING_RE);
        inOut.setTotalNum(totalNum);
        inOut.setBoundIndex(boundIndex);
        // 保存入库
        this.save(inOut);
        // 处理出入库记录详情
        for (MaterialDetailDto materialDetailDto : detailList) {
            RecordInoutDetail recordInoutDetail = new RecordInoutDetail();
            BeanUtils.copyProperties(materialDetailDto, recordInoutDetail);
            recordInoutDetail.setBoundIndex(boundIndex);
            recordInoutDetail.setBoundType(CommonConstant.BoundType.CKYL);
            recordInoutDetail.setInventoryId(materialDetailDto.getInventoryId());
            basicMaterialBatchInventoryService.freezeMaterial(materialDetailDto.getInventoryId(),materialDetailDto.getMaterialNum());
            recordInoutDetailService.save(recordInoutDetail);
        }
        return ResponseResult.getSuccessResult();
    }


    /**
     * 修改仓库用料单信息
     *
     * @param dto
     * @return
     */
    @Transactional
    public ResponseResult updateRecordUseOutbound(RecordUseOutbound dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordUseOutbound recordUseOutbound = recordUseOutboundMapper.selectById(dto.getId());
        if (recordUseOutbound == null) {
            return ResponseResult.getErrorResult("该单据不存在，请刷新页面!");
        }
        if (!recordUseOutbound.getState().equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单,不能修改!");
        }
        if (dto.getState().equals(CommonConstant.BoundStatus.LOCKED)) {
            Date date = new Date();
            String checker = SecurityUtils.getUsername();
            dto.setLockDate(date);
            //获取该单据下的详情物料信息
            List<RecordInoutDetail> list = recordInoutDetailService.list(new LambdaQueryWrapper<RecordInoutDetail>().eq(RecordInoutDetail::getBoundIndex, recordUseOutbound.getBoundIndex()));
            //出库修改物料库存数量
            for (RecordInoutDetail recordInoutDetail : list) {
                ResponseResult responseResult = basicMaterialBatchInventoryService.updateMaterialNum(recordInoutDetail);
                if (responseResult.getCode().equals(ResultMsg.errorCode)){
                    return responseResult;
                }
            }
            //增加物料出入库记录
            recordMaterialInoutService.insertByRecordInoutDetailList(list, CommonConstant.InoutType.OUT, CommonConstant.BusinessSource.WEB,date,checker);
        }
        recordUseOutboundMapper.updateByPrimaryKey(dto);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 删除仓库用料信息
     */
    @Transactional
    public ResponseResult deleteRecordUseOutbound(RecordUseOutbound dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordUseOutbound info = this.getById(dto.getId());
        if (info == null) {
            return ResponseResult.getErrorResult("该单据不存在!");
        }
        if (!info.getState().equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单,不能删除!");
        }
        //冻结的物料进行解冻
        List<RecordInoutDetail> list = recordInoutDetailService.list(new LambdaQueryWrapper<RecordInoutDetail>().eq(RecordInoutDetail::getBoundIndex, info.getBoundIndex()));
        basicMaterialBatchInventoryService.unfreezeMaterialBatch(list);
        this.removeById(info.getId());
        //删除出入库记录详情信息
        recordInoutDetailService.removeByBoundIndex(info.getBoundIndex());
        return ResponseResult.getSuccessResult();
    }

    /**
     * 查询仓库用料单信息
     *
     * @param queryParamVO
     * @return
     */
    public List<RecordUseOutbound> queryRecordUseOutbound(QueryParamVO queryParamVO) {
        LambdaQueryWrapper<RecordUseOutbound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtil.isNotEmpty(queryParamVO.getKeyWord()), RecordUseOutbound::getBoundIndex, queryParamVO.getKeyWord());
        if (queryParamVO.getState() != null) {
            wrapper.eq(RecordUseOutbound::getState, queryParamVO.getState());
        }
        wrapper.ge(StringUtil.isNotEmpty(queryParamVO.getBdate()), RecordUseOutbound::getRecordDate, queryParamVO.getBdate());
        wrapper.le(StringUtil.isNotEmpty(queryParamVO.getEdate()), RecordUseOutbound::getRecordDate, queryParamVO.getEdate());
        wrapper.orderByDesc(RecordUseOutbound::getRecordDate);
        return this.list(wrapper);
    }

    /**
     * 添加仓库用料物料明细
     *
     * @param dto
     * @return
     */
    @Transactional
    public ResponseResult addRecordUseOutboundDetail(MaterialDetailDto dto) {
        LambdaQueryWrapper<RecordUseOutbound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordUseOutbound::getBoundIndex, dto.getBoundIndex());
        RecordUseOutbound one = this.getOne(wrapper);
        if (one == null) {
            return ResponseResult.getErrorResult("该单据不存在,无法新增!");
        }
        if (!one.getState().equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单，不能添加详情!");
        }
        RecordInoutDetail recordInoutDetail = new RecordInoutDetail();
        BeanUtils.copyProperties(dto, recordInoutDetail);
        recordInoutDetail.setBoundType(CommonConstant.BoundType.CKYL);
        //新增详情后需要对数量进行相应的冻结
        basicMaterialBatchInventoryService.freezeMaterial(dto.getInventoryId(), dto.getMaterialNum());
        one.setTotalNum(one.getTotalNum() + dto.getMaterialNum());
        recordUseOutboundMapper.updateById(one);
        recordInoutDetailService.save(recordInoutDetail);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 更新仓库用料物料明细
     *
     * @param dto
     * @return
     */
    @Transactional
    public ResponseResult updateRecordUseOutboundDetail(RecordInoutDetail dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordInoutDetail detail = recordInoutDetailService.getById(dto.getId());
        //原有的物料解冻，冻结修改后的物料
        basicMaterialBatchInventoryService.unfreezeMaterial(detail.getInventoryId(), detail.getMaterialNum());
        basicMaterialBatchInventoryService.freezeMaterial(detail.getInventoryId(), dto.getMaterialNum());

        recordInoutDetailService.updateByPrimaryKey(dto);
        //修改对应生产单物料总量
        RecordUseOutbound one = this.getOne(new LambdaQueryWrapper<RecordUseOutbound>().eq(RecordUseOutbound::getBoundIndex, detail.getBoundIndex()));
        Integer totalNum = one.getTotalNum() - detail.getMaterialNum() + dto.getMaterialNum();
        one.setTotalNum(totalNum);
        this.updateById(one);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 删除仓库用料物料明细
     *
     * @param recordInoutDetail
     * @return
     */
    @Transactional
    public ResponseResult deleteRecordUseOutboundDetail(RecordInoutDetail recordInoutDetail) {
        String id = recordInoutDetail.getId();
        if (StringUtil.isEmpty(id)) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordInoutDetail detail = recordInoutDetailService.getById(id);
        LambdaQueryWrapper<RecordUseOutbound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordUseOutbound::getBoundIndex, detail.getBoundIndex());
        RecordUseOutbound one = this.getOne(wrapper);
        Integer state = one.getState();
        if (!state.equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单，不能删除详情!");
        }
        //修改出入库总数量
        Integer totalNum = one.getTotalNum();
        one.setTotalNum(totalNum - detail.getMaterialNum());
        //解冻物料
        basicMaterialBatchInventoryService.unfreezeMaterial(detail.getInventoryId(), detail.getMaterialNum());

        recordUseOutboundMapper.updateById(one);
        recordInoutDetailService.removeById(id);
        return ResponseResult.getSuccessResult();
    }
}
