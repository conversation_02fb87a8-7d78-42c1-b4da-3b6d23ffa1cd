package com.ruoyi.service.other;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.other.QcTemplateRelation;
import com.ruoyi.mapper.other.QcTemplateRelationMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [功能描述]
 * @date 2024/5/6 16:17
 */
@Service
public class QcTemplateRelationService {
    @Resource
    private QcTemplateRelationMapper qcTemplateRelationMapper;

    public ResponseResult addQcTemplateRelation(QcTemplateRelation qcTemplateRelation){
        qcTemplateRelation.setId(LocalStringUtils.getDataUUID());
        qcTemplateRelation.setReceTime(DateAndTimeUtil.getDetailTime());
        int result = this.qcTemplateRelationMapper.insert(qcTemplateRelation);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    public List<QcTemplateRelation> queryBindQcTemplateList(String dataPrimaryId,Integer dataType){
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotEmpty(dataPrimaryId)){
            queryWrapper.eq("data_primary_id",dataPrimaryId);
        }
        if(dataType != null){
            queryWrapper.eq("data_type",dataType);
        }
        return this.qcTemplateRelationMapper.selectList(queryWrapper);
    }

    public ResponseResult deleteQcTemplateRelation(BatchIdsReq req) {
        int count = this.qcTemplateRelationMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    public ResponseResult uptQcTemplateRelation(QcTemplateRelation qcTemplateRelation) {
        int count = this.qcTemplateRelationMapper.updateById(qcTemplateRelation);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }
}
