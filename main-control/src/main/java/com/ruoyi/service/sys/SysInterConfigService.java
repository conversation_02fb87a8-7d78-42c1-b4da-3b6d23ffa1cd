package com.ruoyi.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.domain.sys.SysInterConfig;
import com.ruoyi.mapper.sys.SysInterConfigMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SysInterConfigService {

    @Resource
    private SysInterConfigMapper sysInterConfigMapper;

    public String getInterConfigValByType(String interType){

        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("inter_type",interType);
        SysInterConfig sysInterConfig = this.sysInterConfigMapper.selectOne(wrapper);
        if(sysInterConfig != null){
            return sysInterConfig.getInterUrl();
        }
        return null;
    }
}
