package com.ruoyi.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.sys.SysFileInfo;
import com.ruoyi.mapper.sys.SysFileInfoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [功能描述]
 * @date 2024/5/6 16:17
 */
@Service
public class SysFileInfoService {
    @Resource
    private SysFileInfoMapper sysFileInfoMapper;

    public int addFileInfo(SysFileInfo sysFileInfo){
        sysFileInfo.setFileId(LocalStringUtils.getDataUUID());
        return this.sysFileInfoMapper.insert(sysFileInfo);
    }

    public SysFileInfo queryFileInfo(String fileId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotEmpty(fileId)){
            queryWrapper.eq("file_id",fileId);
        }
        queryWrapper.orderByDesc("record_time");
        return this.sysFileInfoMapper.selectOne(queryWrapper);
    }

    public void updataByFileId(SysFileInfo sysFileInfo) {
        this.sysFileInfoMapper.updateById(sysFileInfo);
    }

    public void deleteFileInfoById(String id) {
        this.sysFileInfoMapper.deleteById(id);
    }

    public List<SysFileInfo> queryFileInfoByDataId(String dataPrimaryId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotEmpty(dataPrimaryId)){
            queryWrapper.eq("data_primary_id",dataPrimaryId);
        }
        queryWrapper.orderByDesc("record_time");
        return this.sysFileInfoMapper.selectList(queryWrapper);
    }
}
