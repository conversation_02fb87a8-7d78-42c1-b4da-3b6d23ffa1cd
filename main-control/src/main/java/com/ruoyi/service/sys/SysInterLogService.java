package com.ruoyi.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.sys.SysInterLog;
import com.ruoyi.mapper.sys.SysInterLogMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class SysInterLogService {

    @Resource
    private SysInterLogMapper sysInterLogMapper;

    public ResponseResult addSysInterLog(SysInterLog sysInterLog){
        sysInterLog.setId(LocalStringUtils.getDataUUID());
        sysInterLog.setRecordTime(DateAndTimeUtil.getNowDate());
        int result = this.sysInterLogMapper.insert(sysInterLog);
        if(result >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("保存数据失败，请重试");
    }

    public void addSysInterLog(String product_id, String request, String response, int signal_type,
                                  Date resTime, int is_web, Date reqTime) {
        this.addSysInterLog(product_id,null,request,null,response,signal_type,resTime,is_web,reqTime);
    }

    /**
     * 记录请求和响应描述
     */
    public void addSysInterLog(String product_id, String reqDesc,String request, String resDesc,String response,
                               int signal_type, Date resTime, int is_web, Date reqTime) {
        this.addSysInterLog(product_id, null,reqDesc,request, resDesc,response,signal_type,resTime,is_web,reqTime);
    }

    /**
     * 记录任务号、请求和响应描述
     */
    public void addSysInterLog(String product_id, String taskNo,String reqDesc,String request, String resDesc,String response,
                               int signal_type, Date resTime, int is_web, Date reqTime) {
        SysInterLog sysInterLog = new SysInterLog();
        sysInterLog.setTaskNo(taskNo);
        sysInterLog.setSignalType(signal_type);
        sysInterLog.setProductId(product_id);
        sysInterLog.setRequest(request);
        sysInterLog.setReqTime(reqTime);
        sysInterLog.setResponse(response);
        sysInterLog.setResTime(resTime);
        sysInterLog.setIsWeb(is_web);
        sysInterLog.setReqDesc(reqDesc);
        sysInterLog.setResDesc(resDesc);
        this.addSysInterLog(sysInterLog);
    }

    public ResponseResult deleteSysInterLog(BatchIdsReq req) {
        int count = this.sysInterLogMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    public ResponseResult uptSysInterLog(SysInterLog devVulnerInfo) {
        int count = this.sysInterLogMapper.updateById(devVulnerInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    public List<SysInterLog> querySysInterLog(QueryParamVO param) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotEmpty(param.getKeyWord())){
            queryWrapper.eq("product_id",param.getKeyWord());
        }
        if(StringUtils.isNotEmpty(param.getKeySubWord())){
            queryWrapper.like("request",param.getKeySubWord());
        }
        if(StringUtils.isNotEmpty(param.getKeyThirdWord())){
            queryWrapper.like("response",param.getKeyThirdWord());
        }
        if(StringUtils.isNotEmpty(param.getKeyFourWord())){
            queryWrapper.eq("signal_type",param.getKeyFourWord());
        }
        if(StringUtils.isNotEmpty(param.getBdate())){
            queryWrapper.ge("record_time",param.getBdate());
        }
        if(StringUtils.isNotEmpty(param.getEdate())){
            queryWrapper.le("record_time",param.getEdate());
        }
        queryWrapper.orderByDesc("record_time");
        return this.sysInterLogMapper.selectList(queryWrapper);
    }
}
