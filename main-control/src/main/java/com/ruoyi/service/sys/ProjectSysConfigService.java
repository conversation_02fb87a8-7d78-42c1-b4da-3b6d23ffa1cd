package com.ruoyi.service.sys;


import com.ruoyi.domain.sys.ProjectSysConfig;
import com.ruoyi.mapper.sys.IProjectSysConfigMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class ProjectSysConfigService  {

    @Resource
    private IProjectSysConfigMapper projectSysConfigMapper;

    public ProjectSysConfig getSysConfigByType(String config_type) {
        return this.projectSysConfigMapper.getSysConfigByType(config_type);
    }

    public ProjectSysConfig getSysConfigByVal(String config_val) {
        return this.projectSysConfigMapper.getSysConfigByVal(config_val);
    }

    public String getSysConfigValByType(String config_type) {
        return this.projectSysConfigMapper.getSysConfigValByType(config_type);
    }

    public void uptSysConfig(String config_value, String config_type) {
        this.projectSysConfigMapper.uptSysConfig(config_value,config_type);
    }

    public String getSysConfigTypeByVal(@Param("config_value")String config_value){
        return this.projectSysConfigMapper.getSysConfigTypeByVal(config_value);
    }
}
