package com.ruoyi.service.sys;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.sys.SysAppVersionInfo;
import com.ruoyi.domain.sys.SysFileInfo;
import com.ruoyi.mapper.sys.SysAppVersionInfoMapper;
import com.ruoyi.utils.QueryParamVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [功能描述]
 * @date 2024/11/25 13:56
 */
@Service
public class SysAppVersionInfoService {
    @Resource
    private SysAppVersionInfoMapper appVersionInfoMapper;
    @Resource
    private SysFileInfoService sysFileInfoService;

    public SysAppVersionInfoService() {
    }

    public List<SysAppVersionInfo> queryAppInfoByPage(QueryParamVO queryParamVO) {
        return this.appVersionInfoMapper.queryAppInfoByPage(queryParamVO);
    }

    public void insertAppInfo(SysAppVersionInfo appVersionInfo) {
        appVersionInfo.setId(String.valueOf(UUID.randomUUID()));
        appVersionInfo.setUpt_time(new Date());
        if(StringUtils.isNotEmpty(appVersionInfo.getFile_id())){
            SysFileInfo sysFileInfo = this.sysFileInfoService.queryFileInfo(appVersionInfo.getFile_id());
            appVersionInfo.setApk_name(sysFileInfo.getFileName());
            appVersionInfo.setApk_path(sysFileInfo.getFilePath());
        }
        this.appVersionInfoMapper.insert(appVersionInfo);
    }

    public void deleteAppInfo(String id) {
        this.appVersionInfoMapper.deleteById(id);
    }

    public SysAppVersionInfo getInfoByNum(String app_edition_num) {
        return this.appVersionInfoMapper.getInfoByNum(app_edition_num);
    }
}
