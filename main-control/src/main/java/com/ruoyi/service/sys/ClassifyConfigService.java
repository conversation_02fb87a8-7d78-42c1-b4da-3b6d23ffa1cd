package com.ruoyi.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.domain.common.ClassifyConfig;
import com.ruoyi.mapper.common.ClassifyConfigMapper;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [分类配置参数]
 * @date 2024/5/6 16:17
 */
@Service
public class ClassifyConfigService {
    @Resource
    private ClassifyConfigMapper classifyConfigMapper;

    public int addConfigParam(ClassifyConfig classifyConfig){
        classifyConfig.setId(LocalStringUtils.getDataUUID());
        classifyConfig.setCreateTime(DateAndTimeUtil.getNowDate());
        return this.classifyConfigMapper.insert(classifyConfig);
    }

    public void updataByParamId(ClassifyConfig classifyConfig) {
        this.classifyConfigMapper.updateById(classifyConfig);
    }

    public ResponseResult deleteConfigParamById(BatchIdsReq req) {
        int count = this.classifyConfigMapper.deleteBatchIds(req.getIds());
        if(count >= req.getIds().size()){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("部分数据删除失败，请重试");
    }

    public List<String> queryValsByType(int classifyType) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("classify_val");
        queryWrapper.eq("classify_type",classifyType);
        queryWrapper.orderByDesc("classify_type");
        List<Object> objectList = this.classifyConfigMapper.selectObjs(queryWrapper);
        return objectList.stream()
                .map(obj -> obj != null ? obj.toString() : null)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<ClassifyConfig> queryListData(QueryParamVO queryParamVO) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("classify_type",queryParamVO.getKeyWord());
        return this.classifyConfigMapper.selectList(queryWrapper);
    }
}
