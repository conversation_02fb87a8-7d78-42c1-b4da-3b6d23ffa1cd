package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.SimpleProductionInStockDetailService;
import com.ruoyi.service.bill.SimpleProductionInStockService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionInStockDetailVo;
import com.ruoyi.vo.bill.SimpleProductionInStockVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 简单生产入库单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/simple/production/instock")
public class SimpleProductionInStockController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SimpleProductionInStockController.class);

    @Resource
    private SimpleProductionInStockService simpleProductionInStockService;

    @Resource
    private SimpleProductionInStockDetailService simpleProductionInStockDetailService;

    /**
     * 查询简单生产入库单列表
     * @return {@link SimpleProductionInStockVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<SimpleProductionInStockVo> list = simpleProductionInStockService.querySimpleProductionInStockList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询简单生产入库单明细列表
     * @return {@link SimpleProductionInStockDetailVo}
     */
    @PostMapping("/detail/list")
    public TableDataInfo detailList(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<SimpleProductionInStockDetailVo> list = simpleProductionInStockDetailService.querySimpleProductionInStockDetailList(queryParamVO);
        return getDataTable(list);
    }

}
