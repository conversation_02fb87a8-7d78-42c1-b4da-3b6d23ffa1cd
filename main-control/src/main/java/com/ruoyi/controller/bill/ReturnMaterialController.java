package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.ReturnMaterialService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnMaterialDetailVo;
import com.ruoyi.vo.bill.ReturnMaterialVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退料申请单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/returnmaterial")
public class ReturnMaterialController extends BaseController {

    @Resource
    private ReturnMaterialService returnMaterialService;

    /**
     * 查询退料申请单列表
     * @return {@link ReturnMaterialVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ReturnMaterialVo> list = returnMaterialService.queryReturnMaterialList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询退料申请单明细
     * @return {@link ReturnMaterialDetailVo}
     */
    @PostMapping("/detail")
    public TableDataInfo detail(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ReturnMaterialDetailVo> list = returnMaterialService.queryReturnMaterialDetail(queryParamVO);
        return getDataTable(list);
    }


}
