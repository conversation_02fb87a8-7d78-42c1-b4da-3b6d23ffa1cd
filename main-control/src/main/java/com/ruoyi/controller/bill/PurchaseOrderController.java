package com.ruoyi.controller.bill;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.PurchaseOrderService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.PurchaseOrderDetailVo;
import com.ruoyi.vo.bill.PurchaseOrderVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购订单接口
 */
@RestController
@RequestMapping("/speedbot/erpbill/purchase/")
public class PurchaseOrderController extends BaseController {

    @Resource
    private PurchaseOrderService purchaseOrderService;

    /**
     * 查询采购订单列表
     * @return {@link PurchaseOrderVo}
     */
    @PostMapping("/list")
    public TableDataInfo queryPurchaseOrder(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize());
        List<PurchaseOrderVo> list = purchaseOrderService.queryPurchaseOrderList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询采购订单明细
     * @return {@link PurchaseOrderDetailVo}
     */
    @PostMapping("/detail")
    public TableDataInfo queryPurchaseOrderDetail(@RequestBody QueryParamVO queryParamVO) {
        List<PurchaseOrderDetailVo> list = purchaseOrderService.queryPurchaseOrderDetail(queryParamVO);
        return getDataTable(list);
    }
}