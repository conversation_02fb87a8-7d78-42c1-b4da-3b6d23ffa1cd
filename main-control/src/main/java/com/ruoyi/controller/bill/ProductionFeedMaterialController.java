package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.ProductionFeedMaterialService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionFeedMaterialDetailVo;
import com.ruoyi.vo.bill.ProductionFeedMaterialVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产补料单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/production/feed")
public class ProductionFeedMaterialController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ProductionFeedMaterialController.class);

    @Resource
    private ProductionFeedMaterialService productionFeedMaterialService;

    /**
     * 查询生产补料单列表
     * @return {@link ProductionFeedMaterialVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ProductionFeedMaterialVo> list = productionFeedMaterialService.queryProductionFeedMaterialList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询生产补料单明细列表
     * @return {@link ProductionFeedMaterialDetailVo}
     */
    @PostMapping("/detail/list")
    public TableDataInfo detailList(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ProductionFeedMaterialDetailVo> list = productionFeedMaterialService.queryProductionFeedMaterialDetailList(queryParamVO);
        return getDataTable(list);
    }

}
