package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.DeliveryNoticeService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.DeliveryNoticeDetailVo;
import com.ruoyi.vo.bill.DeliveryNoticeVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发货通知单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/delivery")
public class DeliveryNoticeController extends BaseController {

    @Resource
    private DeliveryNoticeService deliveryNoticeService;

    /**
     * 查询发货通知单列表
     *  @return {@link DeliveryNoticeVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<DeliveryNoticeVo> list = deliveryNoticeService.queryDeliveryNoticeList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询发货通知单明细
     * @return {@link DeliveryNoticeDetailVo}
     */
    @PostMapping("/detail")
    public TableDataInfo detail(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<DeliveryNoticeDetailVo> list = deliveryNoticeService.queryDeliveryNoticeDetail(queryParamVO);
        return getDataTable(list);
    }

  
}