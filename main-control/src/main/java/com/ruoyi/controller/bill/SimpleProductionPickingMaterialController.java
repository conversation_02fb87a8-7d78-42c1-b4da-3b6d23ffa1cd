package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.SimpleProductionPickingMaterialService;
import com.ruoyi.service.bill.SimpleProductionPickingMaterialDetailService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.SimpleProductionPickingMaterialVo;
import com.ruoyi.vo.bill.SimpleProductionPickingMaterialDetailVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 简单生产领料单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/simple/production/picking")
public class SimpleProductionPickingMaterialController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SimpleProductionPickingMaterialController.class);

    @Resource
    private SimpleProductionPickingMaterialService simpleProductionPickingMaterialService;

    @Resource
    private SimpleProductionPickingMaterialDetailService simpleProductionPickingMaterialDetailService;

    /**
     * 查询简单生产领料单列表
     * @return {@link SimpleProductionPickingMaterialVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<SimpleProductionPickingMaterialVo> list = simpleProductionPickingMaterialService.querySimpleProductionPickingMaterialList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询简单生产领料单明细列表
     * @return {@link SimpleProductionPickingMaterialDetailVo}
     */
    @PostMapping("/detail/list")
    public TableDataInfo detailList(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<SimpleProductionPickingMaterialDetailVo> list = simpleProductionPickingMaterialDetailService.querySimpleProductionPickingMaterialDetailList(queryParamVO);
        return getDataTable(list);
    }


}
