package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.ProductionPickingMaterialService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionPickingMaterialDetailVo;
import com.ruoyi.vo.bill.ProductionPickingMaterialVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产领料单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/production/picking")
public class ProductionPickingMaterialController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ProductionPickingMaterialController.class);

    @Resource
    private ProductionPickingMaterialService productionPickingMaterialService;

    /**
     * 查询生产领料单列表
     * @return {@link ProductionPickingMaterialVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ProductionPickingMaterialVo> list = productionPickingMaterialService.queryProductionPickingMaterialList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询生产领料单明细列表
     * @return {@link ProductionPickingMaterialDetailVo}
     */
    @PostMapping("/detail/list")
    public TableDataInfo detailList(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ProductionPickingMaterialDetailVo> list = productionPickingMaterialService.queryProductionPickingMaterialDetailList(queryParamVO);
        return getDataTable(list);
    }

}
