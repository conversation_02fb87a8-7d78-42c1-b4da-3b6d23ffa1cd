package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.ReturnNoticeService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReturnNoticeDetailVo;
import com.ruoyi.vo.bill.ReturnNoticeVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退货通知单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/return")
public class ReturnNoticeController extends BaseController {

    @Resource
    private ReturnNoticeService returnNoticeService;

    /**
     * 查询退货通知单列表
     * @return {@link ReturnNoticeVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ReturnNoticeVo> list = returnNoticeService.queryReturnNoticeList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询退货通知单明细
     * @return {@link ReturnNoticeDetailVo}
     */
    @PostMapping("/detail")
    public TableDataInfo detail(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ReturnNoticeDetailVo> list = returnNoticeService.queryReturnNoticeDetail(queryParamVO);
        return getDataTable(list);
    }


}
