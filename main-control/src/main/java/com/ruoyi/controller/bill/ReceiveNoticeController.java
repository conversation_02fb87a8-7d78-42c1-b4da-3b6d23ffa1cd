package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.ReceiveNoticeService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ReceiveNoticeDetailVo;
import com.ruoyi.vo.bill.ReceiveNoticeVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 收料通知单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/receive")
public class ReceiveNoticeController extends BaseController {

    @Resource
    private ReceiveNoticeService receiveNoticeService;

    /**
     * 查询收料通知单列表
     * @return {@link ReceiveNoticeVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ReceiveNoticeVo> list = receiveNoticeService.queryReceiveNoticeList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询收料通知单明细
     * @return {@link ReceiveNoticeDetailVo}
     */
    @PostMapping("/detail")
    public TableDataInfo detail(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ReceiveNoticeDetailVo> list = receiveNoticeService.queryReceiveNoticeDetail(queryParamVO);
        return getDataTable(list);
    }

}
