package com.ruoyi.controller.bill;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.service.bill.ProductionInStockService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.bill.ProductionInStockDetailVo;
import com.ruoyi.vo.bill.ProductionInStockVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产入库单
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/erpbill/production/instock")
public class ProductionInStockController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ProductionInStockController.class);

    @Resource
    private ProductionInStockService productionInStockService;

    /**
     * 查询生产入库单列表
     * @return {@link ProductionInStockVo}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ProductionInStockVo> list = productionInStockService.queryProductionInStockList(queryParamVO);
        return getDataTable(list);
    }

    /**
     * 查询生产入库单明细列表
     * @return {@link ProductionInStockDetailVo}
     */
    @PostMapping("/detail/list")
    public TableDataInfo detailList(@RequestBody QueryParamVO queryParamVO) {
        startPage();
        List<ProductionInStockDetailVo> list = productionInStockService.queryProductionInStockDetailList(queryParamVO);
        return getDataTable(list);
    }


}
