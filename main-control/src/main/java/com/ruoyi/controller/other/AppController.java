package com.ruoyi.controller.other;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.sys.SysAppVersionInfo;
import com.ruoyi.service.sys.SysAppVersionInfoService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [APP相关接口]
 * @date 2024/11/25 16:34
 */
@RestController
@RequestMapping("/speedbot/mainControl/sys/app")
public class AppController extends BaseController {

    @Resource
    private SysAppVersionInfoService appVersionInfoService;

    @PostMapping({"/deleteAppInfo"})
    public ResponseResult deleteAppInfo(@RequestBody SysAppVersionInfo appVersionInfo) {
        if (StringUtils.isEmpty(appVersionInfo.getId())) {
            return ResponseResult.getErrorResult("参数不能为空！");
        } else {
            this.appVersionInfoService.deleteAppInfo(appVersionInfo.getId());
            return ResponseResult.getSuccessResult();
        }
    }

    @PostMapping({"/queryAppInfoByPage"})
    public TableDataInfo queryAppInfoByPage(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize());
        return this.getDataTable(this.appVersionInfoService.queryAppInfoByPage(queryParamVO));
    }

    @PostMapping({"/insertAppInfo"})
    public ResponseResult insertAppInfo(@RequestBody SysAppVersionInfo appVersionInfo) {
        if (StringUtils.isEmpty(appVersionInfo.getUpt_info())) {
            return ResponseResult.getErrorResult("更新信息不能为空！");
        } else {
            SysAppVersionInfo infoByNum = this.appVersionInfoService.getInfoByNum(appVersionInfo.getApp_edition_num());
            if (infoByNum != null) {
                return ResponseResult.getErrorResult("版本号唯一，请确认版本号：" + appVersionInfo.getApp_edition_num());
            } else {
                this.appVersionInfoService.insertAppInfo(appVersionInfo);
                return ResponseResult.getSuccessResult();
            }
        }
    }
}
