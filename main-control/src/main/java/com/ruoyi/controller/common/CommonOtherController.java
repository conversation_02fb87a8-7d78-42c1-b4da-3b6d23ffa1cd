package com.ruoyi.controller.common;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.common.ClassifyConfig;
import com.ruoyi.service.sys.ClassifyConfigService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 通用文件请求处理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/common/other")
public class CommonOtherController extends BaseController {

    @Resource
    private ClassifyConfigService classifyConfigService;

    /**
     * 根据分类查询其下的所有值
     */
    @PostMapping("/queryClassifyConfig")
    public TableDataInfo queryClassifyConfig(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        return this.getDataTable((this.classifyConfigService.queryListData(queryParamVO)));
    }

    /**
     * 根据分类查询其下的所有值
     */
    @PostMapping("/queryPointClassifyConfig")
    public ResponseResult queryPointClassifyConfig(@RequestBody ClassifyConfig req){
        return ResponseResult.getSuccessResult("success",this.classifyConfigService.queryValsByType(req.getClassifyType()));
    }

    @Log(title = "分类配置", businessType = BusinessType.INSERT)
    @PostMapping("/insertClassifyConfig")
    public ResponseResult insertBasicMaterialClassify(@RequestBody ClassifyConfig req){
        this.classifyConfigService.addConfigParam(req);
        return ResponseResult.getSuccessResult();
    }

    @Log(title = "分类配置", businessType = BusinessType.UPDATE)
    @PostMapping("/uptClassifyConfig")
    public ResponseResult uptBasicMaterialClassify(@RequestBody ClassifyConfig req){
        this.classifyConfigService.updataByParamId(req);
        return ResponseResult.getSuccessResult();
    }

    @Log(title = "分类配置", businessType = BusinessType.DELETE)
    @PostMapping("/deleteClassifyConfig")
    public ResponseResult deleteBasicMaterialClassify(@RequestBody BatchIdsReq req){
        return this.classifyConfigService.deleteConfigParamById(req);
    }
}
