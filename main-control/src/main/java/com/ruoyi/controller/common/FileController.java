package com.ruoyi.controller.common;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.domain.sys.SysFileInfo;
import com.ruoyi.service.common.CommonFileService;
import com.ruoyi.service.common.FileDealService;
import com.ruoyi.service.sys.SysFileInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.utils.*;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webRequest.FileInfoRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;

/**
 * 通用文件请求处理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/speedbot/common/file")
public class FileController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(FileController.class);

    @Resource
    private ServerConfig serverConfig;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private CommonFileService commonFileService;
    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private FileDealService fileDealService;

    /**
     * 通用下载请求
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = fileName;
            realFileName = realFileName.substring(realFileName.lastIndexOf("/") + 1);
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-dataService");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        log.info("收到上传文件指令，文件名：" + file.getOriginalFilename());
        try {
            String filePath = RuoYiConfig.getUploadPath();
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            SysFileInfo sysFile = new SysFileInfo();
            String originalFileName = file.getOriginalFilename();
            sysFile.setFileName(file.getOriginalFilename());
            sysFile.setRecordTime(DateAndTimeUtil.getNowDetailTime());
            String origalName = serverConfig.getUrl() + "/profile/upload";
            String dealedFilePath = url
                    .replace(origalName,filePath);
            //截取前面的目录作为文件路径数据
            String subFilePath = filePath + "/2021/08/19";
            sysFile.setFilePath(dealedFilePath.substring(0,subFilePath.length()));
            this.sysFileInfoService.addFileInfo(sysFile);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("id", sysFile.getFileId());
            ajax.put("file_name", originalFileName);
            ajax.put("url", url);
            log.info("上传返回结果：" + ajax.get("file_name") + "--code:" + ajax.get("code"));
            return ajax;
        } catch (Exception e) {
            log.warn("通用文件上传接口异常" + e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String name, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 本地资源路径
        String localPath = RuoYiConfig.getProfile();
        // 数据库资源地址
        String downloadPath = localPath + StringUtils.substringAfter(name, Constants.RESOURCE_PREFIX);
        // 下载名称
        String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-dataService");
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, downloadName));
        FileUtils.writeBytes(downloadPath, response.getOutputStream());
    }

    /**
     * 通用模板下载请求
     * @param fileName 文件名称
     */
    @GetMapping("/downloadTemplateFile")
    public void downloadTemplateFile(String fileName, HttpServletResponse response) {
        try {
            String filePath = sysConfigService.selectConfigByKey(fileName);
            if (StringUtils.isEmpty(filePath)) {
                log.info("文件名:{}下载路径不存在!", fileName);
                return;
            }
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-dataService");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + fileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * FTP上传请求
     */
    @PostMapping("/ftpUpload")
    public AjaxResult ftpUpload(MultipartFile file) throws Exception
    {
        String originalFilename = file.getOriginalFilename();
        log.info("FTP上传请求，收到上传文件指令，文件名：" + originalFilename);
        InputStream inputStream = null;
        File file_new = null;
        file_new = File.createTempFile("temp", null);
        file.transferTo(file_new);
        inputStream = new FileInputStream(file_new);
        String filePath = DateAndTimeUtil.getNowTime();
        boolean uploadFile = commonFileService.uploadFile(filePath,originalFilename,inputStream);
        if (!uploadFile){
            return AjaxResult.error("上传失败！");
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("id", filePath + "/" + originalFilename);
        ajax.put("file_name", originalFilename);
        ajax.put("file_path", filePath + "/" + originalFilename);
        file_new.deleteOnExit();
        return ajax;
    }


    /**
     * FTP下载请求
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/ftpDownload")
    public void ftpDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        if (StringUtils.isEmpty(fileName)){
            throw new CustomException(fileName + " 文件路径为空，不允许下载!", HttpStatus.BAD_REQUEST);
        }
        boolean ftpFileExist = commonFileService.isFtpFileExist(fileName);
        if (!ftpFileExist){
            throw new CustomException(fileName + " 文件路径下无文件，不允许下载!", HttpStatus.BAD_REQUEST);
        }
        //存放到临时文件夹中然后完成之后删掉
        String temporaryPath =  RuoYiConfig.getDxfFile();

        String realFileName =fileName.substring(fileName.lastIndexOf("/") + 1);
        boolean flag = commonFileService.downFtpFile(fileName.substring(0,fileName.lastIndexOf("/") + 1), realFileName,temporaryPath);
        if (!flag){
            log.info("通用ftp下载失败" + fileName);
            return;
        }
        try
        {
            String filePath = temporaryPath + "/" + realFileName;
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 展示FTP中的图片/PDF
     */
    @GetMapping("/ftpImage")
    public void getImageFromFtp(@RequestParam String fileName, HttpServletResponse response) {
        try (InputStream inputStream = commonFileService.getFileStreamFromFTP(fileName)) {
            if (fileName.toLowerCase().endsWith(".pdf")) {
                response.setContentType("application/pdf");
            }else {
                response.setContentType("image/png");
            }
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.flushBuffer();
        } catch (IOException e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 添加附件详情信息
     */
    @PostMapping("/addFileInfo")
    public ResponseResult addFileInfo(@RequestBody FileInfoRequest fileInfoRequest) {
        fileDealService.addFileInfo(fileInfoRequest);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 查询附件详情信息
     */
    @PostMapping("/queryFileInfo")
    public TableDataInfo queryFileInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<SysFileInfo> list = sysFileInfoService.queryFileInfoByDataId(queryParamVO.getKeyWord());
        return this.getDataTable(list);
    }

    /**
     * 删除附件详情信息
     * 数据+文件
     */
    @PostMapping("/deleteFileInfo")
    public ResponseResult deleteFileInfo(@RequestBody BatchIdsReq batchIdsReq) {
        if(batchIdsReq == null || batchIdsReq.getIds() == null || batchIdsReq.getIds().size() == 0){
            return ResponseResult.getErrorResult("缺少ID，无法删除");
        }
        log.info("删除附件，详情记录：" + GsonUtils.toJsonString(batchIdsReq));
        fileDealService.deleteFileInfoById(batchIdsReq.getIds());
        return ResponseResult.getSuccessResult();
    }

}
