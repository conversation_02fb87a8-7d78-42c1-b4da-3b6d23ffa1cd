package com.ruoyi.controller.log;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.domain.sys.SysInterLog;
import com.ruoyi.service.sys.SysInterLogService;
import com.ruoyi.utils.QueryParamVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/17 14:57
 * @Description: 日志管理—接口日志
 */
@RestController
@RequestMapping("/speedbot/log/interLog")
public class InterLogController extends BaseController {
    @Resource
    private SysInterLogService sysInterLogService;

    @PostMapping("/queryInterLog")
    public TableDataInfo queryInterLog(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<SysInterLog> list = this.sysInterLogService.querySysInterLog(queryParamVO);
        return this.getDataTable(list);
    }
}
