package com.ruoyi.controller.erp;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.erp.ErpReportDetail;
import com.ruoyi.domain.erp.ErpReportMain;
import com.ruoyi.service.erp.ErpReportService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * ERP上报记录
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@RestController
@RequestMapping("/speedbot/erp/report")
public class ErpReportController extends BaseController {
    
    @Resource
    private ErpReportService erpReportService;
    
    /**
     * 查询指定单据的ERP上报记录列表（分页）
     * QueryParamVO参数说明：
     * - keyWord: 单据编码（必填，documentCode）
     * - pageNum: 页码
     * - pageSize: 每页大小
     * @return {@link ErpReportMain}
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody QueryParamVO queryParamVO) {
        String documentCode = queryParamVO.getKeyWord();
        if (documentCode == null || documentCode.trim().isEmpty()) {
            return new TableDataInfo();
        }
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<ErpReportMain> list = erpReportService.getReportsByDocumentCode(documentCode);
        return getDataTable(list);
    }

    /**
     * 根据主表ID查询明细列表（分页）
     * QueryParamVO参数说明：
     * - keyWord: 主表ID（必填，上报记录的主键ID）
     * - pageNum: 页码
     * - pageSize: 每页大小
     *
     * @return {@link ErpReportDetail}
     */
    @PostMapping("/details")
    public TableDataInfo getDetails(@RequestBody QueryParamVO queryParamVO) {
        String mainId = queryParamVO.getKeyWord();
        if (mainId == null || mainId.trim().isEmpty()) {
            return new TableDataInfo();
        }
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<ErpReportDetail> details = erpReportService.getDetailsByMainId(mainId);
        return getDataTable(details);
    }
    

    /**
     * 重新上报失败的记录
     * JSONObject参数说明：
     * - mainId: 主表ID（必填，需要重新上报的记录ID）
     */
    @PostMapping("/retry")
    @Log(title = "ERP上报重试", businessType = BusinessType.UPDATE)
    public ResponseResult retryReport(@RequestBody JSONObject jsonObject) {
        try {
            String mainId = jsonObject.getString("mainId");
            if (mainId == null || mainId.trim().isEmpty()) {
                return ResponseResult.getErrorResult("主表ID不能为空");
            }
            erpReportService.executeReportAsync(mainId);
            return ResponseResult.getSuccessResult();
        } catch (Exception e) {
            return ResponseResult.getErrorResult("重新上报失败：" + e.getMessage());
        }
    }

}
