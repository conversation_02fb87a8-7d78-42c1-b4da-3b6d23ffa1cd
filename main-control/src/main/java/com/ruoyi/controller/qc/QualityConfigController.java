package com.ruoyi.controller.qc;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.qc.QcTemplateDetail;
import com.ruoyi.domain.qc.QcTemplateInfo;
import com.ruoyi.domain.qc.QcTemplateItem;
import com.ruoyi.domain.qc.QcTemplateMaterial;
import com.ruoyi.service.qc.QcTemplateDetailService;
import com.ruoyi.service.qc.QcTemplateInfoService;
import com.ruoyi.service.qc.QcTemplateItemService;
import com.ruoyi.service.qc.QcTemplateMaterialService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.qc.QcTemplateAndDetailReq;
import com.ruoyi.vo.qc.QcTemplateDetailVo;
import com.ruoyi.vo.qc.QcTemplateMaterialVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webRequest.qc.BatchAddTemplateDetailReq;
import com.ruoyi.vo.webRequest.qc.BatchAddTemplateMaterialReq;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: psy
 * @CreateDate: 2025/06/17 14:57
 * @Description: 质检管理-质检配置
 */
@RestController
@RequestMapping("/speedbot/qc/templateData")
public class QualityConfigController extends BaseController {

    @Resource
    private QcTemplateInfoService qcTemplateInfoService;
    @Resource
    private QcTemplateDetailService qcTemplateDetailService;
    @Resource
    private QcTemplateItemService qcTemplateItemService;
    @Resource
    private QcTemplateMaterialService qcTemplateMaterialService;


    /**
     * 查询质检模板
     */
    @PostMapping("/queryQuantityTemplate")
    public TableDataInfo queryQuantityTemplate(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<QcTemplateInfo> list = this.qcTemplateInfoService.queryQcTemplateInfo(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "质检模板", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/addQuantityTemplate")
    public ResponseResult addQuantityTemplate(@RequestBody QcTemplateInfo req){
        return this.qcTemplateInfoService.addQcTemplateInfo(req);
    }

    @Log(title = "质检模板", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/uptQuantityTemplate")
    public ResponseResult uptQuantityTemplate(@RequestBody QcTemplateInfo scPlanCompleteSet){
        return this.qcTemplateInfoService.uptQcTemplateInfo(scPlanCompleteSet);
    }

    @Log(title = "质检模板", businessType = BusinessType.DELETE)
    @PostMapping("/deleteQuantityTemplate")
    public ResponseResult deleteQuantityTemplate(@RequestBody BatchIdsReq req){
        return this.qcTemplateInfoService.deleteQcTemplateInfo(req);
    }


    @Log(title = "质检模板", businessType = BusinessType.INSERT)
    @PostMapping("/insertQcTemplateAndDetail")
    public ResponseResult insertQcTemplateAndDetail(@RequestBody QcTemplateAndDetailReq req){
        return this.qcTemplateInfoService.insertQcTemplateAndDetail(req);
    }

    /**
     * 质检模板详情
     */
    @PostMapping("/queryQuantityTemplateDetail")
    public TableDataInfo queryQuantityTemplateDetail(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<QcTemplateDetailVo> list = this.qcTemplateDetailService.queryQcTemplateDetail(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "质检模板详情", businessType = BusinessType.INSERT)
    @PostMapping("/addQuantityTemplateDetail")
    public ResponseResult addQuantityTemplateDetail(@RequestBody BatchAddTemplateDetailReq req){
        return this.qcTemplateDetailService.addQcTemplateDetail(req);
    }

    @Log(title = "质检模板详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/uptQuantityTemplateDetail")
    public ResponseResult uptQuantityTemplateDetail(@RequestBody QcTemplateDetail scPlanCompleteSet){
        return this.qcTemplateDetailService.uptQcTemplateDetail(scPlanCompleteSet);
    }

    @Log(title = "质检模板详情", businessType = BusinessType.DELETE)
    @PostMapping("/deleteQuantityTemplateDetail")
    public ResponseResult deleteQuantityTemplateDetail(@RequestBody BatchIdsReq req){
        return this.qcTemplateDetailService.deleteQcTemplateDetail(req);
    }

    /**
     * 质检项
     */
    @PostMapping("/queryQcTemplateItem")
    public TableDataInfo queryQcTemplateItem(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<QcTemplateItem> list = this.qcTemplateItemService.queryQcTemplateItem(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "质检项", businessType = BusinessType.INSERT)
    @PostMapping("/insertQcTemplateItem")
    public ResponseResult insertQcTemplateItem(@RequestBody QcTemplateItem req){
        return this.qcTemplateItemService.insertQcTemplateItem(req);
    }

    @Log(title = "质检项", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/uptQcTemplateItem")
    public ResponseResult uptQcTemplateItem(@RequestBody QcTemplateItem scPlanCompleteSet){
        return this.qcTemplateItemService.uptQcTemplateItem(scPlanCompleteSet);
    }

    @Log(title = "质检项", businessType = BusinessType.DELETE)
    @PostMapping("/deleteQcTemplateItem")
    public ResponseResult deleteQcTemplateItem(@RequestBody BatchIdsReq req){
        return this.qcTemplateItemService.deleteQcTemplateItem(req);
    }

    /**
     * 质检物料
     */
    @PostMapping("/queryQcTemplateMaterial")
    public TableDataInfo queryQcTemplateMaterial(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<QcTemplateMaterialVo> list = this.qcTemplateMaterialService.queryQcTemplateMaterial(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "质检物料", businessType = BusinessType.INSERT)
    @PostMapping("/insertQcTemplateMaterial")
    public ResponseResult insertQcTemplateMaterial(@RequestBody BatchAddTemplateMaterialReq req){
        return this.qcTemplateMaterialService.insertQcTemplateMaterial(req);
    }

    @Log(title = "质检物料", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/uptQcTemplateMaterial")
    public ResponseResult uptQcTemplateMaterial(@RequestBody QcTemplateMaterial scPlanCompleteSet){
        return this.qcTemplateMaterialService.uptQcTemplateMaterial(scPlanCompleteSet);
    }

    @Log(title = "质检物料", businessType = BusinessType.DELETE)
    @PostMapping("/deleteQcTemplateMaterial")
    public ResponseResult deleteQcTemplateMaterial(@RequestBody BatchIdsReq req){
        return this.qcTemplateMaterialService.deleteQcTemplateMaterial(req);
    }

}
