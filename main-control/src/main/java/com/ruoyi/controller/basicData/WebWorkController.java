package com.ruoyi.controller.basicData;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.domain.document.*;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.document.RecordAllotDetailService;
import com.ruoyi.service.document.RecordAllotInfoService;
import com.ruoyi.service.document.RecordInventoryService;
import com.ruoyi.service.document.RecordUseOutboundService;
import com.ruoyi.service.sys.ProjectSysConfigService;
import com.ruoyi.service.work.RecordMaterialInoutService;
import com.ruoyi.service.work.TaskReportBBSource;
import com.ruoyi.utils.GsonUtils;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ReportUtil;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.MaterialDetailDto;
import com.ruoyi.vo.document.RecordAllotDetailVo;
import com.ruoyi.vo.document.RecordAllotInfoDto;
import com.ruoyi.vo.document.RecordUseOutboundDto;
import com.ruoyi.vo.report.RecordMaterialInoutVo;
import com.ruoyi.vo.warehouse.*;
import com.ruoyi.vo.webResponse.dto.MaterialDetailExcelData;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.util.JRLoader;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 对接前端功能操作
 */
@RestController
@RequestMapping("/speedbot/wms/web")
public class WebWorkController extends BaseController {

    @Resource
    private RecordInventoryService recordInventoryService;
    @Resource
    private RecordUseOutboundService recordUseOutboundService;
    @Resource
    private RecordAllotInfoService recordAllotInfoService;
    @Resource
    private RecordAllotDetailService recordAllotDetailService;
    @Resource
    RecordMaterialInoutService recordMaterialInoutService;
    @Resource
    private BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    ProjectSysConfigService ProjectSysConfigService;


    /**
     * 新增库存盘点功能
     */
    @PostMapping("/addInventoryRecord")
    @Log(title = "库存盘点", businessType = BusinessType.INSERT)
    public ResponseResult addInventoryRecord(@RequestBody RecordInventoryDto recordInventoryDto) {
        logger.info("新增库存盘点信息:" + GsonUtils.toJsonString(recordInventoryDto));
        return recordInventoryService.addInventoryRecord(recordInventoryDto);
    }

    /**
     * 查询库存盘点信息
     */
    @PostMapping("/queryInventoryRecord")
    public TableDataInfo queryInventoryRecord(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordInventoryInfoVo> list = recordInventoryService.queryInventoryRecord(queryParamVO);
        return this.getDataTable(list);
    }


    /**
     * 查询库存盘点详情信息
     */
    @PostMapping("/queryInventoryRecordDetail")
    public TableDataInfo queryInventoryRecordDetail(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordInventoryDetailVo> list = recordInventoryService.queryInventoryRecordDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 更新库存盘点详情信息
     */
    @PostMapping("/updateRecordInventoryDetail")
    @Log(title = "库存盘点信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordInventoryDetail(@RequestBody List<RecordInventoryDetail> param) {
        return recordInventoryService.updateRecordInventoryDetail(param);
    }

    /**
     * 新增库存盘点详情信息
     */
    @PostMapping("/addRecordInventoryDetail")
    @Log(title = "库存盘点信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordInventoryDetail(@RequestBody RecordInventoryDetail param) {
        return recordInventoryService.addRecordInventoryDetail(param);
    }

    /**
     * 更新库存盘点信息
     */
    @PostMapping("/updateRecordInventoryInfo")
    @Log(title = "库存盘点信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordInventoryInfo(@RequestBody RecordInventoryInfo recordInventoryInfo) {
        return recordInventoryService.updateRecordInventoryInfo(recordInventoryInfo);
    }

    /**
     * 删除库存盘点信息
     */
    @PostMapping("/deleteRecordInventoryInfo")
    @Log(title = "库存盘点信息", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordInventoryInfo(@RequestBody RecordInventoryInfo recordInventoryInfo) {
        return recordInventoryService.deleteRecordInventoryInfo(recordInventoryInfo);
    }

    /**
     * 删除库存盘点信息详情
     */
    @PostMapping("/deleteRecordInventoryDetail")
    @Log(title = "库存盘点信息", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordInventoryDetail(@RequestBody RecordInventoryDetail recordInventoryDetail) {
        return recordInventoryService.deleteRecordInventoryDetail(recordInventoryDetail);
    }


    /**
     * 新增仓库用料信息
     */
    @PostMapping("/addRecordUseOutbound")
    @Log(title = "仓库用料", businessType = BusinessType.INSERT)
    public ResponseResult addRecordUseOutbound(@RequestBody RecordUseOutboundDto dto) {
        return recordUseOutboundService.addRecordUseOutbound(dto);
    }

    /**
     * 更新仓库用料信息
     */
    @PostMapping("/updateRecordUseOutbound")
    @Log(title = "仓库用料", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordUseOutbound(@RequestBody RecordUseOutbound recordUseOutbound) {
        return recordUseOutboundService.updateRecordUseOutbound(recordUseOutbound);
    }

    /**
     * 删除仓库用料信息
     */
    @PostMapping("/deleteRecordUseOutbound")
    @Log(title = "仓库用料", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordUseOutbound(@RequestBody RecordUseOutbound recordUseOutbound) {
        return recordUseOutboundService.deleteRecordUseOutbound(recordUseOutbound);
    }

    /**
     * 查询仓库用料信息
     */
    @PostMapping("/queryRecordUseOutbound")
    public TableDataInfo queryRecordUseOutbound(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordUseOutbound> list = recordUseOutboundService.queryRecordUseOutbound(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 新增仓库用料物料详情
     */
    @PostMapping("/addRecordUseOutboundDetail")
    @Log(title = "仓库用料", businessType = BusinessType.INSERT)
    public ResponseResult addRecordUseOutboundDetail(@RequestBody MaterialDetailDto dto) {
        return recordUseOutboundService.addRecordUseOutboundDetail(dto);
    }

    /**
     * 更新仓库用料物料详情
     */
    @PostMapping("/updateRecordUseOutboundDetail")
    @Log(title = "仓库用料", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordUseOutboundDetail(@RequestBody RecordInoutDetail dto) {
        return recordUseOutboundService.updateRecordUseOutboundDetail(dto);
    }

    /**
     * 删除仓库用料物料详情
     */
    @PostMapping("/deleteRecordUseOutboundDetail")
    @Log(title = "仓库用料", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordUseOutboundDetail(@RequestBody RecordInoutDetail recordInoutDetail) {
        return recordUseOutboundService.deleteRecordUseOutboundDetail(recordInoutDetail);
    }

    /**
     * 新增物料调拨信息
     */
    @PostMapping("/addRecordAllotInfo")
    @Log(title = "物料调拨", businessType = BusinessType.INSERT)
    public ResponseResult addRecordAllotInfo(@RequestBody RecordAllotInfoDto dto) {
        return recordAllotInfoService.addRecordAllotInfo(dto);
    }

    /**
     * 更新物料调拨信息
     */
    @PostMapping("/updateRecordAllotInfo")
    @Log(title = "物料调拨", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordAllotInfo(@RequestBody RecordAllotInfo recordAllotInfo) {
        return recordAllotInfoService.updateRecordAllotInfo(recordAllotInfo);
    }

    /**
     * 删除物料调拨信息
     */
    @PostMapping("/deleteRecordAllotInfo")
    @Log(title = "物料调拨", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordAllotInfo(@RequestBody RecordAllotInfo recordAllotInfo) {
        return recordAllotInfoService.deleteRecordAllotInfo(recordAllotInfo);
    }

    /**
     * 查询物料调拨信息
     */
    @PostMapping("/queryRecordAllotInfo")
    public TableDataInfo queryRecordAllotInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordAllotInfo> list = recordAllotInfoService.queryRecordAllotInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 新增物料调拨详情
     */
    @PostMapping("/addRecordAllotDetail")
    @Log(title = "物料调拨", businessType = BusinessType.INSERT)
    public ResponseResult addRecordAllotInfoDetail(@RequestBody List<RecordAllotDetail> dto) {
        return recordAllotDetailService.addRecordAllotInfoDetail(dto);
    }

    /**
     * 更新物料调拨详情
     */
    @PostMapping("/updateRecordAllotDetail")
    @Log(title = "物料调拨", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordAllotInfoDetail(@RequestBody RecordAllotDetail dto) {
        return recordAllotDetailService.updateRecordAllotInfoDetail(dto);
    }

    /**
     * 删除物料调拨详情
     */
    @PostMapping("/deleteRecordAllotDetail")
    @Log(title = "物料调拨", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordAllotInfoDetail(@RequestBody RecordAllotDetail recordAllotDetail) {
        return recordAllotDetailService.deleteRecordAllotInfoDetail(recordAllotDetail);
    }

    /**
     * 查询物料调拨详情
     */
    @PostMapping("/queryRecordAllotDetail")
    public TableDataInfo queryRecordAllotInfoDetail(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordAllotDetailVo> list = recordAllotDetailService.queryRecordAllotInfoDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 出入库记录导出
     */
    @Log(title = "出入库记录", businessType = BusinessType.EXPORT)
    @PostMapping("/recordMaterialInoutExport")
    public AjaxResult recordMaterialInoutExport(@RequestBody QueryParamVO queryParamVO) {
        List<RecordMaterialInoutVo> list = recordMaterialInoutService.queryRecordMaterialInout(queryParamVO);
        ExcelUtil<RecordMaterialInoutVo> util = new ExcelUtil<>(RecordMaterialInoutVo.class);
        return util.exportExcel(list, "出入库记录");
    }

    /**
     * 物料库存预警信息导出
     */
    @Log(title = "物料预警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/materialAlertInfoExport")
    public AjaxResult materialAlertInfoExport(@RequestBody QueryParamVO queryParamVO) {
        List<BasicMaterialAlertInfo> list = basicMaterialBatchInventoryService.queryMaterialAlertInfo(queryParamVO);
        ExcelUtil<BasicMaterialAlertInfo> util = new ExcelUtil<>(BasicMaterialAlertInfo.class);
        return util.exportExcel(list, "物料库存预警信息");
    }

    /**
     * 物料库存数量信息导出
     */
    @Log(title = "物料数量信息", businessType = BusinessType.EXPORT)
    @PostMapping("/materialNumDateExport")
    public AjaxResult materialNumDateExport(@RequestBody QueryParamVO queryParamVO) {
        List<BasicMaterialNumInfo> list = basicMaterialBatchInventoryService.queryMaterialNumDate(queryParamVO);
        ExcelUtil<BasicMaterialNumInfo> util = new ExcelUtil<>(BasicMaterialNumInfo.class);
        return util.exportExcel(list, "物料数量信息");
    }

    /**
     * 物料过期预警信息导出
     */
    @Log(title = "物料过期预警", businessType = BusinessType.EXPORT)
    @PostMapping("/materialExpirationAlertExport")
    public AjaxResult materialExpirationAlertExport(@RequestBody QueryParamVO queryParamVO) {
        List<MaterialAlertInfo> list = basicMaterialBatchInventoryService.queryMaterialExpirationAlert(queryParamVO);
        ExcelUtil<MaterialAlertInfo> util = new ExcelUtil<>(MaterialAlertInfo.class);
        return util.exportExcel(list, "物料过期预警信息");
    }


    /**
     * 物料调拨单导出（pdf）
     *
     */
    @PostMapping("/exportAllotInfoPdf")
    public void exportAllotInfoPdf(HttpServletResponse response, @RequestBody JSONObject idInJson) {
        String bound_index = idInJson.getString("bound_index");
        QueryParamVO query = new QueryParamVO();
        query.setKeyWord(bound_index);
        List<RecordAllotInfo> recordAllotInfos = recordAllotInfoService.queryRecordAllotInfo(query);
        RecordAllotInfo inout = recordAllotInfos.get(0);
        Integer state = inout.getState();
        Integer rule = inout.getTransferRule();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("BOUND_INDEX", inout.getBoundIndex());
        parameters.put("TOTAL_NUM", inout.getTotalNum());
        parameters.put("STATE", state == 0 ? "未送审" : state == 1 ? "审核中" : "锁单");
        parameters.put("REMARK", inout.getRemark());
        parameters.put("RECORDER", inout.getRecorder());
        parameters.put("REASON", inout.getReasons());
        parameters.put("RULES", rule == 0 ? "同物料调拨" : "不同物料调拨");
        parameters.put("LOCK_DATE", Optional.ofNullable(inout.getLockDate()).map(date -> DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date)).orElse(""));
        parameters.put("LOGO",Optional.ofNullable(ProjectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.LOGO_PATH)).orElse("/home/<USER>/control_system/speedbot.png"));

        List<MaterialDetailExcelData> pdfList = new ArrayList<>();
        QueryParamVO queryParamVO = new QueryParamVO();
        queryParamVO.setKeyWord(bound_index);
        List<RecordAllotDetailVo> list = recordAllotDetailService.queryRecordAllotInfoDetail(queryParamVO);
        for (RecordAllotDetailVo recordAllotDetailVo : list) {
            MaterialDetailExcelData param = new MaterialDetailExcelData();
            BeanUtils.copyProperties(recordAllotDetailVo, param);
            param.setDestProduceDate(DateUtils.parseDateToStr("yyyy-MM-dd", recordAllotDetailVo.getDestProduceDate()));
            param.setOrignProduceDate(DateUtils.parseDateToStr("yyyy-MM-dd", recordAllotDetailVo.getOrignProduceDate()));
            pdfList.add(param);
        }

        String filename = bound_index + ".pdf";
        exportPDF(response, "jasper/AllotInfo.jasper", parameters, pdfList, filename);
    }

    /**
     * Jasper导出PDF
     */
    private void exportPDF(HttpServletResponse response, String jasperPath, Map<String, Object> parameters, List<MaterialDetailExcelData> pdfList, String filename) {
        InputStream resourceAsStream = null;
        File jasperFile = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource(jasperPath);
            resourceAsStream = classPathResource.getInputStream();
            if (resourceAsStream == null) {
                jasperFile = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + jasperPath);
            }
        } catch (IOException exception) {
            logger.error(exception.getMessage());
        }
        JasperPrint jasperPrint = null;
        try {
            JasperReport jasperReport;
            if (resourceAsStream == null) {
                jasperReport = (JasperReport) JRLoader.loadObject(jasperFile);
            } else {
                jasperReport = (JasperReport) JRLoader.loadObject(resourceAsStream);
            }
            jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, new TaskReportBBSource(pdfList));
        } catch (JRException e) {
            e.printStackTrace();
        }
        ReportUtil.reportBasicsPdf(jasperPrint, response, filename);
    }


}