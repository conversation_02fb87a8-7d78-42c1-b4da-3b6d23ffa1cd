package com.ruoyi.controller.basicData;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.basicData.BasicWarehouseContainer;
import com.ruoyi.domain.basicData.BasicWarehouseInfo;
import com.ruoyi.domain.basicData.BasicWarehouseLocation;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.basicData.BasicWarehouseContainerService;
import com.ruoyi.service.basicData.BasicWarehouseInfoService;
import com.ruoyi.service.basicData.BasicWarehouseLocationService;
import com.ruoyi.service.document.RecordInoutDetailService;
import com.ruoyi.service.work.RecordMaterialInoutService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.vo.document.RecordInoutDetailVo;
import com.ruoyi.vo.mes.MaterialNumRequest;
import com.ruoyi.vo.report.RecordMaterialInoutVo;
import com.ruoyi.vo.warehouse.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对接前端数据
 */
@RestController
@RequestMapping("/speedbot/wms/web")
public class WebDateController extends BaseController {
    @Resource
    private BasicWarehouseInfoService basicWarehouseInfoService;
    @Resource
    private BasicWarehouseLocationService basicWarehouseLocationService;
    @Resource
    private BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    private BasicWarehouseContainerService basicWarehouseContainerService;
    @Resource
    RecordMaterialInoutService recordMaterialInoutService;
    @Resource
    RecordInoutDetailService recordInoutDetailService;


    /**
     * 新增仓库信息
     */
    @Log(title = "仓库信息", businessType = BusinessType.INSERT)
    @PostMapping("/addBasicWarehouse")
    public ResponseResult addBasicWarehouse(@RequestBody BasicWarehouseDto basicWarehouseInfo) {
        return basicWarehouseInfoService.addBasicWarehouse(basicWarehouseInfo);
    }

    /**
     * 更新仓库信息
     */
    @PostMapping("/updateBasicWarehouse")
    @Log(title = "仓库信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateBasicWarehouse(@RequestBody BasicWarehouseInfo basicWarehouseInfo) {
        return basicWarehouseInfoService.updateBasicWarehouse(basicWarehouseInfo);
    }

    /**
     * 删除仓库信息
     */
    @PostMapping("/deleteBasicWarehouse")
    @Log(title = "仓库信息", businessType = BusinessType.DELETE)
    public ResponseResult deleteBasicWarehouse(@RequestBody BasicWarehouseInfo basicWarehouseInfo) {
        return basicWarehouseInfoService.deleteBasicWarehouse(basicWarehouseInfo);
    }

    /**
     * 查询仓库信息
     */
    @PostMapping("/queryBasicWarehouse")
    public TableDataInfo queryBasicWarehouse(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicWarehouseInfo> list = basicWarehouseInfoService.queryBasicWarehouse(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 新增仓库库位信息
     */
    @Log(title = "仓库库位信息", businessType = BusinessType.INSERT)
    @PostMapping("/addWarehouseLocation")
    public ResponseResult addWarehouseLocation(@RequestBody BasicWarehouseLocation basicWarehouseLocation) {
        return basicWarehouseLocationService.addWarehouseLocation(basicWarehouseLocation);
    }

    /**
     * 复制仓库库位信息
     */
    @Log(title = "仓库库位信息", businessType = BusinessType.INSERT)
    @PostMapping("/copyWarehouseLocation")
    public ResponseResult copyWarehouseLocation(@RequestBody BasicWarehouseLocationDto basicWarehouseLocationDto) {
        return basicWarehouseLocationService.copyWarehouseLocation(basicWarehouseLocationDto);
    }

    /**
     * 更新仓库库位信息
     */
    @PostMapping("/updateWarehouseLocation")
    @Log(title = "仓库库位信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateWarehouseLocation(@RequestBody BasicWarehouseLocation basicWarehouseLocation) {
        return basicWarehouseLocationService.updateWarehouseLocation(basicWarehouseLocation);
    }

    /**
     * 删除仓库库位信息
     */
    @PostMapping("/deleteWarehouseLocation")
    @Log(title = "仓库库位信息", businessType = BusinessType.DELETE)
    public ResponseResult deleteWarehouseLocation(@RequestBody List<BasicWarehouseLocation> basicWarehouseLocations) {
        return basicWarehouseLocationService.deleteWarehouseLocation(basicWarehouseLocations);
    }

    /**
     * 查询仓库库位信息
     */
    @PostMapping("/queryWarehouseLocation")
    public TableDataInfo queryWarehouseLocation(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicWarehouseLocation> list = basicWarehouseLocationService.queryWarehouseLocation(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 更改布局
     */
    @PostMapping("/modifyLayout")
    public ResponseResult modifyLayout(@RequestBody List<BasicWarehouseLocation> list) {
        for (BasicWarehouseLocation obj : list) {
            BasicWarehouseLocation data = basicWarehouseLocationService.selectByPrimaryKey(obj.getId());
            data.setTop(obj.getTop());
            data.setLeft(obj.getLeft());
            data.setWidth(obj.getWidth());
            data.setHeight(obj.getHeight());
            this.basicWarehouseLocationService.updateById(data);
        }
        return ResponseResult.getSuccessResult();
    }


    /**
     * 查询物料仓库批次信息
     */
    @PostMapping("/queryBasicMaterialInventory")
    public TableDataInfo queryBasicMaterialInventory(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicMaterialBatchInventoryDto> list = basicMaterialBatchInventoryService.queryBasicMaterialInventory(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询物料仓库信息
     */
    @PostMapping("/queryBasicMaterialWarehouseInfo")
    public TableDataInfo queryBasicMaterialWarehouseInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicMaterialDetailDto> list = basicMaterialBatchInventoryService.queryBasicMaterialWarehouseInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询容器物料信息
     */
    @PostMapping("/queryContainerMaterialInfo")
    public TableDataInfo queryContainerMaterialInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<MaterialContainerInfoDto> list = basicMaterialBatchInventoryService.queryContainerMaterialInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 新增仓库容器信息
     */
    @Log(title = "仓库容器信息", businessType = BusinessType.INSERT)
    @PostMapping("/addWarehouseContainer")
    public ResponseResult addWarehouseContainer(@RequestBody BasicWarehouseContainer basicWarehouseContainer) {
        return basicWarehouseContainerService.addWarehouseContainer(basicWarehouseContainer);
    }

    /**
     * 更新仓库容器信息
     */
    @PostMapping("/updateWarehouseContainer")
    @Log(title = "仓库容器信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateWarehouseContainer(@RequestBody BasicWarehouseContainer basicWarehouseContainer) {
        return basicWarehouseContainerService.updateWarehouseContainer(basicWarehouseContainer);
    }

    /**
     * 删除仓库容器信息
     */
    @PostMapping("/deleteWarehouseContainer")
    @Log(title = "仓库容器信息", businessType = BusinessType.DELETE)
    public ResponseResult deleteWarehouseContainer(@RequestBody BasicWarehouseContainer basicWarehouseContainer) {
        String id = basicWarehouseContainer.getId();
        if (StringUtil.isEmpty(id)) {
            return ResponseResult.getErrorResult("删除主键不能为空!");
        }
        return basicWarehouseContainerService.deleteWarehouseContainer(id);
    }

    /**
     * 查询仓库库位信息
     */
    @PostMapping("/queryWarehouseContainer")
    public TableDataInfo queryWarehouseContainer(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicWarehouseContainer> list = basicWarehouseContainerService.queryWarehouseContainer(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询物料出入库记录
     */
    @PostMapping("/queryRecordMaterialInout")
    public TableDataInfo queryRecordMaterialInout(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordMaterialInoutVo> list = recordMaterialInoutService.queryRecordMaterialInout(queryParamVO);
        return this.getDataTable(list);
    }


    /**
     * 查询容器位置信息
     */
    @PostMapping("/queryContainerLocationInfo")
    public ResponseResult queryContainerLocationInfo(@RequestBody QueryParamVO queryParamVO) {
        return basicWarehouseContainerService.queryContainerLocationInfo(queryParamVO);
    }

    /**
     * 查询物料预警信息
     */
    @PostMapping("/queryMaterialAlertInfo")
    public TableDataInfo queryMaterialAlertInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicMaterialAlertInfo> list = basicMaterialBatchInventoryService.queryMaterialAlertInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询物过期预警信息
     */
    @PostMapping("/queryMaterialExpirationAlert")
    public TableDataInfo queryMaterialExpirationAlert(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<MaterialAlertInfo> list = basicMaterialBatchInventoryService.queryMaterialExpirationAlert(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询物料数量报表信息
     */
    @PostMapping("/queryMaterialNumDate")
    public TableDataInfo queryMaterialNumDate(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicMaterialNumInfo> list = basicMaterialBatchInventoryService.queryMaterialNumDate(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询出入库详情
     */
    @PostMapping("/queryRecordInoutDetail")
    public TableDataInfo queryRecordInoutDetail(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordInoutDetailVo> list = recordInoutDetailService.queryRecordInoutDetail(queryParamVO);
        return this.getDataTable(list);
    }

}
