package com.ruoyi.controller.basicData;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.basicData.*;
import com.ruoyi.service.basicData.*;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.basicData.BasicMaterialClassifyWithTypeNameVo;
import com.ruoyi.vo.basicData.BomCustomerDto;
import com.ruoyi.vo.basicData.BomInfoAddReq;
import com.ruoyi.vo.basicData.BomInfoDto;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webRequest.DeleteReq;
import com.ruoyi.vo.webResponse.dto.BasicMaterialInfoDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 基础数据—物料建模
 */
@RestController
@RequestMapping("/speedbot/basicData/materielInfo")
public class MaterialInfoController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(MaterialInfoController.class);

    @Resource
    private BasicMaterialInfoService materialInfoService;

    @Resource
    private BasicMaterialClassifyService basicMaterialClassifyService;

    @Resource
    private BasicBomCustomerService bomCustomerService;

    @Resource
    private BasicBomVersionService bomVersionService;

    @Resource
    private BasicBomInfoService basicBomInfoService;

    @Resource
    private BasicUnitInfoService basicUnitInfoService;

    @Resource
    private MaterialClassifyTypeMappingService materialClassifyTypeMappingService;
    /**
     * 查询物料信息
     */
    @PostMapping("/queryBasicMaterialInfo")
    public TableDataInfo queryBasicMaterialInfo(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicMaterialInfoDto> list = this.materialInfoService.queryBasicMaterialInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询物料分类（包含类型中文名称）
     */
    @PostMapping("/queryBasicMaterialClassify")
    public TableDataInfo queryBasicMaterialClassify(@RequestBody QueryParamVO queryParamVO){
        List<BasicMaterialClassifyWithTypeNameVo> list = this.basicMaterialClassifyService.queryBasicMaterialClassify(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "物料分类", businessType = BusinessType.INSERT)
    @PostMapping("/insertBasicMaterialClassify")
    public ResponseResult insertBasicMaterialClassify(@RequestBody BasicMaterialClassify req){
        return this.basicMaterialClassifyService.insertBasicMaterialClassify(req);
    }

    @Log(title = "物料分类", businessType = BusinessType.UPDATE)
    @PostMapping("/uptBasicMaterialClassify")
    public ResponseResult uptBasicMaterialClassify(@RequestBody BasicMaterialClassify req){
        return this.basicMaterialClassifyService.uptBasicMaterialClassify(req);
    }

    @Log(title = "物料分类", businessType = BusinessType.DELETE)
    @PostMapping("/deleteBasicMaterialClassify")
    public ResponseResult deleteBasicMaterialClassify(@RequestBody BatchIdsReq req){
        return this.basicMaterialClassifyService.deleteBasicMaterialClassify(req);
    }

    /**
     * 查询物料所关联的客户信息
     */
    @PostMapping("/queryMaterialBomCustomer")
    public TableDataInfo queryMaterialBomCustomer(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BomCustomerDto> list = this.bomCustomerService.queryBasicBomCustomer(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "物料信息", businessType = BusinessType.UPDATE)
    @PostMapping("/uptBasicMaterialInfo")
    public ResponseResult uptBasicMaterialInfo(@RequestBody BasicMaterialInfo basicMaterialInfo){
        return this.materialInfoService.uptBasicMaterialInfo(basicMaterialInfo);
    }

    @Log(title = "物料信息", businessType = BusinessType.UPDATE)
    @PostMapping("/lockBasicMaterialInfo")
    public ResponseResult lockBasicMaterialInfo(@RequestBody BatchIdsReq batchIdsReq){
        return this.materialInfoService.lockBasicMaterialInfo(batchIdsReq);
    }

    //============================物料BOM版本===================================================
    @PostMapping("/insertBasicBomVersion")
    public ResponseResult insertBasicBomVersion(@RequestBody BasicBomVersion req){
        return this.bomVersionService.insertBasicBomVersion(req);
    }

    /**
     * 查询物料所关联的BOM版本
     */
    @PostMapping("/queryMaterialBomVersion")
    public TableDataInfo queryMaterialBomVersion(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicBomVersion> list = this.bomVersionService.queryBasicBomVersion(queryParamVO);
        return this.getDataTable(list);
    }

    @PostMapping("/uptBasicBomVersion")
    public ResponseResult uptBasicBomVersion(@RequestBody BasicBomVersion req){
        return this.bomVersionService.uptBasicBomVersion(req);
    }

    @PostMapping("/delBasicBomVersion")
    public ResponseResult delBasicBomVersion(@RequestBody DeleteReq req){
        return this.bomVersionService.deleteBasicBomVersion(req);
    }

    @PostMapping("/delBasicBomCustomer")
    public ResponseResult delBasicBomCustomer(@RequestBody DeleteReq req){
        return this.bomCustomerService.deleteBasicBomCustomer(req);
    }

    //***********************************************半成品BOM**********************************************
    /**
     * 查询物料所关联的BOM数据
     */
    @PostMapping("/queryMaterialBom")
    public TableDataInfo queryMaterialBom(@RequestBody QueryParamVO queryParamVO){
        List<BomInfoDto> list = this.basicBomInfoService.queryBasicBom(CommonConstant.MaterialSort.SEMI,queryParamVO);
        return this.getDataTable(list);
    }

    @PostMapping("/insertBasicMaterialBom")
    public ResponseResult insertBasicMaterialBom(@RequestBody BomInfoAddReq req){
        return this.basicBomInfoService.insertBasicBom(req);
    }

    @PostMapping("/uptBasicMaterialBom")
    public ResponseResult uptBasicMaterialBom(@RequestBody BasicBomInfo basicBomInfo){
        return this.basicBomInfoService.uptBasicBom(basicBomInfo);
    }

    @PostMapping("/deleteBasicMaterialBom")
    public ResponseResult deleteBasicMaterialBom(@RequestBody BatchIdsReq req){
        return this.basicBomInfoService.deleteBasicBom(req);
    }

    /**
     * 查询物料和其所关联的BOM数据
     */
    @PostMapping("/queryWholeMaterialBom")
    public TableDataInfo queryWholeMaterialBom(@RequestBody QueryParamVO queryParamVO){
        List<BomInfoDto> list = this.basicBomInfoService.queryWholeMaterialBom(queryParamVO);
        return this.getDataTable(list);
    }

    //***********************************************计量单位管理**********************************************

    /**
     * 计量单位分页查询
     *
     * 查询参数说明：
     * - keyWord: 关键字查询，支持单位编码、单位名称模糊查询
     * - bdate: 创建开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * - edate: 创建结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * - pageNum: 页码
     * - pageSize: 每页大小
     *
     * @param queryParamVO 查询参数
     * @return {@link List<BasicUnitInfo>}
     */
    @PostMapping("/queryBasicUnitInfo")
    public TableDataInfo queryBasicUnitInfo(@RequestBody QueryParamVO queryParamVO) {
        try {
            PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
            List<BasicUnitInfo> list = basicUnitInfoService.queryBasicUnitInfo(queryParamVO);
            TableDataInfo result = getDataTable(list);
            return result;
        } catch (Exception e) {
            logger.error("计量单位分页查询失败", e);
            throw e;
        }
    }

//***********************************************物料分类类型映射**********************************************
    /**
     * 分页查询物料分类类型映射
     */
    @PostMapping("/queryMappingList")
    public TableDataInfo queryMappingList(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        return this.getDataTable(this.materialClassifyTypeMappingService.queryMappingList(queryParamVO));
    }

    /**
     * 获取物料分类类型映射关系（用于下拉选择等）
     */
    @GetMapping("/getMaterialTypeMapping")
    public ResponseResult getMaterialTypeMapping() {
        Map<String, String> mapping = this.materialClassifyTypeMappingService.getMaterialTypeMapping();
        return ResponseResult.getSuccessResult("获取成功", mapping);
    }

    /**
     * 新增物料分类类型映射
     */
    @Log(title = "物料分类类型映射", businessType = BusinessType.INSERT)
    @PostMapping("/insertMapping")
    public ResponseResult insertMapping(@RequestBody MaterialClassifyTypeMapping mapping) {
        return this.materialClassifyTypeMappingService.insertMapping(mapping);
    }

    /**
     * 更新物料分类类型映射
     */
    @Log(title = "物料分类类型映射", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMapping")
    public ResponseResult updateMapping(@RequestBody MaterialClassifyTypeMapping mapping) {
        return this.materialClassifyTypeMappingService.updateMapping(mapping);
    }

    /**
     * 删除物料分类类型映射
     */
    @Log(title = "物料分类类型映射", businessType = BusinessType.DELETE)
    @PostMapping("/deleteMapping")
    public ResponseResult deleteMapping(@RequestBody BatchIdsReq req) {
        return this.materialClassifyTypeMappingService.deleteMapping(req);
    }

    /**
     * 根据ID获取单个映射配置
     */
    @GetMapping("/getMappingById/{id}")
    public ResponseResult getMappingById(@PathVariable String id) {
        MaterialClassifyTypeMapping mapping = this.materialClassifyTypeMappingService.getById(id);
        if (mapping != null) {
            return ResponseResult.getSuccessResult("获取成功", mapping);
        }
        return ResponseResult.getErrorResult("未找到对应的配置");
    }
}
