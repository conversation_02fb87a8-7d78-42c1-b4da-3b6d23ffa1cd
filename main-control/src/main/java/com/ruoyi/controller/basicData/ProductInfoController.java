package com.ruoyi.controller.basicData;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.basicData.BasicBomInfo;
import com.ruoyi.domain.basicData.BasicProductInfo;
import com.ruoyi.service.basicData.*;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.basicData.BomInfoAddReq;
import com.ruoyi.vo.basicData.BomInfoDto;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webResponse.dto.BasicProductInfoDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 基础数据—产品信息
 */
@RestController
@RequestMapping("/speedbot/basicData/productInfo")
public class ProductInfoController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(ProductInfoController.class);

    @Resource
    private BasicProductInfoService productInfoService;

    @Resource
    private BasicBomVersionService bomVersionService;

    @Resource
    private BasicBomInfoService basicBomInfoService;

    /**
     * 查询产品信息
     */
    @PostMapping("/queryBasicProductInfo")
    public TableDataInfo queryBasicProductInfo(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicProductInfoDto> list = this.productInfoService.queryBasicProductInfo(queryParamVO);
        return this.getDataTable(list);
    }

    @Log(title = "产品信息", businessType = BusinessType.UPDATE)
    @PostMapping("/uptBasicProductInfo")
    public ResponseResult uptBasicProductInfo(@RequestBody BasicProductInfo basicProductInfo){
        return this.productInfoService.uptBasicProductInfo(basicProductInfo);
    }

    //***********************************************半成品BOM**********************************************
    /**
     * 查询产品所关联的BOM数据
     */
    @PostMapping("/queryProductBom")
    public TableDataInfo queryProductBom(@RequestBody QueryParamVO queryParamVO){
        List<BomInfoDto> list = this.basicBomInfoService.queryBasicBom(CommonConstant.MaterialSort.PRODUCT,queryParamVO);
        return this.getDataTable(list);
    }

    @PostMapping("/insertProductBom")
    public ResponseResult insertProductBom(@RequestBody BomInfoAddReq req){
        return this.basicBomInfoService.insertBasicBom(req);
    }

    @PostMapping("/uptProductBom")
    public ResponseResult uptProductBom(@RequestBody BasicBomInfo basicBomInfo){
        return this.basicBomInfoService.uptBasicBom(basicBomInfo);
    }

    @PostMapping("/deleteProductBom")
    public ResponseResult deleteProductBom(@RequestBody BatchIdsReq req){
        return this.basicBomInfoService.deleteBasicBom(req);
    }
}
