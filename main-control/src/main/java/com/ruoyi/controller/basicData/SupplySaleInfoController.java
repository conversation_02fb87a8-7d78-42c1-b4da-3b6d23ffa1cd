package com.ruoyi.controller.basicData;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.domain.basicData.BasicCompanyInfo;
import com.ruoyi.domain.basicData.BasicContactInfo;
import com.ruoyi.domain.basicData.BasicUnitInfo;
import com.ruoyi.service.basicData.BasicCompanyInfoService;
import com.ruoyi.service.basicData.BasicContactInfoService;
import com.ruoyi.utils.QueryParamVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 基础数据—供销建模
 */
@RestController
@RequestMapping("/speedbot/basicData/supplySale")
public class SupplySaleInfoController extends BaseController {

    protected final Logger logger = LoggerFactory.getLogger(SupplySaleInfoController.class);

    @Resource
    private BasicCompanyInfoService basicCompanyInfoService;

    @Resource
    private BasicContactInfoService basicContactInfoService;

    /**
     * 查询客户信息
     * @return {@link List<BasicCompanyInfo>}
     */
    @PostMapping("/querySupplySaleInfo")
    public TableDataInfo queryCustomerInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicCompanyInfo> list = this.basicCompanyInfoService.queryBasicCustomerInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 查询联系人信息
     * @return {@link List<BasicContactInfo>}
     */
    @PostMapping("/queryContactInfo")
    public TableDataInfo queryContactInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicContactInfo> list = this.basicContactInfoService.queryBasicContactInfo(queryParamVO);
        return this.getDataTable(list);
    }
}
