package com.ruoyi.controller.test;

import com.ruoyi.service.basicData.*;
import com.ruoyi.service.bill.*;
import com.ruoyi.service.erp.ErpService;
import com.ruoyi.utils.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/speedbot/test")
public class TestController {
    protected final Logger logger = LoggerFactory.getLogger(TestController.class);

    @Autowired
    private ErpService erpService;

    @Autowired
    private BasicMaterialInfoService basicMaterialInfoService;

    @Resource
    private BasicMaterialClassifyService basicMaterialClassifyService;

    @Resource
    private BasicCompanyInfoService basicCompanyInfoService;

    @Resource
    private BasicCompanyGroupService basicCompanyGroupService;

    @Resource
    private BasicContactInfoService basicContactInfoService;

    @Resource
    private BasicUnitInfoService basicUnitInfoService;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private DeliveryNoticeService deliveryNoticeService;

    @Resource
    private ProductionPickingMaterialService productionPickingMaterialService;

    @Resource
    private ProductionFeedMaterialService productionFeedMaterialService;

    @Resource
    private ProductionInStockService productionInStockService;

    @Resource
    private SimpleProductionPickingMaterialService simpleProductionPickingMaterialService;

    @Resource
    private SimpleProductionInStockService simpleProductionInStockService;

    @Resource
    private ReturnMaterialService returnMaterialService;
    @Resource
    private ReceiveNoticeService receiveNoticeService;

    @Resource
    private ReturnNoticeService returnNoticeService;

    /**
     * 从ERP同步物料信息
     */
    @PostMapping("/syncErpMaterials")
    public ResponseResult syncErpMaterials() throws Exception {
        logger.info("开始从ERP同步物料信息...");
        ResponseResult result = basicMaterialInfoService.syncFromErp();
        logger.info("物料信息同步完成。");
        return result;
    }

    /**
     * 从ERP同步物料分类信息
     * @return 同步结果
     */
    @PostMapping("/syncErpMaterialGroups")
    public ResponseResult syncErpMaterialGroups() throws Exception {
        logger.info("开始同步ERP物料分类数据...");
        ResponseResult result = basicMaterialClassifyService.syncFromErpGroups();
        logger.info("同步ERP物料分类数据结束。");
        return result;
    }

    /**
     * 从ERP同步客户信息
     * @return 同步结果
     */
    @PostMapping("/syncErpCustomers")
    public ResponseResult syncErpCustomers() throws Exception {
        return basicCompanyInfoService.syncFromErpCustomers();
    }

    /**
     * 从ERP同步供应商信息
     * @return 同步结果
     */
    @PostMapping("/syncErpSuppliers")
    public ResponseResult syncErpSuppliers() throws Exception {
        return basicCompanyInfoService.syncFromErpSuppliers();
    }

    /**
     * 从ERP同步客户分组信息
     * @return 同步结果
     */
    @PostMapping("/syncErpCustomerGroups")
    public ResponseResult syncErpCustomerGroups() throws Exception {
        return basicCompanyGroupService.syncFromErpCustomerGroups();
    }

    /**
     * 从ERP同步供应商分组信息
     * @return 同步结果
     */
    @PostMapping("/syncErpSupplierGroups")
    public ResponseResult syncErpSupplierGroups() throws Exception {
        return basicCompanyGroupService.syncFromErpSupplierGroups();
    }

    /**
     * 从ERP同步联系人信息
     * @return 同步结果
     */
    @PostMapping("/syncErpContacts")
    public ResponseResult syncErpContacts() throws Exception {
        logger.info("开始同步ERP联系人数据...");
        ResponseResult result = basicContactInfoService.syncFromErp();
        logger.info("同步ERP联系人数据结束。");
        return result;
    }

    /**
     * 从ERP同步计量单位信息
     * @return 同步结果
     */
    @PostMapping("/syncErpUnits")
    public ResponseResult syncErpUnits() throws Exception {
        logger.info("开始同步ERP计量单位数据...");
        ResponseResult result = basicUnitInfoService.syncFromErp();
        logger.info("同步ERP计量单位数据结束。");
        return result;
    }

    /**
     * 从ERP同步采购订单信息
     * @return 同步结果
     */
    @PostMapping("/syncErpPurchaseOrders")
    public ResponseResult syncErpPurchaseOrders() throws Exception {
        logger.info("开始同步ERP采购订单数据...");
        ResponseResult result = purchaseOrderService.syncFromErp();
        logger.info("同步ERP采购订单数据结束。");
        return result;
    }
      /**
     * 从ERP同步发货通知单
     */
    @PostMapping("/syncErpDeliveryNotices")
    public ResponseResult syncFromErp() {
        try {
            return deliveryNoticeService.syncFromErp();
        } catch (Exception e) {
            logger.error("从ERP同步发货通知单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步生产领料单
     */
    @PostMapping("/syncErpProductionPickingMaterials")
    public ResponseResult syncErpProductionPickingMaterials() {
        try {
            logger.info("开始同步ERP生产领料单数据...");
            ResponseResult result = productionPickingMaterialService.syncFromErp();
            logger.info("同步ERP生产领料单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步生产领料单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步生产补料单
     */
    @PostMapping("/syncErpProductionFeedMaterials")
    public ResponseResult syncErpProductionFeedMaterials() {
        try {
            logger.info("开始同步ERP生产补料单数据...");
            ResponseResult result = productionFeedMaterialService.syncFromErp();
            logger.info("同步ERP生产补料单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步生产补料单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步生产入库单
     */
    @PostMapping("/syncErpProductionInStocks")
    public ResponseResult syncErpProductionInStocks() {
        try {
            logger.info("开始同步ERP生产入库单数据...");
            ResponseResult result = productionInStockService.syncFromErp();
            logger.info("同步ERP生产入库单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步生产入库单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步简单生产领料单
     */
    @PostMapping("/syncErpSimpleProductionPickingMaterials")
    public ResponseResult syncErpSimpleProductionPickingMaterials() {
        try {
            logger.info("开始同步ERP简单生产领料单数据...");
            ResponseResult result = simpleProductionPickingMaterialService.syncFromErp();
            logger.info("同步ERP简单生产领料单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步简单生产领料单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 从ERP同步简单生产入库单
     */
    @PostMapping("/syncErpSimpleProductionInStocks")
    public ResponseResult syncErpSimpleProductionInStocks() {
        try {
            logger.info("开始同步ERP简单生产入库单数据...");
            ResponseResult result = simpleProductionInStockService.syncFromErp();
            logger.info("同步ERP简单生产入库单数据结束。");
            return result;
        } catch (Exception e) {
            logger.error("从ERP同步简单生产入库单失败", e);
            return ResponseResult.getErrorResult("同步失败：" + e.getMessage());
        }
    }

    /**
     * 测试采购订单下推采购入库单
     */
    @PostMapping("/testPushPurchaseOrderToInStock")
    public ResponseResult testPushPurchaseOrderToInStock() {
        logger.info("开始测试单个采购订单下推采购入库单...");
        // 使用测试数据：101CGDD003027
        String orderNumber = "101CGDD003027";
        boolean result = erpService.pushSinglePurchaseOrderToInStock(orderNumber);

        if (result) {
            logger.info("单个采购订单下推采购入库单测试成功");
            return ResponseResult.getSuccessResult();
        } else {
            logger.error("单个采购订单下推采购入库单测试失败");
            return ResponseResult.getErrorResult("单个下推失败");
        }
    }



    /**
     * 测试ERP同步功能
     */
    @PostMapping("/testReceiveNoticeSync")
    public ResponseResult testReceiveNoticeSync() {
        try {
            logger.info("开始测试收料通知单ERP同步功能");
            ResponseResult result = receiveNoticeService.syncFromErp();
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试收料通知单同步失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }
    /**
     * 测试ERP同步功能
     */
    @PostMapping("/testReturnMaterialSync")
    public ResponseResult testReturnMaterialSync() {
        try {
            logger.info("开始测试退料申请单ERP同步功能");
            ResponseResult result = returnMaterialService.syncFromErp();
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试退料申请单同步失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试退货通知单ERP同步功能
     */
    @PostMapping("/testReturnNoticeSync")
    public ResponseResult testReturnNoticeSync() {
        try {
            logger.info("开始测试退货通知单ERP同步功能");
            ResponseResult result = returnNoticeService.syncFromErp();
            logger.info("测试完成，结果：{}", result.getMsg());
            return result;
        } catch (Exception e) {
            logger.error("测试退货通知单同步失败", e);
            return ResponseResult.getErrorResult("测试失败: " + e.getMessage());
        }
    }


}


