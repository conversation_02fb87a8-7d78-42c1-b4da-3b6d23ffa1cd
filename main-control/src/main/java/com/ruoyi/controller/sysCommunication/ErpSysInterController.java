package com.ruoyi.controller.sysCommunication;

import com.ruoyi.common.core.controller.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: [ERP系统接口控制交互]
 * @date 2024/9/18 17:55
 */
@RestController
@RequestMapping("/speedbot/sysCommunication/erySys")
public class ErpSysInterController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(ErpSysInterController.class);
}
