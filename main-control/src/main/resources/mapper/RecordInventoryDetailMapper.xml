<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.document.RecordInventoryDetailMapper">
  <update id="uptUnLockInventoryDetail">
    UPDATE record_inventory_detail a
    JOIN record_inventory_info b ON a.bound_index = b.bound_index
    SET
    a.inventory_num = CASE WHEN #{inventoryNum} IS NOT NULL AND #{inventoryNum} != '' THEN #{inventoryNum} ELSE a.inventory_num END,
    a.remark = CASE WHEN #{remark} IS NOT NULL AND #{remark} != '' THEN #{remark} ELSE a.remark END,
    a.record_date = CASE WHEN #{recordDate} IS NOT NULL THEN #{recordDate} ELSE a.record_date END
    WHERE a.id = #{id}
    AND b.state != 2
  </update>
  <delete id="deleteRecordInventoryDetail">
    DELETE FROM record_inventory_detail
    WHERE bound_index = #{bound_index}
  </delete>
  <select id="queryInventoryRecordDetail" resultType="com.ruoyi.vo.warehouse.RecordInventoryDetailVo">
    select
    rid.*,
    bmi.material_name,
    bmi.material_img,
    bwi.warehouse_name AS warehouseName,
    b.node_name as shelfName,
    a.node_name as levelName,
    bwl.node_name as positionName
    from
    record_inventory_detail rid
    join basic_material_info bmi on rid.material_code = bmi.material_code
    left join basic_warehouse_container bwc on rid.container_code = bwc.container_code
    left join basic_warehouse_location bwl on bwc.location_code = bwl.location_code
    left join basic_warehouse_location a on a.id = bwl.parent_id
    left join basic_warehouse_location b on b.id = a.parent_id
    left join basic_warehouse_info bwi on bwl.warehouse_code = bwi.warehouse_code
    where
    1 = 1
    <if test = "keyWord != null and keyWord != ''">
      and rid.bound_index = #{keyWord}
    </if>
  </select>
  <select id="qryInventoryDetailByCode" resultType="com.ruoyi.domain.document.RecordInventoryDetail">
    select *
    from record_inventory_detail
    where 1 = 1
      and bound_index = #{bound_index}
  </select>
</mapper>