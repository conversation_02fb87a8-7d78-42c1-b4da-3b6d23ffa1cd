<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.erp.ErpReportMainMapper">

    <resultMap type="com.ruoyi.domain.erp.ErpReportMain" id="ErpReportMainResult">
        <result property="id" column="id"/>
        <result property="reportNo" column="report_no"/>
        <result property="documentCode" column="document_code"/>
        <result property="businessType" column="business_type"/>
        <result property="transactionType" column="transaction_type"/>
        <result property="reportStatus" column="report_status"/>
        <result property="erpBillNo" column="erp_bill_no"/>
        <result property="totalQuantity" column="total_quantity"/>
        <result property="createTime" column="create_time"/>
        <result property="reportTime" column="report_time"/>
        <result property="reporter" column="reporter"/>
    </resultMap>

    <sql id="selectErpReportMainVo">
        select id, report_no, document_code, business_type, transaction_type,
               report_status, erp_bill_no, total_quantity, create_time,
               report_time, reporter
        from erp_report_main
    </sql>

    <select id="selectErpReportMainList" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="ErpReportMainResult">
        <include refid="selectErpReportMainVo"/>
        <where>
            <if test="keyWord != null and keyWord != ''">
                and (document_code like concat('%', #{keyWord}, '%') 
                     or report_no like concat('%', #{keyWord}, '%')
                     or erp_bill_no like concat('%', #{keyWord}, '%'))
            </if>
            <if test="keySubWord != null and keySubWord != ''">
                and business_type = #{keySubWord}
            </if>
            <if test="keyThirdWord != null and keyThirdWord != ''">
                and report_status = #{keyThirdWord}
            </if>
            <if test="bdate != null and bdate != ''">
                and create_time >= #{bdate}
            </if>
            <if test="edate != null and edate != ''">
                and create_time &lt;= #{edate}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectByDocumentCode" parameterType="String" resultMap="ErpReportMainResult">
        <include refid="selectErpReportMainVo"/>
        where document_code = #{documentCode}
        order by create_time desc
    </select>

    <select id="selectByReportStatus" parameterType="Integer" resultMap="ErpReportMainResult">
        <include refid="selectErpReportMainVo"/>
        where report_status = #{reportStatus}
        order by create_time asc
    </select>

    <select id="selectByBusinessType" parameterType="Integer" resultMap="ErpReportMainResult">
        <include refid="selectErpReportMainVo"/>
        where business_type = #{businessType}
        order by create_time desc
    </select>

    <update id="updateReportStatus">
        update erp_report_main
        set report_status = #{reportStatus},
            report_time = now(),
            <if test="erpBillNo != null and erpBillNo != ''">
                erp_bill_no = #{erpBillNo}
            </if>
        where id = #{id}
    </update>

</mapper>
