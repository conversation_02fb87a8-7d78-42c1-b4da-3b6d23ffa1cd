<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.erp.ErpReportDetailMapper">

    <resultMap type="com.ruoyi.domain.erp.ErpReportDetail" id="ErpReportDetailResult">
        <result property="id" column="id"/>
        <result property="mainId" column="main_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="containerCode" column="container_code"/>
        <result property="quantity" column="quantity"/>
        <result property="unit" column="unit"/>
        <result property="batchNo" column="batch_no"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectErpReportDetailVo">
        select id, main_id, material_code, material_name, container_code,
               quantity, unit, batch_no, create_time
        from erp_report_detail
    </sql>

    <select id="selectByMainId" parameterType="String" resultMap="ErpReportDetailResult">
        <include refid="selectErpReportDetailVo"/>
        where main_id = #{mainId}
        order by create_time asc
    </select>

    <delete id="deleteByMainId" parameterType="String">
        delete from erp_report_detail where main_id = #{mainId}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into erp_report_detail
        (id, main_id, material_code, material_name, container_code, quantity, unit, batch_no, create_time)
        values
        <foreach collection="details" item="detail" separator=",">
            (#{detail.id}, #{detail.mainId}, #{detail.materialCode}, #{detail.materialName},
             #{detail.containerCode}, #{detail.quantity}, #{detail.unit}, #{detail.batchNo},
             #{detail.createTime})
        </foreach>
    </insert>

</mapper>
