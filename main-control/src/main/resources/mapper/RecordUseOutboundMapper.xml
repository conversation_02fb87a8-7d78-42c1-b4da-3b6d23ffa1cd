<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.document.RecordUseOutboundMapper">

  <select id="getMaxIndex" resultType="java.lang.String">
    select bound_index from record_use_outbound where 1 = 1 and bound_index like #{headstr} order by bound_index desc limit 1
  </select>

  <update id="updateByPrimaryKey" parameterType="com.ruoyi.domain.document.RecordUseOutbound">
    update record_use_outbound
    <set>
      <if test="boundIndex != null and boundIndex != ''">
        bound_index = #{boundIndex,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="recordDate != null">
        record_date = #{recordDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lockDate != null">
        lock_date = #{lockDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recorder != null and recorder != ''">
        recorder = #{recorder,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>