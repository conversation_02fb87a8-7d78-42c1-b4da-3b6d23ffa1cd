<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.document.RecordInoutDetailMapper">
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.domain.basicData.RecordInoutDetail">
    <!--@mbg.generated-->
    update record_inout_detail
    <set>
      <if test="boundIndex != null and boundIndex != ''">
        bound_index = #{boundIndex,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null and materialCode != ''">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="containerCode != null and containerCode != ''">
        container_code = #{containerCode,jdbcType=VARCHAR},
      </if>
      <if test="materialNum != null">
        material_num = #{materialNum,jdbcType=INTEGER},
      </if>
      <if test="upperIndex != null and upperIndex != ''">
        upper_index = #{upperIndex,jdbcType=VARCHAR},
      </if>
      <if test="batch != null and batch != ''">
        batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="boundType != null">
        bound_type = #{boundType,jdbcType=INTEGER},
      </if>
      <if test="isCompliant != null">
        is_compliant = #{isCompliant,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <delete id="removeByBoundIndex">
        delete from record_inout_detail
        where bound_index=#{bound_index,jdbcType=VARCHAR}
    </delete>


  <select id="getMaxIndex" resultType="java.lang.String">
    select bound_index from record_inout_detail where 1 = 1 and bound_index like #{headstr} order by bound_index desc limit 1
  </select>

  <select id="queryRecordInoutDetail" resultType="com.ruoyi.vo.document.RecordInoutDetailVo">
    SELECT
    b.id,
    b.bound_index,
    b.material_code,
    b.container_code,
    b.material_num,
    b.upper_index,
    b.batch,
    b.produce_date,
    b.remark,
    b.bound_type,
    b.is_compliant,
    s.material_name,
    s.material_img,
    s.specifications,
    w.warehouse_name
    FROM
    record_inout_detail b
    LEFT JOIN
    basic_material_info s ON b.material_code = s.material_code
    LEFT JOIN
    basic_warehouse_container c ON b.container_code = c.container_code
    LEFT JOIN
    basic_warehouse_location l ON c.location_code = l.location_code
    LEFT JOIN
    basic_warehouse_info w ON l.warehouse_code = w.warehouse_code
    WHERE
    1=1
    <if test="keyWord != null and keyWord.trim() != ''">
      AND (b.bound_index = #{keyWord})
    </if>
    <if test="keySubWord != null and keySubWord.trim() != ''">
      AND (b.material_code LIKE CONCAT('%', #{keySubWord}, '%') OR s.material_name LIKE CONCAT('%', #{keySubWord}, '%'))
    </if>
    <if test="keyThirdWord != null and keyThirdWord.trim() != ''">
      AND (w.warehouse_name LIKE CONCAT('%', #{keyThirdWord}, '%'))
    </if>
    ORDER BY
    b.produce_date DESC
  </select>
  <select id="qryOldNumByInoutDetail" resultType="java.lang.Integer">
    SELECT
      COALESCE(SUM(material_num), 0) AS total_material_num
    FROM
      record_inout_detail
    where 1=1
      and upper_index = #{upperIndex}
      and bound_type = #{boundType}
      and material_code = #{materialCode}
  </select>
  <select id="qryByUpperIndex" resultType="com.ruoyi.domain.basicData.RecordInoutDetail">
    select * from record_inout_detail where 1=1 and upper_index = #{upperIndex}
  </select>
  <select id="qryUnLockBoundByUpperIndexAndState" resultType="java.lang.Integer">
    select
      count(*)
    from
      record_inout_detail rid
        join record_purchase_inout rpi on
        rpi.bound_index = rid.bound_index
    where
      1=1
      and rid.upper_index = #{upperIndex}
    <if test="state != null">
      and rpi.state != #{state}
    </if>
  </select>

</mapper>