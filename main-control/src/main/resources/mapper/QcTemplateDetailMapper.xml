<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.qc.QcTemplateDetailMapper">


    <select id="queryQcTemplateDetail" resultType="com.ruoyi.vo.qc.QcTemplateDetailVo">
        select qtd.*,
        qti.item_name,
        qti.qc_tool,
        qti.qc_result_type,
        qti.qc_result_spc
        from qc_template_detail qtd
        left join qc_template_item qti on qtd.item_code = qti.item_code
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND qtd.template_code = #{keyWord}
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND (qti.item_code LIKE CONCAT('%', #{keyWord}, '%') OR qti.item_name LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        order by qti.create_time desc
    </select>
</mapper>

