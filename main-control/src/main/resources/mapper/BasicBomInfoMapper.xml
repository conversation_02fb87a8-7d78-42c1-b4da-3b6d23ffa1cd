<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicBomInfoMapper">

    <select id="queryMaterialBom" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select bom.*,material.material_name,material.material_img,material.material_sort,class.classify_name,
        bom.material_num as material_old_num,material.specifications
        from basic_bom_info bom
        left join basic_material_info material on bom.material_code = material.material_code
        left join basic_material_classify class on class.classify_code = material.classify_code
        where 1=1
        <if test = "keyWord != null and keyWord.trim() != ''">
            and bom.belong_material_code = #{keyWord}
        </if>
        <if test = "keySubWord != null and keySubWord.trim() != ''">
            and (material.material_code = #{keySubWord} or material.material_name like concat('%',(#{keySubWord}),'%'))
        </if>
        <if test = "keyThirdWord != null and keyThirdWord.trim() != ''">
            and bom.version_id = #{keyThirdWord}
        </if>
        <if test = "keyFourWord != null and keyFourWord.trim() != ''">
            and bom.company_id = #{keyFourWord}
        </if>
    </select>

    <select id="queryByAncestorsAndId" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select
            a.*,
            b.material_name,
            b.classify_code,
            b.material_img,
            b.material_sort,
            bmc.classify_name
        from
            basic_bom_info a
                join basic_material_info b on
                a.material_code = b.material_code
                left join basic_material_classify bmc on bmc.classify_code = b.classify_code
        where
            1 = 1
          AND FIND_IN_SET(#{id},a.ancestors) > 0
    </select>

    <select id="queryByAncestors" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select
        a.*,
        b.material_name,
        b.classify_code,
        b.material_img,
        b.material_sort,
        c.classify_name
        from
        basic_bom_info a
        join basic_material_info b on
        a.material_code = b.material_code
        left join basic_material_classify c on c.classify_code = b.classify_code
        where
        1 = 1
        and a.id IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="checkLocationCodeUnique" resultType="com.ruoyi.domain.basicData.BasicBomInfo">
        select * from basic_bom_info where parent_id = #{parent_id} and material_code = #{material_code} limit 1
    </select>
    <select id="getBomListByCode" resultType="com.ruoyi.domain.basicData.BasicBomInfo">
        select *
        from basic_bom_info
        where 1 = 1
          and material_code = #{material_code}
          and parent_id != #{parent_id}
    </select>
    <select id="getBomListByCodeAndNoParent" resultType="com.ruoyi.domain.basicData.BasicBomInfo">
        select *
        from basic_bom_info
        where 1 = 1
          and parent_id = (select b.id
                           from basic_material_bom b
                           where 1 = 1
                             and material_code = #{material_code}
                             and parent_id = '0')
    </select>

    <select id="getRawMaterialBomByMaterialCode" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select
            a.*,
            b.material_name,
            b.classify_code,
            b.material_img,
            b.material_sort,
            c.classify_name
        from
        basic_bom_info a
                join basic_material_info b on
                a.material_code = b.material_code
                left join basic_material_classify c on c.classify_code = b.classify_code
        where
            b.material_sort = '0' and
            a.ancestors  like concat('%',(#{bomId}),'%')
    </select>
    <select id="getAllFProductCodeList" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select
            a.*,
            b.material_name,
            b.classify_code,
            b.material_img,
            b.material_sort,
            b.material_specifications,
            b.material,
            b.gun_it,
            b.route_code,
            c.classify_name
        from
        basic_bom_info a
        join basic_material_info b on
        a.material_code = b.material_code
        left join basic_material_classify c on c.classify_code = b.classify_code
        where
            a.parent_id = '0'
    </select>
    <select id="getBomInfoByFProductCode" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select
            a.*,
            b.material_name,
            b.classify_code,
            b.material_img,
            b.material_sort,
            b.material_specifications,
            b.material,
            b.gun_it,
            b.route_code,
            c.classify_name
        from
        basic_bom_info a
                join basic_material_info b on
                a.material_code = b.material_code
                left join basic_material_classify c on c.classify_code = b.classify_code
        WHERE FIND_IN_SET(
                      #{bomId},
                      ancestors
              )
    </select>

    <select id="getMaterialBomByMaterialSort" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select
            a.*,
            b.material_name,
            b.classify_code,
            b.material_img,
            b.material_sort,
            b.material_specifications,
            b.material,
            b.gun_it,
            b.route_code,
            c.classify_name
        from
        basic_bom_info a
                join basic_material_info b on
                a.material_code = b.material_code
                left join basic_material_classify c on c.classify_code = b.classify_code
        where
            b.material_sort = '1' and
            a.ancestors  like concat('%',(#{bomId}),'%')
    </select>
    <select id="queryProductBom" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select bom.*,material.material_name,classify.classify_name,material.material_img,material.material_sort
        from basic_bom_info bom
        left join basic_product_info prod on bom.belong_material_code = prod.product_code
        left join basic_material_info material on bom.material_code = material.material_code
        left join basic_material_classify classify on classify.classify_code = material.classify_code
        where 1=1
        <if test = "keyWord != null and keyWord.trim() != ''">
            and bom.belong_material_code = #{keyWord}
        </if>
        <if test = "keySubWord != null and keySubWord.trim() != ''">
            and (bom.material_code = #{keySubWord} or bom.material_name like concat('%',(#{keySubWord}),'%'))
        </if>
    </select>

    <select id="queryMaterialBomByParentId" resultType="com.ruoyi.vo.basicData.BomInfoDto">
        select bom.*,material.material_name,material.material_img,material.material_sort,class.classify_name
        from basic_bom_info bom
        left join basic_material_info material on bom.material_code = material.material_code
        left join basic_material_classify class on class.classify_code = material.classify_code
        where bom.parent_id = #{parent_id}
    </select>

    <select id="getLatestStandardVersionSameMaterial" resultType="com.ruoyi.domain.basicData.BasicBomInfo">
        select * from basic_bom_info bmi
        left join basic_bom_version ver on bmi.version_id = ver.id
        where bmi.company_id is null and bmi.material_code = #{material_code}
        order by ver.version desc
    </select>
</mapper>

