<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.ProductionPickingMaterialDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.ProductionPickingMaterialDetailVo" id="ProductionPickingMaterialDetailVoResult">
        <id     property="id"                   column="id"                     />
        <result property="pickingId"            column="picking_id"             />
        <result property="materialId"           column="material_id"            />
        <result property="materialName"         column="material_name"          />
        <result property="specification"        column="specification"          />
        <result property="stockId"              column="stock_id"               />
        <result property="stockLocId"           column="stock_loc_id"           />
        <result property="stockStatusId"        column="stock_status_id"        />
        <result property="moBillNo"             column="mo_bill_no"             />
        <result property="moEntryId"            column="mo_entry_id"            />
        <result property="ppBomEntryId"         column="pp_bom_entry_id"        />
        <result property="appQty"               column="app_qty"                />
        <result property="actualQty"            column="actual_qty"             />
        <result property="entryMemo"            column="entry_memo"             />
        <result property="moId"                 column="mo_id"                  />
        <result property="moEntrySeq"           column="mo_entry_seq"           />
        <result property="unitId"               column="unit_id"                />
        <result property="baseUnitId"           column="base_unit_id"           />
        <result property="stockUnitId"          column="stock_unit_id"          />
        <result property="entryWorkshopId"      column="entry_workshop_id"      />
        <result property="materialCode"         column="material_code"          />
        <result property="unitName"             column="unit_name"              />
        <result property="baseUnitName"         column="base_unit_name"         />
        <result property="stockUnitName"        column="stock_unit_name"        />
        <result property="stockName"            column="stock_name"             />
        <result property="stockLocName"         column="stock_loc_name"         />
        <result property="stockStatusName"      column="stock_status_name"      />
        <result property="workshopName"         column="workshop_name"          />
    </resultMap>

    <select id="queryProductionPickingMaterialDetailVo" resultMap="ProductionPickingMaterialDetailVoResult">
        select
            ppmd.id,
            ppmd.picking_id,
            ppmd.material_id,
            ppmd.material_name,
            ppmd.specification,
            ppmd.stock_id,
            ppmd.stock_loc_id,
            ppmd.stock_status_id,
            ppmd.mo_bill_no,
            ppmd.mo_entry_id,
            ppmd.pp_bom_entry_id,
            ppmd.app_qty,
            ppmd.actual_qty,
            ppmd.entry_memo,
            ppmd.mo_id,
            ppmd.mo_entry_seq,
            ppmd.unit_id,
            ppmd.base_unit_id,
            ppmd.stock_unit_id,
            ppmd.entry_workshop_id,
            ppmd.material_id as material_code,
            bui1.unit_name,
            bui2.unit_name as base_unit_name,
            bui3.unit_name as stock_unit_name,
            ppmd.stock_id as stock_name,
            ppmd.stock_loc_id as stock_loc_name,
            ppmd.stock_status_id as stock_status_name,
            ppmd.entry_workshop_id as workshop_name
        from production_picking_material_detail ppmd
            left join basic_unit_info bui1 on ppmd.unit_id = bui1.id
            left join basic_unit_info bui2 on ppmd.base_unit_id = bui2.id
            left join basic_unit_info bui3 on ppmd.stock_unit_id = bui3.id
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND ppmd.picking_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND ppmd.mo_bill_no like concat('%', #{param.keySubWord}, '%')
            </if>
            <if test="param.keyThirdWord != null and param.keyThirdWord != ''">
                AND (
                    ppmd.material_id like concat('%', #{param.keyThirdWord}, '%') OR
                    ppmd.material_name like concat('%', #{param.keyThirdWord}, '%')
                )
            </if>
        </where>
        ORDER BY ppmd.mo_entry_seq
    </select>
</mapper>
