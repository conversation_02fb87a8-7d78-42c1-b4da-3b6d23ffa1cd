<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.ProductionInStockMapper">

    <resultMap type="com.ruoyi.vo.bill.ProductionInStockVo" id="ProductionInStockVoResult">
        <id     property="id"               column="id"                 />
        <result property="billNo"           column="bill_no"            />
        <result property="description"      column="description"        />
        <result property="inStockDate"      column="in_stock_date"      />
        <result property="createName"       column="create_name"        />
        <result property="createTime"       column="create_time"        />
        <result property="updateName"       column="update_name"        />
        <result property="updateTime"       column="update_time"        />
    </resultMap>

    <select id="queryProductionInStock" resultMap="ProductionInStockVoResult">
        SELECT
        pis.id,
        pis.bill_no,
        pis.description,
        pis.in_stock_date,
        pis.create_name,
        pis.create_time,
        pis.update_name,
        pis.update_time
        FROM production_in_stock pis
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND (pis.bill_no LIKE CONCAT('%', #{param.keyWord}, '%') OR pis.description LIKE CONCAT('%', #{param.keyWord}, '%'))
            </if>

            <if test="param.bdate != null and param.bdate != ''">
                AND  pis.in_stock_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND  pis.in_stock_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY pis.create_time DESC
    </select>
</mapper>
