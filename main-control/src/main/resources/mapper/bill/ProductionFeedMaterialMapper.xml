<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.ProductionFeedMaterialMapper">

    <resultMap type="com.ruoyi.vo.bill.ProductionFeedMaterialVo" id="ProductionFeedMaterialVoResult">
        <id     property="id"               column="id"                 />
        <result property="billNo"           column="bill_no"            />
        <result property="description"      column="description"        />
        <result property="feedDate"         column="feed_date"          />
        <result property="createName"       column="create_name"        />
        <result property="createTime"       column="create_time"        />
        <result property="updateName"       column="update_name"        />
        <result property="updateTime"       column="update_time"        />

    </resultMap>

    <select id="queryProductionFeedMaterial" resultMap="ProductionFeedMaterialVoResult">
        SELECT
        pfm.id,
        pfm.bill_no,
        pfm.description,
        pfm.feed_date,
        pfm.create_name,
        pfm.create_time,
        pfm.update_name,
        pfm.update_time
        FROM production_feed_material pfm
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND (pfm.bill_no LIKE CONCAT('%', #{param.keyWord}, '%') OR pfm.description LIKE CONCAT('%', #{param.keyWord}, '%'))
            </if>

            <if test="param.bdate != null and param.bdate != ''">
                AND  pfm.feed_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND  pfm.feed_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY pfm.create_time DESC
    </select>
</mapper>
