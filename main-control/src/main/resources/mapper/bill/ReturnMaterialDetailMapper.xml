<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.returnmaterial.ReturnMaterialDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.ReturnMaterialDetailVo" id="ReturnMaterialDetailResult">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="uom" column="uom"/>
        <result property="mrAppQty" column="mr_app_qty"/>
        <result property="noteM" column="note_m"/>
        <result property="unitId" column="unit_id"/>
        <result property="unitName" column="unit_name"/>
        <result property="priceUnitIdF" column="price_unit_id_f"/>
        <result property="priceUnitNameF" column="price_unit_name_f"/>
        <result property="priceQtyF" column="price_qty_f"/>
        <result property="replenishQty" column="replenish_qty"/>
        <result property="purUnitId" column="pur_unit_id"/>
        <result property="purUnitName" column="pur_unit_name"/>
        <result property="purQty" column="pur_qty"/>
        <result property="stockId" column="stock_id"/>
        <result property="stockName" column="stock_name"/>
        <result property="stockLocId" column="stock_loc_id"/>
        <result property="stockLocName" column="stock_loc_name"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectReturnMaterialAppDetailVo">
        SELECT 
            rmad.id,
            rmad.app_id,
            rma.bill_no,
            rmad.material_id,
            bmi.material_code,
            rmad.material_name,
            rmad.uom,
            rmad.mr_app_qty,
            rmad.note_m,
            rmad.unit_id,
            bui.unit_name,
            rmad.price_unit_id_f,
            pui.unit_name as price_unit_name_f,
            rmad.price_qty_f,
            rmad.replenish_qty,
            rmad.pur_unit_id,
            puru.unit_name as pur_unit_name,
            rmad.pur_qty,
            rmad.stock_id,
            '' as stock_name,
            rmad.stock_loc_id,
            '' as stock_loc_name,
            rmad.create_name,
            rmad.create_time,
            rmad.update_name,
            rmad.update_time
        FROM return_material_detail rmad
        LEFT JOIN return_material rma ON rmad.app_id = rma.id
        LEFT JOIN basic_material_info bmi ON rmad.material_id = bmi.material_code
        LEFT JOIN basic_unit_info bui ON rmad.unit_id = bui.id
        LEFT JOIN basic_unit_info pui ON rmad.price_unit_id_f = pui.id
        LEFT JOIN basic_unit_info puru ON rmad.pur_unit_id = puru.id
    </sql>

    <select id="queryReturnMaterialDetailVo" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="ReturnMaterialDetailResult">
        <include refid="selectReturnMaterialAppDetailVo"/>
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND rmad.app_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND (rma.bill_no LIKE CONCAT('%', #{param.keySubWord}, '%') OR bmi.material_code LIKE CONCAT('%', #{param.keySubWord}, '%') OR rmad.material_name LIKE CONCAT('%', #{param.keySubWord}, '%'))
            </if>
        </where>
        ORDER BY rmad.create_time DESC
    </select>

</mapper>
