<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.SimpleProductionInStockDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.SimpleProductionInStockDetailVo" id="SimpleProductionInStockDetailVoResult">
        <id     property="id"               column="id"                 />
        <result property="inStockId"        column="in_stock_id"        />
        <result property="entryId"          column="entry_id"           />
        <result property="materialId"       column="material_id"        />
        <result property="materialName"     column="material_name"      />
        <result property="materialCode"     column="material_code"      />
        <result property="specification"    column="specification"      />
        <result property="inStockType"      column="in_stock_type"      />
        <result property="unitId"           column="unit_id"            />
        <result property="unitName"         column="unit_name"          />
        <result property="baseUnitId"       column="base_unit_id"       />
        <result property="mustQty"          column="must_qty"           />
        <result property="baseMustQty"      column="base_must_qty"      />
        <result property="realQty"          column="real_qty"           />
        <result property="baseRealQty"      column="base_real_qty"      />
        <result property="ownerTypeId"      column="owner_type_id"      />
        <result property="ownerId"          column="owner_id"           />
        <result property="stockId"          column="stock_id"           />
        <result property="workshopId"       column="workshop_id"        />
        <result property="memo"             column="memo"               />
        <result property="stockStatusId"    column="stock_status_id"    />
        <result property="keeperTypeId"     column="keeper_type_id"     />
        <result property="keeperId"         column="keeper_id"          />
    </resultMap>

    <sql id="selectSimpleProductionInStockDetailVo">
        select
            spisd.id, spisd.in_stock_id, spisd.entry_id, spisd.material_id, spisd.material_name,
            spisd.specification, spisd.in_stock_type, spisd.unit_id, spisd.base_unit_id,
            spisd.must_qty, spisd.base_must_qty, spisd.real_qty, spisd.base_real_qty,
            spisd.owner_type_id, spisd.owner_id, spisd.stock_id, spisd.workshop_id, spisd.memo,
            spisd.stock_status_id, spisd.keeper_type_id, spisd.keeper_id,
            bmi.material_code,
            bui.unit_name
        from simple_production_in_stock_detail spisd
        left join basic_material_info bmi on spisd.material_id = bmi.material_code
        left join basic_unit_info bui on spisd.unit_id = bui.id
    </sql>

    <select id="querySimpleProductionInStockDetail" resultMap="SimpleProductionInStockDetailVoResult">
        <include refid="selectSimpleProductionInStockDetailVo"/>
        <where>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND (spisd.material_name LIKE CONCAT('%', #{param.keySubWord}, '%') OR bmi.material_code LIKE CONCAT('%', #{param.keySubWord}, '%'))
            </if>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND spisd.in_stock_id = #{param.keyWord}
            </if>
        </where>
        ORDER BY spisd.id
    </select>
</mapper>
