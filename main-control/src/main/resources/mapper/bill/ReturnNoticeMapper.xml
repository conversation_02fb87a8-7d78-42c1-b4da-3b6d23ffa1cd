<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.ReturnNoticeMapper">

    <resultMap type="com.ruoyi.vo.bill.ReturnNoticeVo" id="ReturnNoticeResult">
        <result property="id" column="id"/>
        <result property="billNo" column="bill_no"/>
        <result property="date" column="date"/>
        <result property="retCustId" column="ret_cust_id"/>
        <result property="retCustName" column="ret_cust_name"/>
        <result property="billTypeId" column="bill_type_id"/>
        <result property="billTypeName" column="bill_type_name"/>
        <result property="businessType" column="business_type"/>
        <result property="businessTypeName" column="business_type_name"/>
        <result property="returnReason" column="return_reason"/>
        <result property="returnReasonName" column="return_reason_name"/>
        <result property="receiveAddress" column="receive_address"/>
        <result property="headLocId" column="head_loc_id"/>
        <result property="headLocName" column="head_loc_name"/>
        <result property="description" column="description"/>
        <result property="linkMan" column="link_man"/>
        <result property="linkPhone" column="link_phone"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectReturnNoticeVo">
        SELECT
            rn.id,
            rn.bill_no,
            rn.date,
            rn.ret_cust_id,
            '' as ret_cust_name,
            rn.bill_type_id,
            '' as bill_type_name,
            rn.business_type,
            '' as business_type_name,
            rn.return_reason,
            '' as return_reason_name,
            rn.receive_address,
            rn.head_loc_id,
            '' as head_loc_name,
            rn.description,
            rn.link_man,
            rn.link_phone,
            rn.create_name,
            rn.create_time,
            rn.update_name,
            rn.update_time
        FROM return_notice rn
    </sql>

    <select id="queryReturnNotice" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="ReturnNoticeResult">
        <include refid="selectReturnNoticeVo"/>
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND (rn.bill_no LIKE CONCAT('%', #{param.keyWord}, '%')
                OR rn.ret_cust_id LIKE CONCAT('%', #{param.keyWord}, '%'))
            </if>

            <if test="param.bdate != null and param.bdate != ''">
                AND rn.date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND rn.date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY rn.create_time DESC
    </select>

</mapper>
