<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.SimpleProductionPickingMaterialMapper">

    <resultMap type="com.ruoyi.vo.bill.SimpleProductionPickingMaterialVo" id="SimpleProductionPickingMaterialVoResult">
        <id     property="id"               column="id"                 />
        <result property="billNo"           column="bill_no"            />
        <result property="description"      column="description"        />
        <result property="pickingDate"      column="picking_date"       />

        <result property="workshopId"       column="workshop_id"        />
        <result property="createName"       column="create_name"        />
        <result property="createTime"       column="create_time"        />
        <result property="updateName"       column="update_name"        />
        <result property="updateTime"       column="update_time"        />
    </resultMap>

    <select id="querySimpleProductionPickingMaterial" resultMap="SimpleProductionPickingMaterialVoResult">
        SELECT
        sppm.id,
        sppm.bill_no,
        sppm.description,
        sppm.picking_date,
        sppm.workshop_id,
        sppm.create_name,
        sppm.create_time,
        sppm.update_name,
        sppm.update_time
        FROM simple_production_picking_material sppm
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND (sppm.bill_no LIKE CONCAT('%', #{param.keyWord}, '%') OR sppm.description LIKE CONCAT('%', #{param.keyWord}, '%'))
            </if>

            <if test="param.bdate != null and param.bdate != ''">
                AND sppm.picking_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND sppm.picking_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY sppm.create_time DESC
    </select>
</mapper>
