<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.SimpleProductionPickingMaterialDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.SimpleProductionPickingMaterialDetailVo" id="SimpleProductionPickingMaterialDetailVoResult">
        <id     property="id"               column="id"                 />
        <result property="pickingId"        column="picking_id"         />
        <result property="entryId"          column="entry_id"           />
        <result property="materialId"       column="material_id"        />
        <result property="materialName"     column="material_name"      />
        <result property="materialCode"     column="material_code"      />
        <result property="specification"    column="specification"      />
        <result property="stockId"          column="stock_id"           />
        <result property="stockStatusId"    column="stock_status_id"    />
        <result property="appQty"           column="app_qty"            />
        <result property="actualQty"        column="actual_qty"         />
        <result property="entryMemo"        column="entry_memo"         />
        <result property="unitId"           column="unit_id"            />
        <result property="unitName"         column="unit_name"          />
        <result property="baseUnitId"       column="base_unit_id"       />
        <result property="stockUnitId"      column="stock_unit_id"      />
    </resultMap>

    <sql id="selectSimpleProductionPickingMaterialDetailVo">
        select
            sppmd.id, sppmd.picking_id, sppmd.entry_id, sppmd.material_id, sppmd.material_name,
            sppmd.specification, sppmd.stock_id, sppmd.stock_status_id,
            sppmd.app_qty, sppmd.actual_qty, sppmd.entry_memo, sppmd.unit_id, sppmd.base_unit_id,
            sppmd.stock_unit_id,
            bmi.material_code,
            bui.unit_name
        from simple_production_picking_material_detail sppmd
        left join basic_material_info bmi on sppmd.material_id = bmi.material_code
        left join basic_unit_info bui on sppmd.unit_id = bui.id
    </sql>

    <select id="querySimpleProductionPickingMaterialDetail" resultMap="SimpleProductionPickingMaterialDetailVoResult">
        <include refid="selectSimpleProductionPickingMaterialDetailVo"/>
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND sppmd.picking_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND (sppmd.material_name LIKE CONCAT('%', #{param.keySubWord}, '%') OR bmi.material_code LIKE CONCAT('%', #{param.keySubWord}, '%'))
            </if>
        </where>
        ORDER BY sppmd.id
    </select>
</mapper>
