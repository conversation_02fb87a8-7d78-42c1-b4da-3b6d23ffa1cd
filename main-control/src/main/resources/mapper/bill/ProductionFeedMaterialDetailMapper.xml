<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.ProductionFeedMaterialDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.ProductionFeedMaterialDetailVo" id="ProductionFeedMaterialDetailVoResult">
        <id     property="id"                   column="id"                     />
        <result property="feedId"               column="feed_id"                />
        <result property="stockId"              column="stock_id"               />
        <result property="materialId"           column="material_id"            />
        <result property="materialName"         column="material_name"          />
        <result property="specification"        column="specification"          />
        <result property="stockLocId"           column="stock_loc_id"           />
        <result property="produceDate"          column="produce_date"           />
        <result property="moBillNo"             column="mo_bill_no"             />
        <result property="moEntryId"            column="mo_entry_id"            />
        <result property="ppBomEntryId"         column="pp_bom_entry_id"        />
        <result property="ownerTypeId"          column="owner_type_id"          />
        <result property="appQty"               column="app_qty"                />
        <result property="actualQty"            column="actual_qty"             />
        <result property="entryDescription"     column="entry_description"      />
        <result property="scrapQty"             column="scrap_qty"              />
        <result property="entryWorkshopId"      column="entry_workshop_id"      />
        <result property="keeperTypeId"         column="keeper_type_id"         />
        <result property="keeperId"             column="keeper_id"              />
        <result property="ownerId"              column="owner_id"               />
        <result property="entrySrcBillType"     column="entry_src_bill_type"    />
        <result property="entrySrcBillNo"       column="entry_src_bill_no"      />
        <result property="parentOwnerTypeId"    column="parent_owner_type_id"   />
        <result property="parentOwnerId"        column="parent_owner_id"        />
        <result property="unitId"               column="unit_id"                />
        <result property="baseUnitId"           column="base_unit_id"           />
        <result property="stockUnitId"          column="stock_unit_id"          />
        <result property="materialCode"         column="material_code"          />
        <result property="stockName"            column="stock_name"             />
        <result property="stockLocName"         column="stock_loc_name"         />
        <result property="workshopName"         column="workshop_name"          />
        <result property="keeperName"           column="keeper_name"            />
        <result property="ownerName"            column="owner_name"             />
        <result property="parentOwnerName"      column="parent_owner_name"      />
        <result property="unitName"             column="unit_name"              />
        <result property="baseUnitName"         column="base_unit_name"         />
        <result property="stockUnitName"        column="stock_unit_name"        />
    </resultMap>

    <select id="queryProductionFeedMaterialDetailVo" resultMap="ProductionFeedMaterialDetailVoResult">
        select
            pfmd.id,
            pfmd.feed_id,
            pfmd.stock_id,
            pfmd.material_id,
            pfmd.material_name,
            pfmd.specification,
            pfmd.stock_loc_id,
            pfmd.produce_date,
            pfmd.mo_bill_no,
            pfmd.mo_entry_id,
            pfmd.pp_bom_entry_id,
            pfmd.owner_type_id,
            pfmd.app_qty,
            pfmd.actual_qty,
            pfmd.entry_description,
            pfmd.scrap_qty,
            pfmd.entry_workshop_id,
            pfmd.keeper_type_id,
            pfmd.keeper_id,
            pfmd.owner_id,
            pfmd.entry_src_bill_type,
            pfmd.entry_src_bill_no,
            pfmd.parent_owner_type_id,
            pfmd.parent_owner_id,
            pfmd.unit_id,
            pfmd.base_unit_id,
            pfmd.stock_unit_id,
            pfmd.material_id as material_code,
            pfmd.stock_id as stock_name,
            pfmd.stock_loc_id as stock_loc_name,
            pfmd.entry_workshop_id as workshop_name,

            bui1.unit_name,
            bui2.unit_name as base_unit_name,
            bui3.unit_name as stock_unit_name
        from production_feed_material_detail pfmd
            left join basic_unit_info bui1 on pfmd.unit_id = bui1.id
            left join basic_unit_info bui2 on pfmd.base_unit_id = bui2.id
            left join basic_unit_info bui3 on pfmd.stock_unit_id = bui3.id
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND pfmd.feed_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND (
                    pfmd.material_id like concat('%', #{param.keySubWord}, '%') OR
                    pfmd.material_name like concat('%', #{param.keySubWord}, '%')
                )
            </if>
        </where>
        ORDER BY pfmd.id
    </select>
</mapper>
