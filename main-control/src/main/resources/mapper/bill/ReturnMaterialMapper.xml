<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.returnmaterial.ReturnMaterialMapper">

    <resultMap type="com.ruoyi.vo.bill.ReturnMaterialVo" id="ReturnMaterialResult">
        <result property="id" column="id"/>
        <result property="billNo" column="bill_no"/>
        <result property="billTypeId" column="bill_type_id"/>
        <result property="billTypeName" column="bill_type_name"/>
        <result property="appDate" column="app_date"/>
        <result property="rmType" column="rm_type"/>
        <result property="rmTypeName" column="rm_type_name"/>
        <result property="rmLoc" column="rm_loc"/>
        <result property="rmLocName" column="rm_loc_name"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="rmMode" column="rm_mode"/>
        <result property="rmModeName" column="rm_mode_name"/>
        <result property="replenishMode" column="replenish_mode"/>
        <result property="replenishModeName" column="replenish_mode_name"/>
        <result property="businessType" column="business_type"/>
        <result property="businessTypeName" column="business_type_name"/>
        <result property="remarks" column="remarks"/>
        <result property="rmReason" column="rm_reason"/>
        <result property="applicantId" column="applicant_id"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="appDeptId" column="app_dept_id"/>
        <result property="appDeptName" column="app_dept_name"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectReturnMaterialAppVo">
        SELECT 
            rma.id,
            rma.bill_no,
            rma.bill_type_id,
            '' as bill_type_name,
            rma.app_date,
            rma.rm_type,
            '' as rm_type_name,
            rma.rm_loc,
            '' as rm_loc_name,
            rma.supplier_id,
            '' as supplier_name,
            rma.rm_mode,
            '' as rm_mode_name,
            rma.replenish_mode,
            '' as replenish_mode_name,
            rma.business_type,
            '' as business_type_name,
            rma.remarks,
            rma.rm_reason,
            rma.applicant_id,
            '' as applicant_name,
            rma.app_dept_id,
            '' as app_dept_name,
            rma.create_name,
            rma.create_time,
            rma.update_name,
            rma.update_time
        FROM return_material rma
    </sql>

    <select id="queryReturnMaterial" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="ReturnMaterialResult">
        <include refid="selectReturnMaterialAppVo"/>
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND (rma.bill_no LIKE CONCAT('%', #{param.keyWord}, '%') OR rma.supplier_id LIKE CONCAT('%', #{param.keyWord}, '%'))
            </if>
            <if test="param.bdate != null and param.bdate != ''">
                AND  rma.app_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND  rma.app_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY rma.create_time DESC
    </select>

</mapper>
