<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.SimpleProductionInStockMapper">

    <resultMap type="com.ruoyi.vo.bill.SimpleProductionInStockVo" id="SimpleProductionInStockVoResult">
        <id     property="id"               column="id"                 />
        <result property="billNo"           column="bill_no"            />
        <result property="description"      column="description"        />
        <result property="inStockDate"      column="in_stock_date"      />

        <result property="createName"       column="create_name"        />
        <result property="createTime"       column="create_time"        />
        <result property="updateName"       column="update_name"        />
        <result property="updateTime"       column="update_time"        />
    </resultMap>

    <select id="querySimpleProductionInStock" resultMap="SimpleProductionInStockVoResult">
        SELECT
        spis.id,
        spis.bill_no,
        spis.description,
        spis.in_stock_date,

        spis.create_name,
        spis.create_time,
        spis.update_name,
        spis.update_time
        FROM simple_production_in_stock spis
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND (spis.bill_no LIKE CONCAT('%', #{param.keyWord}, '%') OR spis.description LIKE CONCAT('%', #{param.keyWord}, '%'))
            </if>
            <if test="param.bdate != null and param.bdate != ''">
                AND spis.in_stock_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND spis.in_stock_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY spis.create_time DESC
    </select>
</mapper>
