<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.receive.ReceiveNoticeDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.ReceiveNoticeDetailVo" id="ReceiveNoticeDetailResult">
        <result property="id" column="id"/>
        <result property="noticeId" column="notice_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="materialModel" column="material_model"/>
        <result property="unitId" column="unit_id"/>
        <result property="unitName" column="unit_name"/>
        <result property="actReceiveQty" column="act_receive_qty"/>
        <result property="preDeliveryDate" column="pre_delivery_date"/>
        <result property="supDelQty" column="sup_del_qty"/>
        <result property="priceUnitId" column="price_unit_id"/>
        <result property="priceUnitName" column="price_unit_name"/>
        <result property="priceUnitQty" column="price_unit_qty"/>
        <result property="stockId" column="stock_id"/>
        <result property="stockName" column="stock_name"/>
        <result property="stockLocId" column="stock_loc_id"/>
        <result property="stockLocName" column="stock_loc_name"/>
        <result property="stockUnitId" column="stock_unit_id"/>
        <result property="stockUnitName" column="stock_unit_name"/>
        <result property="stockQty" column="stock_qty"/>
        <result property="poQty" column="po_qty"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectReceiveNoticeDetailVo">
        SELECT 
            rnd.id,
            rnd.notice_id,
            rn.bill_no,
            rnd.material_id,
            bmi.material_code,
            rnd.material_name,
            rnd.material_model,
            rnd.unit_id,
            bui.unit_name,
            rnd.act_receive_qty,
            rnd.pre_delivery_date,
            rnd.sup_del_qty,
            rnd.price_unit_id,
            pui.unit_name as price_unit_name,
            rnd.price_unit_qty,
            rnd.stock_id,
            '' as stock_name,
            rnd.stock_loc_id,
            '' as stock_loc_name,
            rnd.stock_unit_id,
            sui.unit_name as stock_unit_name,
            rnd.stock_qty,
            rnd.po_qty,
            rnd.create_name,
            rnd.create_time,
            rnd.update_name,
            rnd.update_time
        FROM receive_notice_detail rnd
        LEFT JOIN receive_notice rn ON rnd.notice_id = rn.id
        LEFT JOIN basic_material_info bmi ON rnd.material_id = bmi.material_code
        LEFT JOIN basic_unit_info bui ON rnd.unit_id = bui.id
        LEFT JOIN basic_unit_info pui ON rnd.price_unit_id = pui.id
        LEFT JOIN basic_unit_info sui ON rnd.stock_unit_id = sui.id
    </sql>

    <select id="queryReceiveNoticeDetailVo" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="ReceiveNoticeDetailResult">
        <include refid="selectReceiveNoticeDetailVo"/>
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND rnd.notice_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND rn.bill_no LIKE CONCAT('%', #{param.keySubWord}, '%')
            </if>
            <if test="param.keyThirdWord != null and param.keyThirdWord != ''">
                AND (bmi.material_code LIKE CONCAT('%', #{param.keyThirdWord}, '%')
                     OR rnd.material_name LIKE CONCAT('%', #{param.keyThirdWord}, '%'))
            </if>
        </where>
        ORDER BY rnd.create_time DESC
    </select>

</mapper>
