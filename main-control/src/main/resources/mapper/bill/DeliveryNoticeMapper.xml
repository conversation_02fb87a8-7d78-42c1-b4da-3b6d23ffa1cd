<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.delivery.DeliveryNoticeMapper">

    <resultMap type="com.ruoyi.vo.bill.DeliveryNoticeVo" id="DeliveryNoticeVoResult">
        <id     property="id"               column="id"                 />
        <result property="billNo"           column="bill_no"            />
        <result property="salesmanId"       column="salesman_id"        />
        <result property="saleDeptId"       column="sale_dept_id"       />
        <result property="customerId"       column="customer_id"        />
        <result property="deliveryWay"      column="delivery_way"       />
        <result property="noticeDate"       column="notice_date"        />
        <result property="settleCurrId"     column="settle_curr_id"     />
        <result property="stockerId"        column="stocker_id"         />
        <result property="receiverId"       column="receiver_id"        />
        <result property="settleId"         column="settle_id"          />
        <result property="receiverContactId" column="receiver_contact_id"/>
        <result property="payerId"          column="payer_id"           />
        <result property="receiveAddress"   column="receive_address"    />
        <result property="createName"       column="create_name"        />
        <result property="createTime"       column="create_time"        />
        <result property="updateName"       column="update_name"        />
        <result property="updateTime"       column="update_time"        />
        <result property="customerName"     column="customer_name"      />
        <result property="receiverName"     column="receiver_name"      />
        <result property="settleName"       column="settle_name"        />
        <result property="receiverContactName" column="receiver_contact_name"/>
    </resultMap>

    <select id="queryDeliveryNotice" resultMap="DeliveryNoticeVoResult">
        SELECT
        dn.id,
        dn.bill_no,
        dn.salesman_id,
        dn.sale_dept_id,
        dn.customer_id,
        dn.delivery_way,
        dn.notice_date,
        dn.settle_curr_id,
        dn.stocker_id,
        dn.receiver_id,
        dn.settle_id,
        dn.receiver_contact_id,
        dn.payer_id,
        dn.receive_address,
        dn.create_name,
        dn.create_time,
        dn.update_name,
        dn.update_time,
        c.company_name AS customer_name,
        r.company_name AS receiver_name,
        s.company_name AS settle_name,
        rc.contact_name AS receiver_contact_name
        FROM delivery_notice dn
        LEFT JOIN basic_company_info c ON dn.customer_id = c.id
        LEFT JOIN basic_company_info r ON dn.receiver_id = r.id
        LEFT JOIN basic_company_info s ON dn.settle_id = s.id
        LEFT JOIN basic_contact_info rc ON dn.receiver_contact_id = rc.id
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND dn.bill_no LIKE CONCAT('%', #{param.keyWord}, '%')
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND (c.company_name LIKE CONCAT('%', #{param.keySubWord}, '%') Or dn.customer_id = #{param.keySubWord})
            </if>
            <if test="param.bdate != null and param.bdate != ''">
                AND dn.notice_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND dn.notice_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY dn.create_time DESC
    </select>
</mapper>