<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.purchase.PurchaseOrderMapper">

    <resultMap type="com.ruoyi.vo.bill.PurchaseOrderVo" id="PurchaseOrderResult">
        <id     property="id"                column="id"               />
        <result property="billNo"            column="bill_no"          />
        <result property="purchaseDate"      column="purchase_date"    />
        <result property="supplierId"        column="supplier_id"      />
        <result property="purchaseDeptId"    column="purchase_dept_id" />
        <result property="purchaserId"       column="purchaser_id"     />
        <result property="businessType"      column="business_type"    />
        <result property="providerId"        column="provider_id"      />
        <result property="providerContactId" column="provider_contact_id"/>
        <result property="settleId"          column="settle_id"        />
        <result property="chargeId"          column="charge_id"        />
        <result property="createName"        column="create_name"      />
        <result property="updateName"        column="update_name"      />
        <result property="createTime"        column="create_time"      />
        <result property="updateTime"        column="update_time"      />
        <result property="supplierName"      column="supplier_name"    />
        <result property="contactName"       column="contact_name"     />
    </resultMap>

    <sql id="selectPurchaseOrderVo">
        select
            po.id, po.bill_no, po.purchase_date, po.supplier_id, po.purchase_dept_id, po.purchaser_id,
            po.business_type, po.provider_id, po.provider_contact_id, po.settle_id, po.charge_id,
            po.create_name, po.update_name, po.create_time, po.update_time,
            bci.company_name as supplier_name,
            bci_contact.contact_name as contact_name
        from purchase_order po
        left join basic_company_info bci on po.supplier_id = bci.id
        left join basic_contact_info bci_contact on po.provider_contact_id = bci_contact.id
    </sql>

    <select id="queryPurchaseOrder" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="PurchaseOrderResult">
        <include refid="selectPurchaseOrderVo"/>
        <where>
            <if test="keyWord != null and keyWord != ''">
                AND po.bill_no like concat('%', #{keyWord}, '%')
            </if>
            <if test="keySubWord != null and keySubWord != ''">
                AND bci.company_name like concat('%', #{keySubWord}, '%')
            </if>
            <if test="bdate != null and bdate != ''">
                AND po.purchase_date &gt;= #{bdate}
            </if>
            <if test="edate != null and edate != ''">
                AND po.purchase_date &lt;= #{edate}
            </if>
        </where>
        order by po.create_time desc
    </select>
</mapper>