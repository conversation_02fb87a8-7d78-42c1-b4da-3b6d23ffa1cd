<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.purchase.PurchaseOrderDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.PurchaseOrderDetailVo" id="PurchaseOrderDetailVoResult">
        <id     property="id"           column="id"      />
        <result property="orderId"      column="order_id"  />
        <result property="materialId"   column="material_id" />
        <result property="unitId"       column="unit_id" />
        <result property="priceUnitId"  column="price_unit_id" />
        <result property="qty"          column="qty" />
        <result property="rowType"      column="row_type" />
        <result property="entryNote"    column="entry_note" />
        <result property="deliveryDate" column="delivery_date" />
        <result property="stockUnitId"  column="stock_unit_id" />
        <result property="materialName" column="material_name" />
        <result property="specifications" column="specifications" />
        <result property="unitName"     column="unit_name" />
    </resultMap>

    <sql id="selectPurchaseOrderDetailVo">
        select
            pod.id, pod.order_id, pod.material_id, pod.unit_id, pod.price_unit_id, pod.qty,
            pod.row_type, pod.entry_note, pod.delivery_date, pod.stock_unit_id,
            bmi.material_name,
            bmi.material_code,
            bmi.specifications,
            bui.unit_name
        from purchase_order_detail pod
        left join basic_material_info bmi on pod.material_id = bmi.material_code
        left join basic_unit_info bui on pod.unit_id = bui.id
    </sql>

    <select id="queryPurchaseOrderDetailVo" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="PurchaseOrderDetailVoResult">
        <include refid="selectPurchaseOrderDetailVo"/>
        <where>
            <if test="keyWord != null and keyWord != ''">
                AND pod.order_id = #{keyWord}
            </if>
            <if test="keySubWord != null and keySubWord != ''">
                AND (bmi.material_code like concat('%', #{keySubWord}, '%') or bmi.material_name like concat('%', #{keySubWord}, '%'))
            </if>
        </where>
    </select>
</mapper>