<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.receive.ReceiveNoticeMapper">

    <resultMap type="com.ruoyi.vo.bill.ReceiveNoticeVo" id="ReceiveNoticeResult">
        <result property="id" column="id"/>
        <result property="billNo" column="bill_no"/>
        <result property="receiveDate" column="receive_date"/>
        <result property="billTypeId" column="bill_type_id"/>
        <result property="billTypeName" column="bill_type_name"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="receiveDeptId" column="receive_dept_id"/>
        <result property="receiveDeptName" column="receive_dept_name"/>
        <result property="purchaserId" column="purchaser_id"/>
        <result property="purchaserName" column="purchaser_name"/>
        <result property="note" column="note"/>
        <result property="accType" column="acc_type"/>
        <result property="accTypeName" column="acc_type_name"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectReceiveNoticeVo">
        SELECT
            rn.id,
            rn.bill_no,
            rn.receive_date,
            rn.bill_type_id,
            '' as bill_type_name,
            rn.supplier_id,
            '' as supplier_name,
            rn.receive_dept_id,
            '' as receive_dept_name,
            rn.purchaser_id,
            '' as purchaser_name,
            rn.note,
            rn.acc_type,
            '' as acc_type_name,
            rn.create_name,
            rn.create_time,
            rn.update_name,
            rn.update_time
        FROM receive_notice rn
    </sql>

    <select id="queryReceiveNotice" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="ReceiveNoticeResult">
        <include refid="selectReceiveNoticeVo"/>
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND rn.bill_no LIKE CONCAT('%', #{param.keyWord}, '%')
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND rn.supplier_id = #{param.keySubWord}
            </if>
            <if test="param.bdate != null and param.bdate != ''">
                AND rn.receive_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND rn.receive_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY rn.create_time DESC
    </select>

</mapper>
