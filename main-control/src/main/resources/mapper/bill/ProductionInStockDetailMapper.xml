<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.ProductionInStockDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.ProductionInStockDetailVo" id="ProductionInStockDetailVoResult">
        <id     property="id"                   column="id"                     />
        <result property="inStockId"            column="in_stock_id"            />
        <result property="materialId"           column="material_id"            />
        <result property="materialName"         column="material_name"          />
        <result property="specification"        column="specification"          />
        <result property="productType"          column="product_type"           />
        <result property="inStockType"          column="in_stock_type"          />
        <result property="unitId"               column="unit_id"                />
        <result property="baseUnitId"           column="base_unit_id"           />
        <result property="mustQty"              column="must_qty"               />
        <result property="baseMustQty"          column="base_must_qty"          />
        <result property="realQty"              column="real_qty"               />
        <result property="baseRealQty"          column="base_real_qty"          />
        <result property="ownerTypeId"          column="owner_type_id"          />
        <result property="ownerId"              column="owner_id"               />
        <result property="stockId"              column="stock_id"               />
        <result property="moBillNo"             column="mo_bill_no"             />
        <result property="moId"                 column="mo_id"                  />
        <result property="moEntryId"            column="mo_entry_id"            />
        <result property="moEntrySeq"           column="mo_entry_seq"           />
        <result property="memo"                 column="memo"                   />
        <result property="stockStatusId"        column="stock_status_id"        />
        <result property="keeperTypeId"         column="keeper_type_id"         />
        <result property="keeperId"             column="keeper_id"              />
        <result property="materialCode"         column="material_code"          />
        <result property="unitName"             column="unit_name"              />
        <result property="baseUnitName"         column="base_unit_name"         />
        <result property="stockName"            column="stock_name"             />
        <result property="stockStatusName"      column="stock_status_name"      />
    </resultMap>

    <select id="queryProductionInStockDetailVo" resultMap="ProductionInStockDetailVoResult">
        select
            pisd.id,
            pisd.in_stock_id,
            pisd.material_id,
            pisd.material_name,
            pisd.specification,
            pisd.product_type,
            pisd.in_stock_type,
            pisd.unit_id,
            pisd.base_unit_id,
            pisd.must_qty,
            pisd.base_must_qty,
            pisd.real_qty,
            pisd.base_real_qty,
            pisd.owner_type_id,
            pisd.owner_id,
            pisd.stock_id,
            pisd.mo_bill_no,
            pisd.mo_id,
            pisd.mo_entry_id,
            pisd.mo_entry_seq,
            pisd.memo,
            pisd.stock_status_id,
            pisd.keeper_type_id,
            pisd.keeper_id,
            pisd.material_id as material_code,
            bui1.unit_name,
            bui2.unit_name as base_unit_name,
            pisd.stock_id as stock_name,
            pisd.stock_status_id as stock_status_name
        from production_in_stock_detail pisd
            left join basic_unit_info bui1 on pisd.unit_id = bui1.id
            left join basic_unit_info bui2 on pisd.base_unit_id = bui2.id
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND pisd.in_stock_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND pisd.mo_bill_no like concat('%', #{param.keySubWord}, '%')
            </if>
            <if test="param.keyThirdWord != null and param.keyThirdWord != ''">
                AND (
                    pisd.material_id like concat('%', #{param.keyThirdWord}, '%') OR
                    pisd.material_name like concat('%', #{param.keyThirdWord}, '%')
                )
            </if>
        </where>
        ORDER BY pisd.mo_entry_seq
    </select>
</mapper>
