<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.ReturnNoticeDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.ReturnNoticeDetailVo" id="ReturnNoticeDetailResult">
        <result property="id" column="id"/>
        <result property="noticeId" column="notice_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="materialModel" column="material_model"/>
        <result property="qty" column="qty"/>
        <result property="baseUnitId" column="base_unit_id"/>
        <result property="baseUnitName" column="base_unit_name"/>
        <result property="entryDescription" column="entry_description"/>
        <result property="orderNo" column="order_no"/>
        <result property="lot" column="lot"/>
        <result property="rmType" column="rm_type"/>
        <result property="rmTypeName" column="rm_type_name"/>
        <result property="deliveryDate" column="delivery_date"/>
        <result property="unitId" column="unit_id"/>
        <result property="unitName" column="unit_name"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectReturnNoticeDetailVo">
        SELECT 
            rnd.id,
            rnd.notice_id,
            rn.bill_no,
            rnd.material_id,
            bmi.material_code,
            rnd.material_name,
            rnd.material_model,
            rnd.qty,
            rnd.base_unit_id,
            bui.unit_name as base_unit_name,
            rnd.entry_description,
            rnd.order_no,
            rnd.lot,
            rnd.rm_type,
            '' as rm_type_name,
            rnd.delivery_date,
            rnd.unit_id,
            ui.unit_name,
            rnd.create_name,
            rnd.create_time,
            rnd.update_name,
            rnd.update_time
        FROM return_notice_detail rnd
        LEFT JOIN return_notice rn ON rnd.notice_id = rn.id
        LEFT JOIN basic_material_info bmi ON rnd.material_id = bmi.material_code
        LEFT JOIN basic_unit_info bui ON rnd.base_unit_id = bui.id
        LEFT JOIN basic_unit_info ui ON rnd.unit_id = ui.id
    </sql>

    <select id="queryReturnNoticeDetail" parameterType="com.ruoyi.utils.QueryParamVO" resultMap="ReturnNoticeDetailResult">
        <include refid="selectReturnNoticeDetailVo"/>
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND rnd.notice_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND rn.bill_no LIKE CONCAT('%', #{param.keySubWord}, '%')
            </if>
            <if test="param.keyThirdWord != null and param.keyThirdWord != ''">
                AND (bmi.material_code LIKE CONCAT('%', #{param.keyThirdWord}, '%')
                     OR rnd.material_name LIKE CONCAT('%', #{param.keyThirdWord}, '%'))
            </if>
        </where>
        ORDER BY rnd.create_time DESC
    </select>

</mapper>
