<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.delivery.DeliveryNoticeDetailMapper">

    <resultMap type="com.ruoyi.vo.bill.DeliveryNoticeDetailVo" id="DeliveryNoticeDetailVoResult">
        <id     property="id"                column="id"                  />
        <result property="noticeId"          column="notice_id"           />
        <result property="materialId"        column="material_id"         />
        <result property="parentMaterialId"  column="parent_material_id"  />
        <result property="unitId"            column="unit_id"             />
        <result property="qty"               column="qty"                 />
        <result property="deliveryDate"      column="delivery_date"       />
        <result property="materialType"      column="material_type"       />
        <result property="materialCode"      column="material_code"       />
        <result property="materialName"      column="material_name"       />
        <result property="materialSpecification" column="material_specification"/>
        <result property="unitName"          column="unit_name"           />
    </resultMap>

    <select id="queryDeliveryNoticeDetailVo" resultMap="DeliveryNoticeDetailVoResult">
        select
            dnd.id,
            dnd.notice_id,
            dnd.material_id,
            dnd.parent_material_id,
            dnd.unit_id,
            dnd.qty,
            dnd.delivery_date,
            dnd.material_type,
            bmi.material_code,
            bmi.material_name,
            bmi.specifications as material_specification,
            bui.unit_name
        from delivery_notice_detail dnd
             left join basic_material_info bmi on dnd.material_id = bmi.material_code
             left join basic_unit_info bui on dnd.unit_id = bui.id
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND dnd.notice_id = #{param.keyWord}
            </if>
            <if test="param.keySubWord != null and param.keySubWord != ''">
                AND (
                    bmi.material_code like concat('%', #{param.keySubWord}, '%') OR
                    bmi.material_name like concat('%', #{param.keySubWord}, '%')
                )
            </if>
        </where>
        order by dnd.id
    </select>
</mapper>