<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.bill.production.ProductionPickingMaterialMapper">

    <resultMap type="com.ruoyi.vo.bill.ProductionPickingMaterialVo" id="ProductionPickingMaterialVoResult">
        <id     property="id"               column="id"                 />
        <result property="billNo"           column="bill_no"            />
        <result property="description"      column="description"        />
        <result property="pickingDate"      column="picking_date"       />
        <result property="createName"       column="create_name"        />
        <result property="createTime"       column="create_time"        />
        <result property="updateName"       column="update_name"        />
        <result property="updateTime"       column="update_time"        />

    </resultMap>

    <select id="queryProductionPickingMaterial" resultMap="ProductionPickingMaterialVoResult">
        SELECT
        ppm.id,
        ppm.bill_no,
        ppm.description,
        ppm.picking_date,
        ppm.create_name,
        ppm.create_time,
        ppm.update_name,
        ppm.update_time
        FROM production_picking_material ppm
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">
                AND (ppm.bill_no LIKE CONCAT('%', #{param.keyWord}, '%') OR ppm.description LIKE CONCAT('%', #{param.keyWord}, '%'))
            </if>

            <if test="param.bdate != null and param.bdate != ''">
                AND  ppm.picking_date &gt;= #{param.bdate}
            </if>
            <if test="param.edate != null and param.edate != ''">
                AND  ppm.picking_date &lt;= #{param.edate}
            </if>
        </where>
        ORDER BY ppm.create_time DESC
    </select>
</mapper>
