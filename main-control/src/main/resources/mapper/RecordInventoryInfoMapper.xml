<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.document.RecordInventoryInfoMapper">

  <select id="getMaxIndex" resultType="java.lang.String">
    select bound_index from record_inventory_info where 1 = 1 and bound_index like #{headstr} order by bound_index desc limit 1
  </select>
  <select id="queryInventoryRecord" resultType="com.ruoyi.vo.warehouse.RecordInventoryInfoVo">
    select
    a.*,
    b.warehouse_name
    from
    record_inventory_info a
    join basic_warehouse_info b on
    a.warehouse_code = b.warehouse_code
    where
    1 = 1
    <if test="keyWord != null and keyWord != ''">
      and (
      b.warehouse_code like CONCAT('%', #{keyWord}, '%')
      or b.warehouse_name like CONCAT('%', #{keyWord}, '%')
      )
    </if>
    <if test="state != null">
      and a.state = #{state}
    </if>
    <if test="bdate != null and bdate!='' ">
      and DATE_FORMAT(a.record_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{bdate},'%Y-%m-%d')
    </if>
    <if test="edate != null and edate != '' ">
      and DATE_FORMAT(a.record_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{edate},'%Y-%m-%d')
    </if>
    order by a.record_date desc
  </select>
  <select id="queryByBoundIndex" resultType="com.ruoyi.domain.document.RecordInventoryInfo">
    select * from record_inventory_info where 1=1 and bound_index = #{bound_index}
  </select>
</mapper>