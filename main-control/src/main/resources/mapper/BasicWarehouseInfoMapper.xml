<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicWarehouseInfoMapper">
    <select id="getMaxIndex" resultType="java.lang.String">
        select warehouse_code
        from basic_warehouse_info
        where 1 = 1
          and warehouse_code like #{strDate}
        order by warehouse_code desc limit 1
    </select>
</mapper>