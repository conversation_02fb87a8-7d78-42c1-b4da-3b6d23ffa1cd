<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicBomCustomerMapper">

    <select id="queryBasicBomCustomer" resultType="com.ruoyi.vo.basicData.BomCustomerDto">
        select cust.*,com.company_name,ver.version from basic_bom_customer cust
        left join basic_company_info com on cust.company_code = com.company_code
        left join basic_bom_version ver on cust.bom_version_id = ver.id
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (cust.material_code = #{keyWord})
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND (cust.bom_version_id = #{keySubWord})
        </if>
        order by cust.create_time desc
    </select>
</mapper>

