<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.sys.IProjectSysConfigMapper">

    <resultMap type="com.ruoyi.domain.sys.ProjectSysConfig" id="projectSysConfigMap">
        <result property="id" column="id"/>
        <result property="config_type" column="config_type"/>
        <result property="config_value" column="config_value"/>
        <result property="config_desc" column="config_desc"/>
    </resultMap>
    <update id="uptSysConfig">
        update project_sys_config set config_value = #{config_value} where config_type=#{config_type}
    </update>

    <select id="getSysConfigByType" parameterType="string" resultType="com.ruoyi.domain.sys.ProjectSysConfig">
        select * from project_sys_config where 1 = 1 and config_type = #{config_type} limit 1
    </select>
    <select id="getSysConfigByVal" resultType="com.ruoyi.domain.sys.ProjectSysConfig">
        select * from project_sys_config where 1 = 1 and config_value = #{config_value} limit 1
    </select>
    <select id="getSysConfigValByType" resultType="java.lang.String">
        select config_value from project_sys_config where config_type = #{config_type} limit 1
    </select>

    <select id="getSysConfigTypeByVal" resultType="java.lang.String">
        select config_type from project_sys_config where config_value = #{config_value} limit 1
    </select>
</mapper>