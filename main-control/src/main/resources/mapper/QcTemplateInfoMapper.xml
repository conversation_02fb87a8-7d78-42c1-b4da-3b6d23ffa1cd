<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.qc.QcTemplateInfoMapper">



    <select id="getMaxTemplateCode" resultType="java.lang.String">
        select template_code from qc_template_info where template_code LIKE CONCAT(#{item}, '%') order by template_code desc limit 1
    </select>

</mapper>

