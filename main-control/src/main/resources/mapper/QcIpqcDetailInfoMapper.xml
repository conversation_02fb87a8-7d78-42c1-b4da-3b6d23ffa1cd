<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.qc.QcIpqcDetailInfoMapper">



    <select id="queryQcIpqcDetailInfo" resultType="com.ruoyi.vo.qc.QcIpqcDetailInfoVo">
        select qidi.*,
        qtd.check_method ,
        qtd.stander_val ,
        qtd.unit_of_measure ,
        qtd.threshold_max ,
        qtd.threshold_min,
        qti.item_name,
        qti.qc_tool,
        qti.qc_result_type,
        qti.qc_result_spc,
        -- 计算参考缺陷数量：检测数量 * 对应缺陷率 / 100
        CASE
            WHEN qii.quantity_check IS NOT NULL AND qtm.cr_rate IS NOT NULL
            THEN ROUND(qii.quantity_check * qtm.cr_rate / 100)
            ELSE NULL
        END as egCrQuantity,
        CASE
            WHEN qii.quantity_check IS NOT NULL AND qtm.maj_rate IS NOT NULL
            THEN ROUND(qii.quantity_check * qtm.maj_rate / 100)
            ELSE NULL
        END as egMajQuantity,
        CASE
            WHEN qii.quantity_check IS NOT NULL AND qtm.min_rate IS NOT NULL
            THEN ROUND(qii.quantity_check * qtm.min_rate / 100)
            ELSE NULL
        END as egMinQuantity
        from qc_ipqc_detail_info qidi
        left join qc_template_detail qtd on qidi.template_detail_id = qtd.id
        left join qc_template_item qti on qidi.item_code = qti.item_code
        left join qc_ipqc_task_info qii on qidi.ipqc_code = qii.ipqc_code
        left join qc_template_material qtm on qtd.template_code = qtm.template_code and qii.material_code = qtm.material_code
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND qidi.ipqc_code LIKE CONCAT('%', #{keyWord}, '%')
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND (qti.item_code LIKE CONCAT('%', #{keySubWord}, '%') OR qti.item_name LIKE CONCAT('%', #{keySubWord}, '%'))
        </if>
        order by qti.create_time desc
    </select>

</mapper>

