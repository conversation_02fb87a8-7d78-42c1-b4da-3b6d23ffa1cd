<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.MaterialInfoMapper">

    <update id="lockBasicMaterialInfo">
        UPDATE basic_material_info
        SET is_lock = 1
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="queryBasicMaterialInfo" resultType="com.ruoyi.vo.webResponse.dto.BasicMaterialInfoDto">
        select bmi.id,
               bmi.version_id as versionId,
               bmi.material_code as materialCode,
               bmi.material_name as materialName,
               bmi.classify_code as classifyCode,
               bmi.material_sort as materialSort,
               bmi.specifications,
               bmi.texture,
               pu.unit_name as purchaseUnit,
               pru.unit_name as produceUnit,
               bmi.material_img as materialImg,
               bmi.serial_number as serialNumber,
               bmi.is_shipped_item as isShippedItem,
               bmi.is_out_source as isOutSource,
               bmi.is_out_process as isOutProcess,
               bmi.is_lock as isLock,
               bmi.batch_id as batchId,
               bmi.expiry_date as expiryDate,
               bmi.min_inventory as minInventory,
               bmi.max_inventory as maxInventory,
               bmi.remark,
               bmi.create_name as createName,
               bmi.create_time as createTime,
               bmi.update_name as updateName,
               bmi.update_time as updateTime,
               bmc.classify_name as classifyName,
               ver.version
        from basic_material_info bmi
        left join basic_material_classify bmc on bmc.classify_code = bmi.classify_code
        left join material_classify_type_mapping mctm on mctm.type_value = bmc.type and mctm.is_active = 1
        left join basic_bom_version ver on bmi.version_id = ver.id
        left join basic_unit_info pu on pu.id = bmi.purchase_unit
        left join basic_unit_info pru on pru.id = bmi.produce_unit
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (bmi.material_code like concat('%',(#{keyWord}),'%') OR bmi.material_name LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND bmi.specifications like concat('%',(#{keySubWord}),'%')
        </if>
        <if test="keyThirdWord != null and keyThirdWord.trim() != ''">
            AND bmi.classify_code = #{keyThirdWord}
        </if>
        <if test="keyFourWord != null and keyFourWord.trim() != ''">
            AND bmc.type = #{keyFourWord}
        </if>
        order by bmi.create_time desc
    </select>
</mapper>

