<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.UpperSignalDataMapper">
    <select id="querySignalData" resultType="com.ruoyi.domain.basicData.UpperSignalData">
        select
        *
        from upper_signal_data
        where 1=1
        <if test="keyWord != null and keyWord != ''">
            and product_id = #{keyWord}
        </if>
        <if test="keySubWord != null and keySubWord != ''">
            and signal_type LIKE CONCAT('%', #{keySubWord}, '%')
        </if>
        <if test="bdate != null and bdate != ''">
            and DATE_FORMAT(rece_time, '%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{bdate}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="edate != null and edate != ''">
            and DATE_FORMAT(rece_time, '%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{edate}, '%Y-%m-%d %H:%i:%s')
        </if>
        order by rece_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        <selectKey keyProperty="id" resultType="string" order="BEFORE">
            SELECT replace(uuid(),'-','') as id from dual
        </selectKey>
        insert into upper_signal_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="request != null and request != '' ">
                request,
            </if>
            <if test="response != null and response != '' ">
                response,
            </if>
            <if test="receTime != null">
                rece_time,
            </if>
            <if test="signalType != null and signalType != ''">
                signal_type,
            </if>
            <if test="productId != null and productId != ''">
                product_id,
            </if>
            <if test="responseTime != null">
                response_time,
            </if>
            <if test="isWeb != null">
                is_web,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id},
            </if>
            <if test="request != null and request != '' ">
                #{request},
            </if>
            <if test="response != null and response != ''">
                #{response},
            </if>
            <if test="receTime != null">
                #{receTime},
            </if>
            <if test="signalType != null and signalType != ''">
                #{signalType},
            </if>
            <if test="productId != null and productId != ''">
                #{productId},
            </if>
            <if test="responseTime != null">
                #{responseTime},
            </if>
            <if test="isWeb != null">
                #{isWeb},
            </if>
        </trim>
    </insert>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from upper_signal_data where id = #{id}
    </delete>


</mapper>

