<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.qc.QcTemplateItemMapper">



    <select id="getMaxItemCode" resultType="java.lang.String">
        select item_code from qc_template_item where item_code LIKE CONCAT(#{item}, '%') order by item_code desc limit 1
    </select>

</mapper>

