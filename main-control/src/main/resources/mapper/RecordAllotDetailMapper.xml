<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.document.RecordAllotDetailMapper">

  <update id="updateByPrimaryKey" parameterType="com.ruoyi.domain.document.RecordAllotDetail">
    update record_allot_detail
      <set>
          <if test="boundIndex != null and boundIndex != ''">
              bound_index = #{boundIndex,jdbcType=VARCHAR},
          </if>
          <if test="materialNum != null">
              material_num = #{materialNum,jdbcType=INTEGER},
          </if>
          <if test="orignMaterialCode != null and orignMaterialCode != ''">
              orign_material_code = #{orignMaterialCode,jdbcType=VARCHAR},
          </if>
          <if test="orignContainer != null and orignContainer != ''">
              orign_container = #{orignContainer,jdbcType=VARCHAR},
          </if>
          <if test="orignBatch != null and orignBatch != ''">
              orign_batch = #{orignBatch,jdbcType=VARCHAR},
          </if>
          <if test="orignProduceDate != null">
              orign_produce_date = #{orignProduceDate,jdbcType=TIMESTAMP},
          </if>
          <if test="orignUpperIndex != null and orignUpperIndex != ''">
              orign_upper_index = #{orignUpperIndex,jdbcType=VARCHAR},
          </if>
          <if test="destMaterialCode != null and destMaterialCode != ''">
              dest_material_code = #{destMaterialCode,jdbcType=VARCHAR},
          </if>
          <if test="destContainer != null and destContainer != ''">
              dest_container = #{destContainer,jdbcType=VARCHAR},
          </if>
          <if test="destBatch != null and destBatch != ''">
              dest_batch = #{destBatch,jdbcType=VARCHAR},
          </if>
          <if test="destProduceDate != null">
              dest_produce_date = #{destProduceDate,jdbcType=TIMESTAMP},
          </if>
          <if test="destUpperIndex != null and destUpperIndex != ''">
              dest_upper_index = #{destUpperIndex,jdbcType=VARCHAR},
          </if>
          <if test="remark != null and remark != ''">
              remark = #{remark,jdbcType=VARCHAR},
          </if>
      </set>
      where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryRecordAllotInfoDetail" resultType="com.ruoyi.vo.document.RecordAllotDetailVo">
      SELECT rad.id AS id,
      bmf_dest.material_name AS dest_material_name,
      bmf_dest.material_img AS dest_material_img,
      bmf_dest.specifications AS dest_material_spec,
      bwi_dest.warehouse_name AS dest_warehouse,
      rad.dest_batch AS dest_batch,
      rad.dest_container AS dest_container,
      rad.dest_material_code AS dest_material_code,
      rad.dest_produce_date AS dest_produce_date,
      rad.dest_upper_index AS dest_upper_index,
      rad.material_num AS material_num,
      bmf_orign.material_name AS orign_material_name,
      bmf_orign.material_img AS orign_material_img,
      bmf_orign.specifications AS orign_material_spec,
      bwi_orign.warehouse_name AS orign_warehouse,
      rad.orign_batch AS orign_batch,
      rad.orign_container AS orign_container,
      rad.orign_material_code AS orign_material_code,
      rad.orign_produce_date AS orign_produce_date,
      rad.orign_upper_index AS orign_upper_index,
      rad.remark AS remark
      FROM record_allot_detail rad
      JOIN
      basic_material_info bmf_orign ON rad.orign_material_code = bmf_orign.material_code
      JOIN
      basic_material_info bmf_dest ON rad.dest_material_code = bmf_dest.material_code
      JOIN
      basic_warehouse_container bwc_orign ON rad.orign_container = bwc_orign.container_code
      JOIN
      basic_warehouse_location bwl_orign ON bwc_orign.location_code = bwl_orign.location_code
      JOIN
      basic_warehouse_info bwi_orign ON bwl_orign.warehouse_code = bwi_orign.warehouse_code
      JOIN
      basic_warehouse_container bwc_dest ON rad.dest_container = bwc_dest.container_code
      JOIN
      basic_warehouse_location bwl_dest ON bwc_dest.location_code = bwl_dest.location_code
      JOIN
      basic_warehouse_info bwi_dest ON bwl_dest.warehouse_code = bwi_dest.warehouse_code
      WHERE 1 = 1
      <if test="keyWord != null and keyWord.trim() != ''">
          and rad.bound_index = #{keyWord}
      </if>
  </select>
</mapper>