<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.ProductInfoMapper">


    <select id="queryBasicProductInfo" resultType="com.ruoyi.vo.webResponse.dto.BasicProductInfoDto">
        select pri.*,bmc.classify_name,ver.version from basic_product_info pri
        left join basic_material_classify bmc on bmc.classify_code = pri.classify_code
        left join basic_bom_version ver on pri.version_id = ver.id
        where 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (pri.product_code = #{keyWord} or pri.product_name LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND pri.specifications = #{keySubWord}
        </if>
        <if test="keyThirdWord != null and keyThirdWord.trim() != ''">
            AND pri.product_type = #{keyThirdWord}
        </if>
        order by pri.create_time desc
    </select>
</mapper>

