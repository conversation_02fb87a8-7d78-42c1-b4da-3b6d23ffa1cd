<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicWarehouseLocationMapper">
    <update id="updateLocationChildren">
        update basic_warehouse_location set ancestors =
        <foreach collection="childrens" item="item" index="index"
                 separator=" " open="case id" close="end">
            when #{item.id} then #{item.ancestors}
        </foreach>
        where id in
        <foreach collection="childrens" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <delete id="deleteByLocationCode">
        delete from basic_warehouse_container where location_code = #{locationCode}
    </delete>
    <select id="getMaxIndex" resultType="java.lang.String">
        select location_code
        from basic_warehouse_location
        where 1 = 1
          and location_code like #{strDate}
        order by location_code desc limit 1
    </select>
    <select id="checkLocationNameUnique" resultType="com.ruoyi.domain.basicData.BasicWarehouseLocation">
        select * from basic_warehouse_location where node_name=#{nodeName} and parent_id = #{parentId} and warehouse_code = #{warehouseCode} limit 1
    </select>
    <select id="selectChildrenLocationById" resultType="com.ruoyi.domain.basicData.BasicWarehouseLocation">
        select * from basic_warehouse_location where find_in_set(#{id}, ancestors)
    </select>
    <select id="hasChildById" resultType="java.lang.Integer">
        select count(1) from basic_warehouse_location
        where parent_id = #{id} limit 1
    </select>
    <select id="getLocationByWareHouseType" resultType="java.lang.String">
        SELECT a.location_code
        FROM basic_warehouse_location a
            JOIN basic_warehouse_info b ON a.warehouse_code = b.warehouse_code
        WHERE 1=1
        <if test="wareHouseType != null">
            and b.warehouse_type = #{wareHouseType}
        </if>
        <if test="state != null">
            and a.state = #{state}
        </if>
          AND a.node_name LIKE '%库位%'
    </select>
    <select id="getLocationIsUse" resultType="java.lang.String">
        SELECT a.location_code
        FROM basic_warehouse_location a
                 join basic_warehouse_container b on b.location_code = a.location_code
                 join basic_material_batch_inventory c on c.container_code = b.container_code
        WHERE 1=1
        <if test="locationCodeList != null and locationCodeList.size() > 0">
            and a.location_code IN
            <foreach collection="locationCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        group by a.location_code
    </select>
    <select id="qryMaterialContainer" resultType="com.ruoyi.domain.basicData.BasicWarehouseContainer">
        select
            b.*
        from
            basic_material_batch_inventory a
                left join basic_warehouse_container b on
                a.container_code = b.container_code
        where
            b.location_code = #{locationCode}
            and a.material_num &gt; 0
    </select>
</mapper>