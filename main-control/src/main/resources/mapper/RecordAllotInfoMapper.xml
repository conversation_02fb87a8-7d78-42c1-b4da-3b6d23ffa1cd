<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.document.RecordAllotInfoMapper">

  <select id="getMaxIndex" resultType="java.lang.String">
    select bound_index
    from record_allot_info
    where 1 = 1
    and bound_index like #{headstr}
    order by bound_index desc
    limit 1
  </select>
    <select id="queryRecordAllotInfo" resultType="com.ruoyi.domain.document.RecordAllotInfo">
      SELECT *
      FROM record_allot_info
      WHERE 1 = 1
      <if test="keyWord != null and keyWord != ''">
        AND bound_index = #{keyWord}
      </if>
      <if test="keySubWord != null and keySubWord != ''">
        AND transfer_rule = #{keySubWord}
      </if>
      <if test="state != null">
        AND state = #{state}
      </if>
      <if test="bdate != null and bdate != ''">
        AND DATE_FORMAT(record_date,'%Y-%m-%d') &gt;= DATE_FORMAT(#{bdate},'%Y-%m-%d')
      </if>
      <if test="edate != null and edate != ''">
        AND DATE_FORMAT(record_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{edate},'%Y-%m-%d')
      </if>
      order by record_date desc
    </select>
    <update id="updateByPrimaryKey" parameterType="com.ruoyi.domain.document.RecordAllotInfo">
    update record_allot_info
    <set>
      <if test="boundIndex != null and boundIndex != ''">
        bound_index = #{boundIndex,jdbcType=VARCHAR},
      </if>
      <if test="transferRule != null and transferRule != ''">
        transfer_rule = #{transferRule,jdbcType=INTEGER},
      </if>
      <if test="reasons != null and reasons != ''">
        reasons = #{reasons,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="recordDate != null">
        record_date = #{recordDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lockDate != null">
        lock_date = #{lockDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recorder != null and recorder != ''">
        recorder = #{recorder,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>