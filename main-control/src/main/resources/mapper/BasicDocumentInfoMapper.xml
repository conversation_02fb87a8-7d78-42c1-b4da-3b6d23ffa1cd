<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicDocumentInfoMapper">
    <select id="getDocumentInfoByCode" resultType="com.ruoyi.domain.basicData.BasicDocumentInfo">
        select * from basic_document_info where id = #{transactionCode} or transaction_code = #{transactionCode} limit 1
    </select>
</mapper>

