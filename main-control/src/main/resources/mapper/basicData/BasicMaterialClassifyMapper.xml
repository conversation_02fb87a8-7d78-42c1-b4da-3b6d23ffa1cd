<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicMaterialClassifyMapper">

    <!-- 查询物料分类列表（包含类型中文名称） -->
    <select id="queryMaterialClassifyWithTypeName" 
            resultType="com.ruoyi.vo.basicData.BasicMaterialClassifyWithTypeNameVo">
        SELECT 
            bmc.id,
            bmc.type,
            COALESCE(mctm.material_name, '未知类型') AS typeName,
            bmc.classify_code AS classifyCode,
            bmc.classify_name AS classifyName,
            bmc.parent_id AS parentId,
            bmc.classify_des AS classifyDes,
            bmc.create_time AS createTime
        FROM 
            basic_material_classify bmc
        LEFT JOIN 
            material_classify_type_mapping mctm 
            ON mctm.type_value = bmc.type 
            AND mctm.is_active = 1
        WHERE 1 = 1
        <if test="keyWord != null and keyWord.trim() != ''">
            AND (bmc.classify_name LIKE CONCAT('%', #{keyWord}, '%') 
                 OR bmc.classify_code LIKE CONCAT('%', #{keyWord}, '%'))
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            AND (bmc.type = #{keySubWord} OR mctm.material_name LIKE CONCAT('%', #{keySubWord}, '%'))
        </if>
        ORDER BY bmc.create_time DESC
    </select>

</mapper>
