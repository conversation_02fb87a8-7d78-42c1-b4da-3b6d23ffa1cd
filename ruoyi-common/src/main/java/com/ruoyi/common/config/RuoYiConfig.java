package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "ruoyi")
public class RuoYiConfig
{
    /** 项目名称 */
    private String name;

    /** 版本 */
    private String version;

    /** 版权年份 */
    private String copyrightYear;

    /** 实例演示开关 */
    private boolean demoEnabled;

    /** 上传路径 */
    private static String profile;

    /** 上传路径—上游下发套料图 */
    private static String dxfFile;

    /** 获取地址开关 */
    private static boolean addressEnabled;

    /**
     * 任务信息上报地址
     */
    private static String reportPlateInfoUrl;

    /**
     * 任务控制反馈地址
     */
    private static String stateFeedbackUrl;

    /**
     * 需要检测的工位
     */
    private static String checkStationProductId;

    /**
     * 需要提前手动结束任务的工位
     */
    private static String needDealStationProductId;

    /**
     * 线程执行时限，单位：分钟
     */
    private static Integer threadRunMinute;

    public static Integer getThreadRunMinute() {
        return threadRunMinute;
    }

    public void setThreadRunMinute(Integer threadRunMinute) {
        RuoYiConfig.threadRunMinute = threadRunMinute;
    }

    public static String getCheckStationProductId() {
        return checkStationProductId;
    }

    public void setCheckStationProductId(String checkStationProductId) {
        RuoYiConfig.checkStationProductId = checkStationProductId;
    }

    public static String getNeedDealStationProductId() {
        return needDealStationProductId;
    }

    public void setNeedDealStationProductId(String needDealStationProductId) {
        RuoYiConfig.needDealStationProductId = needDealStationProductId;
    }

    public static String getReportPlateInfoUrl() {
        return reportPlateInfoUrl;
    }

    public  void setReportPlateInfoUrl(String reportPlateInfoUrl) {
        RuoYiConfig.reportPlateInfoUrl = reportPlateInfoUrl;
    }

    public static String getStateFeedbackUrl() {
        return stateFeedbackUrl;
    }

    public  void setStateFeedbackUrl(String stateFeedbackUrl) {
        RuoYiConfig.stateFeedbackUrl = stateFeedbackUrl;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getVersion()
    {
        return version;
    }

    public void setVersion(String version)
    {
        this.version = version;
    }

    public String getCopyrightYear()
    {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear)
    {
        this.copyrightYear = copyrightYear;
    }

    public boolean isDemoEnabled()
    {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled)
    {
        this.demoEnabled = demoEnabled;
    }

    public static String getProfile()
    {
        return profile;
    }

    public void setProfile(String profile)
    {
        RuoYiConfig.profile = profile;
    }

    public static String getDxfFile() {
        return dxfFile;
    }

    public void setDxfFile(String dxfFile) {
        RuoYiConfig.dxfFile = dxfFile;
    }

    public static boolean isAddressEnabled()
    {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled)
    {
        RuoYiConfig.addressEnabled = addressEnabled;
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath()
    {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath()
    {
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath()
    {
        return getProfile() + "/upload";
    }

    /**
     * 获取上游下发的DXF文件转编码后的存储路径
     */
    public static String getDxfFilePath()
    {
        return getDxfFile();
    }
}
