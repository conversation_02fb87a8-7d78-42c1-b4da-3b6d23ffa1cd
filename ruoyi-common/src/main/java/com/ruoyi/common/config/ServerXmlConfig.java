package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "server")
public class ServerXmlConfig
{
    private static String port;

    public static String getPort() {
        return port;
    }

    public void setPort(String port) {
        port = port;
    }
}
