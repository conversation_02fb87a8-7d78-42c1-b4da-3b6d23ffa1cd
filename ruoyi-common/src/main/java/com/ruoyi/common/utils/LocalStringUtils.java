package com.ruoyi.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LocalStringUtils {
	
	public static String getDataUUID(){
		String uuid = UUID.randomUUID().toString().replaceAll("-","");
        return uuid;
	}

	public synchronized static String getPointCgEmail(String userName){
		Map<String, String> map = new HashMap<String, String>();
		map.put("dengyuping", "<EMAIL>");
		map.put("limin", "<EMAIL>");
		map.put("mengyue", "<EMAIL>");
		map.put("lishuhui", "<EMAIL>");
		return map.get(userName);
	}
	/**
	 * 截取类似料号信息字符串,例如：103小型保险丝-VF215 10A250V(1031764) 的括号中料号数字
	 * @param index
	 * @return
	 */
	public synchronized static String getSubIndexStr(String index){
		if(!StringUtils.isEmpty(index)
				&& index.contains("(")
				&& index.contains(")")){
			index = index.trim();
			index = index.substring(index.lastIndexOf("(") + 1, index.lastIndexOf(")"));
		}
		return index;
	}
	
	public synchronized static String getForeSubIndexStr(String index){
		if(!StringUtils.isEmpty(index)
				&& index.contains("(")
				&& index.contains(")")){
			index = index.substring(0, index.lastIndexOf("("));
		}
		return index;
	}
	
	/**
	 * 获取最左边的括号内容中的数据
	 * @param index
	 * @return
	 */
	public synchronized static String getFirstSubIndexStr(String index){
		if(!StringUtils.isEmpty(index)
				&& index.contains("(")
				&& index.contains(")")){
			index = index.trim();
			index = index.substring(index.indexOf("(") + 1, index.indexOf(")"));
		}
		return index;
	}
	
	public synchronized static String unescape(String src) {
		StringBuffer tmp = new StringBuffer();
		tmp.ensureCapacity(src.length());
		int lastPos = 0, pos = 0;
		char ch;
		while (lastPos < src.length()) {
			pos = src.indexOf("%", lastPos);
			if (pos == lastPos) {
				if (src.charAt(pos + 1) == 'u') {
					ch = (char) Integer.parseInt(
							src.substring(pos + 2, pos + 6), 16);
					tmp.append(ch);
					lastPos = pos + 6;
				} else {
					ch = (char) Integer.parseInt(
							src.substring(pos + 1, pos + 3), 16);
					tmp.append(ch);
					lastPos = pos + 3;
				}
			} else {
				if (pos == -1) {
					tmp.append(src.substring(lastPos));
					lastPos = src.length();
				} else {
					tmp.append(src.substring(lastPos, pos));
					lastPos = pos;
				}
			}
		}
		return tmp.toString();
	}
	
	public static void main(String args[]){
		String sqt = "深圳好(1111111)风频借力科技有限公司(34201711270003)";
		System.out.println(getFirstSubIndexStr(sqt));
		
		String rate = "17%";
		System.out.println(rate.split("%")[0]);
		
		String test = "109线束用保险丝座~线束用保险丝座~V-209A(V-714)~RoHS(UL12# 210mm线环状红色线材)(1090004)";
	}

	public static String replaceSpecialChar(String gsize) {
		// TODO Auto-generated method stub
		if(StringUtils.isNotEmpty(gsize)){
			gsize = gsize.trim().replaceAll("&amp;times;", "x")
					.trim().replaceAll("&times;", "x")
					.trim().replaceAll("&delta;","δ")
					.trim().replaceAll("&amp;delta;","δ")
					.trim().replaceAll("&Phi;","Φ")
					.trim().replaceAll("&phi;","φ")
					.trim().replaceAll("&amp;Phi;","Φ")
					.trim().replaceAll("&amp;amp;amp;Phi;","Φ")
					.trim().replaceAll("&amp;phi;","φ")
					.trim().replaceAll("&le;","≤")
					.trim().replaceAll("&amp;le;","≤")
					
					.trim().replaceAll("&lt;","<")
					.trim().replaceAll("&amp;lt;","<")
					
					.trim().replaceAll("&gt;",">")
					.trim().replaceAll("&amp;gt;",">")
					
					.trim().replaceAll("&ge;","≥")
					.trim().replaceAll("&amp;ge;","≥")
					
					.trim().replaceAll("&empty;","∅")
					.trim().replaceAll("&amp;empty;","∅")
					
					.trim().replaceAll("&nbsp;"," ")
					
					.trim().replaceAll("&mdash;","—")
					.trim().replaceAll("&amp;mdash;","—")
					
					.trim().replaceAll("&quot;", "\"")
					.trim().replaceAll("&amp;quot;", "\"")
					
					.trim().replaceAll("&deg;", "°")
					.trim().replaceAll("&amp;deg;", "°")
					.trim().replaceAll("&plusmn;", "±")
					.trim().replaceAll("&amp;plusmn;", "±")
					.trim().replaceAll("&Omega;", "Ω")
					.trim().replaceAll("&amp;Omega;", "Ω")
					.trim().replaceAll("&amp;amp;Omega;", "Ω")
					.trim().replaceAll("&micro;", "µ")
					.trim().replaceAll("&amp;micro;", "µ")
					.trim().replaceAll("&amp;mu;", "μ")
					.trim().replaceAll("&mu;", "μ")
					
					.trim().replaceAll("&amp;", "&")
					.trim().replaceAll("&amp;amp;", "&")
					;
		}
		return gsize;
	}

	//审核内容
	public static String getCheckComment(String premark, String comment, String name) {
		// TODO Auto-generated method stub
		String checkContent = premark + "\n审核：" + name + "->" + comment;
		return checkContent;
	}
	
	public static String getChineseMoney(String money) {
        String text = transChineseMoney1(money) + transChineseMoney2(money);
        Pattern p = Pattern.compile("零分", Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(text);
        text = m.replaceAll("");  
        return text;  
    }  
	/** 
     * 截得输入金额的整数部分，并将其转换成中文大写的格式 <br> 
     * <br> 
     * 其他描述：输入数字超过接受范围时拒绝转换并退出。<br> 
     * @return 返回转换后的字符串
     */  
    public static String transChineseMoney1(String s) {
        String ss = s;
        String tmpnewchar = "";
        String[] part = ss.split("\\.");
  
        if (part[0].length() > 10) {  
            // 超出可转换位数  
            return "";  
        }  
        for (int i = 0; i < part[0].length(); i++) {  
            char perchar = part[0].charAt(i);  
            if (perchar == '0')  
                tmpnewchar = tmpnewchar + "零";  
            if (perchar == '1')  
                tmpnewchar = tmpnewchar + "壹";  
            if (perchar == '2')  
                tmpnewchar = tmpnewchar + "贰";  
            if (perchar == '3')  
                tmpnewchar = tmpnewchar + "叁";  
            if (perchar == '4')  
                tmpnewchar = tmpnewchar + "肆";  
            if (perchar == '5')  
                tmpnewchar = tmpnewchar + "伍";  
            if (perchar == '6')  
                tmpnewchar = tmpnewchar + "陆";  
            if (perchar == '7')  
                tmpnewchar = tmpnewchar + "柒";  
            if (perchar == '8')  
                tmpnewchar = tmpnewchar + "捌";  
            if (perchar == '9')  
                tmpnewchar = tmpnewchar + "玖";  
  
            int j = part[0].length() - i - 1;  
            if (j == 0)  
                tmpnewchar = tmpnewchar + "圆";  
            if (j == 1 && perchar != 0)  
                tmpnewchar = tmpnewchar + "拾";  
            if (j == 2 && perchar != 0)  
                tmpnewchar = tmpnewchar + "佰";  
            if (j == 3 && perchar != 0)  
                tmpnewchar = tmpnewchar + "仟";  
            if (j == 4 && perchar != 0)  
                tmpnewchar = tmpnewchar + "万";  
            if (j == 5 && perchar != 0)  
                tmpnewchar = tmpnewchar + "拾";  
            if (j == 6 && perchar != 0)  
                tmpnewchar = tmpnewchar + "佰";  
            if (j == 7 && perchar != 0)  
                tmpnewchar = tmpnewchar + "仟";  
            if (j == 8 && perchar != 0)  
                tmpnewchar = tmpnewchar + "亿";  
            if (j == 9 && perchar != 0)  
                tmpnewchar = tmpnewchar + "拾";  
        }  
        return tmpnewchar;  
    }  
  
    /** 
     * 截得输入金额的小数部分，并将其转换成中文大写的格式 <br> 
     * <br> 
     * 其他描述：小数部分超过两位时系统自动截断。<br> 
     *  
     *
     * @return 返回转换后的字符串 
     */  
    public static String transChineseMoney2(String s) {
        String ss = s;
        String tmpnewchar1 = "";
        String[] part = ss.split("\\.");
  
        if (ss.indexOf(".") != -1) {  
            if (part[1].length() > 2) {  
                // MessageDialog.openInformation(null,"提示","小数点之后只能保留两位,系统将自动截段");  
                part[1] = part[1].substring(0, 2);  
            }  
            for (int i = 0; i < part[1].length(); i++) {  
                char perchar = part[1].charAt(i);  
//              System.out.println(perchar);  
                if (perchar == '0')  
                    tmpnewchar1 = tmpnewchar1 + "零";  
                if (perchar == '1')  
                    tmpnewchar1 = tmpnewchar1 + "壹";  
                if (perchar == '2')  
                    tmpnewchar1 = tmpnewchar1 + "贰";  
                if (perchar == '3')  
                    tmpnewchar1 = tmpnewchar1 + "叁";  
                if (perchar == '4')  
                    tmpnewchar1 = tmpnewchar1 + "肆";  
                if (perchar == '5')  
                    tmpnewchar1 = tmpnewchar1 + "伍";  
                if (perchar == '6')  
                    tmpnewchar1 = tmpnewchar1 + "陆";  
                if (perchar == '7')  
                    tmpnewchar1 = tmpnewchar1 + "柒";  
                if (perchar == '8')  
                    tmpnewchar1 = tmpnewchar1 + "捌";  
                if (perchar == '9')  
                    tmpnewchar1 = tmpnewchar1 + "玖";  
  
                if (i == 0 && perchar != 0)  
                    tmpnewchar1 = tmpnewchar1 + "角";  
                if (i == 1 && perchar != 0)  
                    tmpnewchar1 = tmpnewchar1 + "分";  
            }  
        }  
        return tmpnewchar1;  
    }  
   
    
    /**
     * 返回物料号存储的内容，带有名称，规格单独存储！
     * @param gname
     * @return
     */
	public static String getWlInfo(String gname, String gindex) {
		// TODO Auto-generated method stub
		return gname + "(" + gindex + ")";
	}

	/**
	 * 是否指定的人审核采购报价单
	 * @param mname
	 * @return
	 */
	public static boolean needCheckPointNameCgQuote(String mname) {
		// TODO Auto-generated method stub
		if(mname != null && (mname.contains("xutuquan") || mname.contains("limin"))){
			return true;
		}
		return false;
	}


}
