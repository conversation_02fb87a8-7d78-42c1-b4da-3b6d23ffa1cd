#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffc9ac03520, pid=22628, tid=20412
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# Problematic frame:
# C  [jna11171974750513244952.dll+0x3520]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\25046f62500b28466155dbc8db461daf\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\25046f62500b28466155dbc8db461daf\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4b6665279886199cd0b9ff7636b3154c-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 23G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
Time: Thu Jul  3 15:50:10 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.3958) elapsed time: 18408.690212 seconds (0d 5h 6m 48s)

---------------  T H R E A D  ---------------

Current thread (0x00000249c1bc8a20):  JavaThread "WorkspaceEventsHandler"        [_thread_in_native, id=20412, stack(0x000000e88b900000,0x000000e88ba00000) (1024K)]

Stack: [0x000000e88b900000,0x000000e88ba00000],  sp=0x000000e88b9fe968,  free space=1018k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [jna11171974750513244952.dll+0x3520]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 11882  com.sun.jna.Native.getLastError()I (0 bytes) @ 0x00000249eca3cc96 [0x00000249eca3cc40+0x0000000000000056]
J 13122 c2 org.eclipse.core.internal.filesystem.local.LocalFile.fetchInfo(ILorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/filesystem/IFileInfo; (45 bytes) @ 0x00000249ecd07390 [0x00000249ecd062a0+0x00000000000010f0]
J 13516 c2 org.eclipse.core.filesystem.provider.FileStore.fetchInfo()Lorg/eclipse/core/filesystem/IFileInfo; (27 bytes) @ 0x00000249ecde0968 [0x00000249ecde0920+0x0000000000000048]
J 13126 c1 org.eclipse.core.internal.localstore.UnifiedTree.addRootToQueue()V (96 bytes) @ 0x00000249e6250624 [0x00000249e624fba0+0x0000000000000a84]
J 24246 c1 org.eclipse.core.internal.localstore.UnifiedTree.initializeQueue()V (70 bytes) @ 0x00000249e5a57524 [0x00000249e5a57040+0x00000000000004e4]
J 24245 c1 org.eclipse.core.internal.localstore.UnifiedTree.accept(Lorg/eclipse/core/internal/localstore/IUnifiedTreeVisitor;I)V (128 bytes) @ 0x00000249e574e81c [0x00000249e574e6e0+0x000000000000013c]
J 12799 c1 org.eclipse.core.internal.localstore.FileSystemResourceManager.refreshResource(Lorg/eclipse/core/resources/IResource;IZLorg/eclipse/core/runtime/IProgressMonitor;)Z (168 bytes) @ 0x00000249e6126874 [0x00000249e6125780+0x00000000000010f4]
J 12798 c1 org.eclipse.core.internal.localstore.FileSystemResourceManager.refresh(Lorg/eclipse/core/resources/IResource;IZLorg/eclipse/core/runtime/IProgressMonitor;)Z (88 bytes) @ 0x00000249e6124fcc [0x00000249e6124ca0+0x000000000000032c]
J 12801 c1 org.eclipse.core.internal.resources.Resource.refreshLocal(ILorg/eclipse/core/runtime/IProgressMonitor;)V (253 bytes) @ 0x00000249e612ab6c [0x00000249e6128ca0+0x0000000000001ecc]
J 14529 c1 org.eclipse.core.internal.resources.File.refreshLocal(ILorg/eclipse/core/runtime/IProgressMonitor;)V (18 bytes) @ 0x00000249e66a2644 [0x00000249e66a2460+0x00000000000001e4]
j  org.eclipse.jdt.ls.core.internal.managers.IBuildSupport.refresh(Lorg/eclipse/core/resources/IResource;Lorg/eclipse/jdt/ls/core/internal/managers/ProjectsManager$CHANGE_TYPE;Lorg/eclipse/core/runtime/IProgressMonitor;)V+105
j  org.eclipse.jdt.ls.core.internal.managers.IBuildSupport.fileChanged(Lorg/eclipse/core/resources/IResource;Lorg/eclipse/jdt/ls/core/internal/managers/ProjectsManager$CHANGE_TYPE;Lorg/eclipse/core/runtime/IProgressMonitor;)Z+4
j  org.eclipse.jdt.ls.core.internal.managers.MavenBuildSupport.fileChanged(Lorg/eclipse/core/resources/IResource;Lorg/eclipse/jdt/ls/core/internal/managers/ProjectsManager$CHANGE_TYPE;Lorg/eclipse/core/runtime/IProgressMonitor;)Z+23
j  org.eclipse.jdt.ls.core.internal.managers.StandardProjectsManager.fileChanged(Ljava/lang/String;Lorg/eclipse/jdt/ls/core/internal/managers/ProjectsManager$CHANGE_TYPE;)V+333
j  org.eclipse.jdt.ls.core.internal.handlers.WorkspaceEventsHandler.handleFileEvent(Lorg/eclipse/lsp4j/FileEvent;)V+250
j  org.eclipse.jdt.ls.core.internal.handlers.WorkspaceEventsHandler.lambda$0()V+32
j  org.eclipse.jdt.ls.core.internal.handlers.WorkspaceEventsHandler$$Lambda+0x00000249813be5a0.run()V+4
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.6
j  java.lang.Thread.run()V+19 java.base@21.0.6
v  ~StubRoutines::call_stub 0x00000249ebcb10e7

siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), writing address 0x00007ffc9ac03520


Registers:
RAX=0x00007ffc9ac03520, RBX=0x00000000eab3cbd8, RCX=0x00000249c1bc8dd8, RDX=0x000000e88b9fe9c0
RSP=0x000000e88b9fe968, RBP=0x000000e88b9fe9d0, RSI=0x00000000eab3cc68, RDI=0x00000249f3370000
R8 =0x00000249de750500, R9 =0x0000000000000001, R10=0x00007ffbf1ec5b35, R11=0x000000e88b9fe1c0
R12=0x0000000000000000, R13=0x0000000000000001, R14=0x000000e88b9fe9c0, R15=0x00000249c1bc8a20
RIP=0x00007ffc9ac03520, EFLAGS=0x0000000000010246

XMM[0]=0x0000000000000000 0xffffffffffffffff
XMM[1]=0x0000000000000000 0x0000000000000000
XMM[2]=0x0000000000000000 0x0000000000000000
XMM[3]=0x0000000000000000 0x0000000000000000
XMM[4]=0x0000000000000000 0x0000000000000000
XMM[5]=0x0000000000000000 0x0000000000000000
XMM[6]=0x437c1d2e12c2a0bb 0x5447d83340ae8190
XMM[7]=0x135ef2740d4ca252 0xdafe94a98835de12
XMM[8]=0x058f45e5c28876ce 0x815ea5aed2feee14
XMM[9]=0x004a8aa1004a8aa1 0x004a8aa142aa3851
XMM[10]=0x0000000000000000 0x00000000425fadb0
XMM[11]=0x1cab466e1cab466e 0x1cab466e5f557ebf
XMM[12]=0x0000000000000000 0x0000000042aa3851
XMM[13]=0x0000000000000000 0x746163696c707061
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fa2


Register to memory mapping:

RAX=0x00007ffc9ac03520 jna11171974750513244952.dll
RBX=0x00000000eab3cbd8 is an oop: com.sun.jna.Memory 
{0x00000000eab3cbd8} - klass: 'com/sun/jna/Memory'
 - ---- fields (total size 4 words):
 - protected 'peer' 'J' @16  2515846512128 (0x00000249c4233e00)
 - private final 'cleanable' 'Lcom/sun/jna/internal/Cleaner$Cleanable;' @12  a 'com/sun/jna/internal/Cleaner$CleanerRef'{0x00000000eab3cc68} (0xeab3cc68)
 - protected 'size' 'J' @24  592 (0x0000000000000250)
RCX=0x00000249c1bc8dd8 points into unknown readable memory: 0x00007ffbf1e5b7b0 | b0 b7 e5 f1 fb 7f 00 00
RDX=0x000000e88b9fe9c0 is pointing into the stack for thread: 0x00000249c1bc8a20
RSP=0x000000e88b9fe968 is pointing into the stack for thread: 0x00000249c1bc8a20
RBP=0x000000e88b9fe9d0 is pointing into the stack for thread: 0x00000249c1bc8a20
RSI=0x00000000eab3cc68 is an oop: com.sun.jna.internal.Cleaner$CleanerRef 
{0x00000000eab3cc68} - klass: 'com/sun/jna/internal/Cleaner$CleanerRef'
 - ---- fields (total size 6 words):
 - private 'referent' 'Ljava/lang/Object;' @12  a 'com/sun/jna/Memory'{0x00000000eab3cbd8} (0xeab3cbd8)
 - volatile 'queue' 'Ljava/lang/ref/ReferenceQueue;' @16  a 'java/lang/ref/ReferenceQueue'{0x00000000c0cbd240} (0xc0cbd240)
 - volatile 'next' 'Ljava/lang/ref/Reference;' @20  null (0x00000000)
 - private transient 'discovered' 'Ljava/lang/ref/Reference;' @24  null (0x00000000)
 - private final 'cleaner' 'Lcom/sun/jna/internal/Cleaner;' @28  a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348} (0xc0cbd348)
 - private final 'cleanupTask' 'Ljava/lang/Runnable;' @32  a 'com/sun/jna/Memory$MemoryDisposer'{0x00000000eab3cc50} (0xeab3cc50)
 - private 'previous' 'Lcom/sun/jna/internal/Cleaner$CleanerRef;' @36  null (0x00000000)
 - private 'next' 'Lcom/sun/jna/internal/Cleaner$CleanerRef;' @40  a 'com/sun/jna/internal/Cleaner$CleanerRef'{0x00000000c140ec68} (0xc140ec68)
RDI=0x00000249f3370000 is an unknown value
R8 =0x00000249de750500 points into unknown readable memory: 0x00000249de740db0 | b0 0d 74 de 49 02 00 00
R9 =0x0000000000000001 is an unknown value
R10=0x00007ffbf1ec5b35 jvm.dll
R11=0x000000e88b9fe1c0 is pointing into the stack for thread: 0x00000249c1bc8a20
R12=0x0 is null
R13=0x0000000000000001 is an unknown value
R14=0x000000e88b9fe9c0 is pointing into the stack for thread: 0x00000249c1bc8a20
R15=0x00000249c1bc8a20 is a thread

Top of Stack: (sp=0x000000e88b9fe968)
0x000000e88b9fe968:   00000249eca3cd29 00000249c1bc8dd8
0x000000e88b9fe978:   000000e88b9fe9c0 00000249f863fb70
0x000000e88b9fe988:   00000249c4233e00 00000000eab3cc98
0x000000e88b9fe998:   00000000eab3cbd8 00000000eab3c9b8
0x000000e88b9fe9a8:   000000003dd6a257 0000000000000001
0x000000e88b9fe9b8:   00000000eab3c278 00000000c0c8f170
0x000000e88b9fe9c8:   000000000000000f 00000000c0cbd1b0
0x000000e88b9fe9d8:   00000249ecd07390 000000e88b9fead0
0x000000e88b9fe9e8:   00007ffbf12ee70a 0000000000000060
0x000000e88b9fe9f8:   00000249c1bc8a20 00000000eab3cbd8
0x000000e88b9fea08:   00000000eab3c9d8 00000000eab3cab8
0x000000e88b9fea18:   00000000eab3cb30 00000000eab3cbc0
0x000000e88b9fea28:   00000000eab3cbd8 0000000000000004
0x000000e88b9fea38:   00000000eab3bb48 eab3c208c19a22c0
0x000000e88b9fea48:   c08855f000000001 eab3bb70eab3bb48
0x000000e88b9fea58:   00000000ec042a20 0000000000000009
0x000000e88b9fea68:   00000000ec042798 0000000000000009
0x000000e88b9fea78:   00000249c1285f70 00000000eab3c9d8
0x000000e88b9fea88:   00000249ecde0968 0000000000000009
0x000000e88b9fea98:   00000000eab3bfb8 000000e88b9ff090
0x000000e88b9feaa8:   00000249e6250624 000000e88b9feb30
0x000000e88b9feab8:   00000249c1bc8a20 000000e88b9feb30
0x000000e88b9feac8:   0000000000000001 000000e88b9feb30
0x000000e88b9fead8:   00000249c1bc8a20 000000e88b9feb10
0x000000e88b9feae8:   0000000000000000 000000e88b9ff090
0x000000e88b9feaf8:   00000249e696fa8c 000000000000004b
0x000000e88b9feb08:   00007ffbf146eb62 00000000eab3bfb8
0x000000e88b9feb18:   00000249c1bc8a20 0000024980002268
0x000000e88b9feb28:   000000020000000c 00007ffbf1c3d338
0x000000e88b9feb38:   00000249c1bc8a20 0000024980002268
0x000000e88b9feb48:   00000000eab3c0c0 00000000c1407c70
0x000000e88b9feb58:   00000000eab3c9d8 000000000000004b 

Instructions: (pc=0x00007ffc9ac03520)
0x00007ffc9ac03420:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03430:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03440:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03450:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03460:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03470:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03480:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03490:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac034a0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac034b0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac034c0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac034d0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac034e0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac034f0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03500:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03510:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03520:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03530:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03540:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03550:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03560:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03570:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03580:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03590:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac035a0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac035b0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac035c0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac035d0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac035e0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac035f0:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03600:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x00007ffc9ac03610:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00000249eca3cd29 is at entry_point+233 in (nmethod*)0x00000249eca3ca90
Compiled method (n/a) 18408737 11882     n 0       com.sun.jna.Native::getLastError (native)
 total in heap  [0x00000249eca3ca90,0x00000249eca3ce70] = 992
 relocation     [0x00000249eca3cbf0,0x00000249eca3cc28] = 56
 main code      [0x00000249eca3cc40,0x00000249eca3ce65] = 549
 stub code      [0x00000249eca3ce65,0x00000249eca3ce68] = 3
 oops           [0x00000249eca3ce68,0x00000249eca3ce70] = 8
stack at sp + 1 slots: 0x00000249c1bc8dd8 points into unknown readable memory: 0x00007ffbf1e5b7b0 | b0 b7 e5 f1 fb 7f 00 00
stack at sp + 2 slots: 0x000000e88b9fe9c0 is pointing into the stack for thread: 0x00000249c1bc8a20
stack at sp + 3 slots: 0x00000249f863fb70 points into unknown readable memory: 0x005c003f005c005c | 5c 00 5c 00 3f 00 5c 00
stack at sp + 4 slots: 0x00000249c4233e00 points into unknown readable memory: 0x96e0768100000020 | 20 00 00 00 81 76 e0 96
stack at sp + 5 slots: 0x00000000eab3cc98 is an oop: com.sun.jna.WString 
{0x00000000eab3cc98} - klass: 'com/sun/jna/WString'
 - ---- fields (total size 2 words):
 - private 'string' 'Ljava/lang/String;' @12  "\\?\D:\myCode\wms_business\ruoyi-admin\src\main\resources\application-druid.yml~"{0x00000000eab3cbc0} (0xeab3cbc0)
stack at sp + 6 slots: 0x00000000eab3cbd8 is an oop: com.sun.jna.Memory 
{0x00000000eab3cbd8} - klass: 'com/sun/jna/Memory'
 - ---- fields (total size 4 words):
 - protected 'peer' 'J' @16  2515846512128 (0x00000249c4233e00)
 - private final 'cleanable' 'Lcom/sun/jna/internal/Cleaner$Cleanable;' @12  a 'com/sun/jna/internal/Cleaner$CleanerRef'{0x00000000eab3cc68} (0xeab3cc68)
 - protected 'size' 'J' @24  592 (0x0000000000000250)
stack at sp + 7 slots: 0x00000000eab3c9b8 is an oop: org.eclipse.core.runtime.Path 
{0x00000000eab3c9b8} - klass: 'org/eclipse/core/runtime/Path'
 - ---- fields (total size 4 words):
 - private 'hash' 'I' @12  0 (0x00000000)
 - private final 'flags' 'B' @16  9 (0x09)
 - private final 'device' 'Ljava/lang/String;' @20  "D:"{0x00000000eab3c850} (0xeab3c850)
 - private final 'segments' '[Ljava/lang/String;' @24  a 'java/lang/String'[7] {0x00000000eab3c988} (0xeab3c988)


Compiled method (n/a) 18408751 11882     n 0       com.sun.jna.Native::getLastError (native)
 total in heap  [0x00000249eca3ca90,0x00000249eca3ce70] = 992
 relocation     [0x00000249eca3cbf0,0x00000249eca3cc28] = 56
 main code      [0x00000249eca3cc40,0x00000249eca3ce65] = 549
 stub code      [0x00000249eca3ce65,0x00000249eca3ce68] = 3
 oops           [0x00000249eca3ce68,0x00000249eca3ce70] = 8

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000249fa8be610} 'getLastError' '()I' in 'com/sun/jna/Native'
  #           [sp+0x70]  (sp of caller)
  0x00000249eca3cc40: 448b 5208 | 49bb 0000 | 0080 4902 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x00000249eca3cc58: ;   {runtime_call ic_miss_stub}
  0x00000249eca3cc58: 0000 e921 | 1a2c ff90 
[Verified Entry Point]
  0x00000249eca3cc60: 8984 2400 | 80ff ff55 | 488b ec48 | 83ec 6090 | 4181 7f20 | 0c00 0000 

  0x00000249eca3cc78: ;   {runtime_call StubRoutines (final stubs)}
  0x00000249eca3cc78: 7405 e881 

  0x00000249eca3cc7c: ;   {oop(a 'java/lang/Class'{0x00000000c0c8f170} = 'com/sun/jna/Native')}
  0x00000249eca3cc7c: 0b2b ff49 | be70 f1c8 | c000 0000 | 004c 8974 | 2450 4c8d | 7424 5049 | 8bd6 c5f8 

  0x00000249eca3cc98: ;   {internal_word}
  0x00000249eca3cc98: 7749 ba96 | cca3 ec49 | 0200 004d | 8997 a003 | 0000 4989 | a798 0300 

  0x00000249eca3ccb0: ;   {external_word}
  0x00000249eca3ccb0: 0049 ba35 | 5bec f1fb | 7f00 0041 | 803a 000f | 8446 0000 

  0x00000249eca3ccc4: ;   {metadata({method} {0x00000249fa8be610} 'getLastError' '()I' in 'com/sun/jna/Native')}
  0x00000249eca3ccc4: 0052 48ba | 08e6 8bfa | 4902 0000 | 498b cf48 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec 
  0x00000249eca3cce4: ;   {runtime_call}
  0x00000249eca3cce4: 0848 b8c0 | 4b96 f1fb | 7f00 00ff | d048 83c4 | 08e9 0c00 

  0x00000249eca3ccf8: ;   {runtime_call}
  0x00000249eca3ccf8: 0000 48b8 | c04b 96f1 | fb7f 0000 | ffd0 4883 | c420 5a49 | 8d8f b803 | 0000 41c7 | 8744 0400 
  0x00000249eca3cd18: 0004 0000 

  0x00000249eca3cd1c: ;   {runtime_call}
  0x00000249eca3cd1c: 0048 b820 | 35c0 9afc | 7f00 00ff | d0c5 f877 | 41c7 8744 | 0400 0005 | 0000 00f0 | 8344 24c0 
  0x00000249eca3cd3c: 0049 3baf | 4804 0000 | 0f87 0e00 | 0000 4183 | bf40 0400 | 0000 0f84 | 2b00 0000 | c5f8 7748 
  0x00000249eca3cd5c: 8945 f849 | 8bcf 4c8b | e448 83ec | 2048 83e4 

  0x00000249eca3cd6c: ;   {runtime_call}
  0x00000249eca3cd6c: f048 b860 | cf61 f1fb | 7f00 00ff | d049 8be4 | 4d33 e448 | 8b45 f841 | c787 4404 | 0000 0800 
  0x00000249eca3cd8c: 0000 4183 | bfc0 0400 | 0002 0f84 | 9c00 0000 

  0x00000249eca3cd9c: ;   {external_word}
  0x00000249eca3cd9c: 49ba 355b | ecf1 fb7f | 0000 4180 | 3a00 0f84 | 4c00 0000 | 4889 45f8 

  0x00000249eca3cdb4: ;   {metadata({method} {0x00000249fa8be610} 'getLastError' '()I' in 'com/sun/jna/Native')}
  0x00000249eca3cdb4: 48ba 08e6 | 8bfa 4902 | 0000 498b | cf48 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 

  0x00000249eca3cdd0: ;   {runtime_call}
  0x00000249eca3cdd0: 83ec 0848 | b8c0 4b96 | f1fb 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 

  0x00000249eca3cde8: ;   {runtime_call}
  0x00000249eca3cde8: 48b8 c04b | 96f1 fb7f | 0000 ffd0 | 4883 c420 | 488b 45f8 | 49c7 8798 | 0300 0000 | 0000 0049 
  0x00000249eca3ce08: c787 a003 | 0000 0000 | 0000 c5f8 | 7749 8b8f | 2804 0000 | c781 0001 | 0000 0000 | 0000 c949 
  0x00000249eca3ce28: 837f 0800 | 0f85 0100 

  0x00000249eca3ce30: ;   {runtime_call StubRoutines (initial stubs)}
  0x00000249eca3ce30: 0000 c3e9 | c840 27ff | c5f8 7748 | 8945 f84c | 8be4 4883 | ec20 4883 

  0x00000249eca3ce48: ;   {runtime_call}
  0x00000249eca3ce48: e4f0 48b8 | a083 96f1 | fb7f 0000 | ffd0 498b | e44d 33e4 | 488b 45f8 | e937 ffff | fff4 f4f4 
[/MachCode]


Compiled method (c2) 18408758 13122       4       org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo (45 bytes)
 total in heap  [0x00000249ecd05e10,0x00000249ecd0ac90] = 20096
 relocation     [0x00000249ecd05f70,0x00000249ecd06298] = 808
 main code      [0x00000249ecd062a0,0x00000249ecd08e08] = 11112
 stub code      [0x00000249ecd08e08,0x00000249ecd08f20] = 280
 oops           [0x00000249ecd08f20,0x00000249ecd08f70] = 80
 metadata       [0x00000249ecd08f70,0x00000249ecd09250] = 736
 scopes data    [0x00000249ecd09250,0x00000249ecd0a1f0] = 4000
 scopes pcs     [0x00000249ecd0a1f0,0x00000249ecd0a7b0] = 1472
 dependencies   [0x00000249ecd0a7b0,0x00000249ecd0a8a8] = 248
 handler table  [0x00000249ecd0a8a8,0x00000249ecd0ac20] = 888
 nul chk table  [0x00000249ecd0ac20,0x00000249ecd0ac90] = 112

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000249fbcb6478} 'fetchInfo' '(ILorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/filesystem/IFileInfo;' in 'org/eclipse/core/internal/filesystem/local/LocalFile'
  # this:     rdx:rdx   = 'org/eclipse/core/internal/filesystem/local/LocalFile'
  # parm0:    r8        = int
  # parm1:    r9:r9     = 'org/eclipse/core/runtime/IProgressMonitor'
  #           [sp+0xb0]  (sp of caller)
  0x00000249ecd062a0: 448b 5208 | 49bb 0000 | 0080 4902 | 0000 4d03 | d349 3bc2 

  0x00000249ecd062b4: ;   {runtime_call ic_miss_stub}
  0x00000249ecd062b4: 0f85 c683 | fffe 6690 | 0f1f 4000 
[Verified Entry Point]
  0x00000249ecd062c0: 8984 2400 | 80ff ff55 | 4881 eca0 | 0000 0090 | 4181 7f20 | 0c00 0000 | 0f85 1e2b | 0000 4889 
  0x00000249ecd062e0: 5424 2844 | 8b5a 1044 | 895c 2430 

  0x00000249ecd062ec: ;   {oop(a 'java/lang/Class'{0x00000000c13d8b90} = 'org/eclipse/core/internal/filesystem/local/LocalFileNativesManager')}
  0x00000249ecd062ec: 49ba 908b | 3dc1 0000 | 0000 458b | 5a74 458b 

  0x00000249ecd062fc: ;   {metadata('org/eclipse/core/internal/filesystem/local/Win32Handler')}
  0x00000249ecd062fc: 5308 4181 | fa20 445c | 010f 8549 | 1900 0049 | 8b87 b801 | 0000 4c8b | d049 83c2 | 304d 3b97 
  0x00000249ecd0631c: c801 0000 | 0f83 a815 | 0000 4d89 | 97b8 0100 | 004c 8bd8 | 4983 c310 | 48c7 0001 | 0000 004c 
  0x00000249ecd0633c: 8bc0 4983 

  0x00000249ecd06340: ;   {metadata('org/eclipse/core/filesystem/provider/FileInfo')}
  0x00000249ecd06340: c028 c740 | 0840 485c | 0141 0f0d | 8ac0 0000 | 00c5 f5ef | c962 d1fe | 087f 0b44 | 8960 20c5 
  0x00000249ecd06360: fdef c0c4 | c179 d600 | c740 0c00 

  0x00000249ecd0636c: ;   {oop(""{0x00000000c06972f8})}
  0x00000249ecd0636c: 00c0 00c7 | 4024 f872 | 69c0 4889 | 4424 3844 | 8b54 2430 | 498b d290 

  0x00000249ecd06384: ;   {static_call}
  0x00000249ecd06384: c5f8 77e8 

  0x00000249ecd06388: ; ImmutableOopMap {[40]=Oop [48]=NarrowOop [56]=Oop }
                      ;*invokestatic toLongWindowsPath {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@9 (line 53)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06388: 3474 feff 

  0x00000249ecd0638c: ;   {other}
  0x00000249ecd0638c: 0f1f 8400 | 7c05 0000 | 4c8b c844 | 8b58 1445 | 8b53 0c44 | 0fbe 4010 | c4c2 3af7 | ea83 fd07 
  0x00000249ecd063ac: 0f84 ca1b | 0000 498b | 87b8 0100 | 004c 8bd0 | 4983 c220 | 4d3b 97c8 | 0100 000f | 8338 1500 
  0x00000249ecd063cc: 004d 8997 | b801 0000 | 4c8b d849 | 83c3 1048 | c700 0100 | 0000 410f | 0d8a c000 

  0x00000249ecd063e8: ;   {metadata('com/sun/jna/Memory')}
  0x00000249ecd063e8: 0000 c740 | 0850 902d | 0144 8960 | 0cc5 fdef | c0c4 c179 | d603 4c89 | 4c24 4048 | c740 1850 
  0x00000249ecd06408: 0200 0048 | 8be8 ba50 | 0200 0090 

  0x00000249ecd06414: ;   {static_call}
  0x00000249ecd06414: c5f8 77e8 

  0x00000249ecd06418: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop }
                      ;*invokestatic malloc {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Memory::malloc@1 (line 753)
                      ; - com.sun.jna.Memory::<init>@27 (line 117)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06418: 44fd 8dff 

  0x00000249ecd0641c: ;   {other}
  0x00000249ecd0641c: 0f1f 8400 | 0c06 0001 | 488b cd48 | 8945 1048 | 85c0 0f84 | 881b 0000 | 4883 f880 | 7c36 4883 
  0x00000249ecd0643c: f87f 7f30 | 448b d841 | 8dab 8000 | 0000 81fd | 0001 0000 | 0f83 a61f | 0000 4d63 

  0x00000249ecd06458: ;   {oop(a 'java/lang/Long'[256] {0x00000000c0003e88})}
  0x00000249ecd06458: d349 bb88 | 3e00 c000 | 0000 0047 | 8b94 9310 | 0200 0049 | 8bda eb50 | 498b 9fb8 | 0100 004c 
  0x00000249ecd06478: 8bd3 4983 | c218 4d3b | 97c8 0100 | 000f 8359 | 1500 004d | 8997 b801 | 0000 4c8b | db49 83c3 
  0x00000249ecd06498: 1048 c703 | 0100 0000 | 410f 0d8a | c000 0000 

  0x00000249ecd064a8: ;   {metadata('java/lang/Long')}
  0x00000249ecd064a8: c743 0890 | 5d03 0044 | 8963 0cc5 | fdef c0c4 | c179 d603 | 4889 4310 | 4d8b 8fb8 | 0100 004d 
  0x00000249ecd064c8: 8bd1 4983 | c220 4d3b | 97c8 0100 | 000f 8356 | 1400 004d | 8997 b801 | 0000 410f | 0d8a c000 
  0x00000249ecd064e8: 0000 49c7 | 0101 0000 | 004d 8bd1 | 4983 c210 

  0x00000249ecd064f8: ;   {metadata('java/lang/ref/WeakReference')}
  0x00000249ecd064f8: 41c7 4108 | 80da 0300 | c5fd efc0 | 62d1 fe08 | 7f02 4889 | 5c24 2048 | 8be9 4889 | 4c24 4841 
  0x00000249ecd06518: ;   {oop(a 'java/lang/ref/ReferenceQueue$Null'{0x00000000c0002b80})}
  0x00000249ecd06518: 8969 0c41 | c741 1080 | 2b00 c04d | 8bd1 49c1 | ea09 49bb | 0000 37f3 | 4902 0000 | 4788 2413 
  0x00000249ecd06538: f083 4424 | c000 33ff 

  0x00000249ecd06540: ;   {oop(a 'java/util/concurrent/ConcurrentHashMap'{0x00000000c0b6efd8})}
  0x00000249ecd06540: 48ba d8ef | b6c0 0000 | 0000 4c8b | 4424 2048 | 894c 2420 

  0x00000249ecd06554: ;   {optimized virtual_call}
  0x00000249ecd06554: c5f8 77e8 

  0x00000249ecd06558: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*invokevirtual putVal {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.concurrent.ConcurrentHashMap::put@4
                      ; - com.sun.jna.Memory::<init>@92 (line 121)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06558: e433 9900 

  0x00000249ecd0655c: ;   {other}
  0x00000249ecd0655c: 0f1f 8400 | 4c07 0002 | 4d8b 8fb8 | 0100 004d | 8bd1 4983 | c218 4d3b | 97c8 0100 | 000f 8317 
  0x00000249ecd0657c: 1400 004d | 8997 b801 | 0000 410f | 0d8a c000 | 0000 49c7 | 0101 0000 

  0x00000249ecd06594: ;   {metadata('com/sun/jna/Memory$MemoryDisposer')}
  0x00000249ecd06594: 0041 c741 | 0850 f52d | 0145 8961 | 0c4c 8b54 | 2448 4d8b | 5a10 4d89 

  0x00000249ecd065ac: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd065ac: 5910 49b8 | 48d3 cbc0 | 0000 0000 | 488d 9c24 | 8000 0000 | 498b 00a8 | 020f 8523 | 0000 0048 
  0x00000249ecd065cc: 83c8 0148 | 8903 f049 | 0fb1 180f | 8437 0000 | 0048 2bc4 | 4825 07f0 | ffff 4889 | 03e9 2400 
  0x00000249ecd065ec: 0000 4c8b | d848 33c0 | f04d 0fb1 | 7b3e 48c7 | 0303 0000 | 0074 114c | 3bf8 7515 | 49ff 8386 
  0x00000249ecd0660c: 0000 0048 | 33c0 7509 | 49ff 8748 | 0500 0033 | c00f 8551 | 1600 004d | 8b9f b801 | 0000 4d8b 
  0x00000249ecd0662c: c349 83c0 | 304d 3b87 | c801 0000 | 0f83 2113 | 0000 4d89 | 87b8 0100 | 0041 0f0d | 88c0 0000 
  0x00000249ecd0664c: 0049 c703 | 0100 0000 | 4d8b c349 

  0x00000249ecd06658: ;   {metadata('com/sun/jna/internal/Cleaner$CleanerRef')}
  0x00000249ecd06658: 83c0 1041 | c743 0828 | d02d 01c5 | fdef c062 | d1fe 287f | 0041 896b 

  0x00000249ecd06670: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd06670: 0c41 b848 | d3cb c045 | 8b40 0c45 | 85c0 750c 

  0x00000249ecd06680: ;   {oop(a 'java/lang/ref/ReferenceQueue$Null'{0x00000000c0002b80})}
  0x00000249ecd06680: 49b8 802b | 00c0 0000 | 0000 eb00 | 498b c841 | 894b 104d | 8bc3 498b | c949 c1e8 | 0949 b900 
  0x00000249ecd066a0: 0037 f349 | 0200 0047 | 8824 01f0 | 8344 24c0 | 0041 894b 

  0x00000249ecd066b4: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd066b4: 2041 c743 | 1c48 d3cb | c04d 8bc3 | 49c1 e809 | 4788 2401 

  0x00000249ecd066c8: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd066c8: 41b9 48d3 | cbc0 458b | 410c 4585 | c00f 8436 | 1500 0049 | 8bc8 488d | 9c24 9000 | 0000 488b 
  0x00000249ecd066e8: 01a8 020f | 8523 0000 | 0048 83c8 | 0148 8903 | f048 0fb1 | 190f 8437 | 0000 0048 | 2bc4 4825 
  0x00000249ecd06708: 07f0 ffff | 4889 03e9 | 2400 0000 | 4c8b c848 | 33c0 f04d | 0fb1 793e | 48c7 0303 | 0000 0074 
  0x00000249ecd06728: 114c 3bf8 | 7515 49ff | 8186 0000 | 0048 33c0 | 7509 49ff | 8748 0500 | 0033 c00f | 8566 1500 
  0x00000249ecd06748: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd06748: 0041 b948 | d3cb c045 | 8b49 1445 | 85c9 0f84 | c018 0000 | 498b da45 | 894b 2845 

  0x00000249ecd06764: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd06764: 8bd1 41b9 | 48d3 cbc0 | 418b 6910 

  0x00000249ecd06770: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd06770: 498b f3bf | 48d3 cbc0 | 8977 1441 | 8972 244d | 8bcb 49c1 | e909 48bf | 0000 37f3 | 4902 0000 
  0x00000249ecd06790: 4688 240f | 49c1 ea09 | 49b9 0000 | 37f3 4902 | 0000 4788 

  0x00000249ecd067a4: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd067a4: 2411 49ba | 48d3 cbc0 | 0000 0000 | 49c1 ea09 | 4788 2411 | 85ed 0f84 | a818 0000 | 488d 8424 
  0x00000249ecd067c4: 9000 0000 | 4883 3800 | 0f84 7900 | 0000 4c8b | 1141 f6c2 | 020f 8462 | 0000 0049 | 83ba 8600 
  0x00000249ecd067e4: 0000 0074 | 0949 ff8a | 8600 0000 | eb4b 498b | 8296 0000 | 0049 0b82 | 8e00 0000 | 750a 49c7 
  0x00000249ecd06804: 423e 0000 | 0000 eb3d | 4983 ba9e | 0000 0000 | 7422 4833 | c049 c742 | 3e00 0000 | 00f0 8304 
  0x00000249ecd06824: 2400 4983 | ba9e 0000 | 0000 750d | f04d 0fb1 | 7a3e 7505 | 83c8 01eb | 0ca8 00eb | 084c 8b10 
  0x00000249ecd06844: f04c 0fb1 | 1175 0a49 | ff8f 4805 | 0000 4533 | d20f 85ca 

  0x00000249ecd06858: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd06858: 1400 0049 | bb48 d3cb | c000 0000 | 0048 8d84 | 2480 0000 | 0048 8338 | 000f 8479 | 0000 004d 
  0x00000249ecd06878: 8b13 41f6 | c202 0f84 | 6200 0000 | 4983 ba86 | 0000 0000 | 7409 49ff | 8a86 0000 | 00eb 4b49 
  0x00000249ecd06898: 8b82 9600 | 0000 490b | 828e 0000 | 0075 0a49 | c742 3e00 | 0000 00eb | 3d49 83ba | 9e00 0000 
  0x00000249ecd068b8: 0074 2248 | 33c0 49c7 | 423e 0000 | 0000 f083 | 0424 0049 | 83ba 9e00 | 0000 0075 | 0df0 4d0f 
  0x00000249ecd068d8: b17a 3e75 | 0583 c801 | eb0c a800 | eb08 4c8b | 10f0 4d0f | b113 750a | 49ff 8f48 | 0500 0045 
  0x00000249ecd068f8: 33d2 0f85 | f313 0000 | 8973 0c4c | 8bd3 49c1 | ea09 49bb | 0000 37f3 | 4902 0000 | 4788 2413 
  0x00000249ecd06918: 498b 97b8 | 0100 004c | 8bd2 4983 | c210 4d3b | 97c8 0100 | 000f 8385 | 1000 004d | 8997 b801 
  0x00000249ecd06938: 0000 410f | 0d8a c000 | 0000 48c7 | 0201 0000 

  0x00000249ecd06948: ;   {metadata('com/sun/jna/WString')}
  0x00000249ecd06948: 00c7 4208 | 48ad 2d01 | 4889 5c24 | 484c 8b54 | 2440 4d8b | da44 895a | 0c4c 8b44 | 2448 6690 
  0x00000249ecd06968: ;   {static_call}
  0x00000249ecd06968: c5f8 77e8 

  0x00000249ecd0696c: ; ImmutableOopMap {[40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic FindFirstFileW {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@102 (line 68)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0696c: f002 8eff 

  0x00000249ecd06970: ;   {other}
  0x00000249ecd06970: 0f1f 8400 | 600b 0003 | 4883 f8ff | 0f84 060a | 0000 488b | d066 6690 

  0x00000249ecd06988: ;   {static_call}
  0x00000249ecd06988: c5f8 77e8 

  0x00000249ecd0698c: ; ImmutableOopMap {[40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic FindClose {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@152 (line 77)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0698c: 5007 8eff 

  0x00000249ecd06990: ;   {other}
  0x00000249ecd06990: 0f1f 8400 | 800b 0004 | 4c8b 4424 | 484d 8b50 | 1849 83fa | 040f 8c11 | 1700 004d | 8b40 1048 
  0x00000249ecd069b0: 8b54 2448 | 4533 c948 | 8b6c 2438 

  0x00000249ecd069bc: ;   {static_call}
  0x00000249ecd069bc: c5f8 77e8 

  0x00000249ecd069c0: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic getInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Pointer::getInt@6 (line 580)
                      ; - com.sun.jna.Memory::getInt@10 (line 517)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@2 (line 207)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd069c0: 9ca3 8dff 

  0x00000249ecd069c4: ;   {other}
  0x00000249ecd069c4: 0f1f 8400 | b40b 0005 | 8944 2434 | 4c8b 5c24 | 484d 8b53 | 1849 83fa | 180f 8c25 | 1700 004d 
  0x00000249ecd069e4: 8b43 1041 | b914 0000 | 0048 8b54 | 2448 4889 | 6c24 2090 

  0x00000249ecd069f8: ;   {static_call}
  0x00000249ecd069f8: c5f8 77e8 

  0x00000249ecd069fc: ; ImmutableOopMap {[32]=Oop [40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic getInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Pointer::getInt@6 (line 580)
                      ; - com.sun.jna.Memory::getInt@10 (line 517)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readFILETIME@2 (line 244)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@9 (line 208)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd069fc: 60a3 8dff 

  0x00000249ecd06a00: ;   {other}
  0x00000249ecd06a00: 0f1f 8400 | f00b 0006 | 8944 2440 | 4c8b 4424 | 484d 8b50 | 1849 83fa | 1c0f 8c39 | 1700 004d 
  0x00000249ecd06a20: 8b40 1048 | 8b54 2448 | 41b9 1800 | 0000 488b | 6c24 2090 

  0x00000249ecd06a34: ;   {static_call}
  0x00000249ecd06a34: c5f8 77e8 

  0x00000249ecd06a38: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic getInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Pointer::getInt@6 (line 580)
                      ; - com.sun.jna.Memory::getInt@10 (line 517)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readFILETIME@10 (line 245)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@9 (line 208)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06a38: 24a3 8dff 

  0x00000249ecd06a3c: ;   {other}
  0x00000249ecd06a3c: 0f1f 8400 | 2c0c 0007 | 4c8b 5424 | 484d 8b52 | 184c 63d8 | 448b 4424 | 4049 c1e3 | 204d 0bd8 
  0x00000249ecd06a5c: 4d8b c349 | c1f8 3f48 | b84b 5986 | 38d6 c56d | 3449 f7eb | 48c1 fa0b | 492b d049 | bb00 9849 
  0x00000249ecd06a7c: cf68 f5ff | ff49 03d3 | 4889 5424 | 4049 83fa | 200f 8c15 | 1700 004c | 8b54 2448 | 4d8b 4210 
  0x00000249ecd06a9c: 488b 5424 | 4841 b91c | 0000 0090 

  0x00000249ecd06aa8: ;   {static_call}
  0x00000249ecd06aa8: c5f8 77e8 

  0x00000249ecd06aac: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic getInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Pointer::getInt@6 (line 580)
                      ; - com.sun.jna.Memory::getInt@10 (line 517)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORD@2 (line 240)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@17 (line 209)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06aac: b0a2 8dff 

  0x00000249ecd06ab0: ;   {other}
  0x00000249ecd06ab0: 0f1f 8400 | a00c 0008 | 4c8b 5424 | 484d 8b52 | 184c 63d8 | 458b c34c | 8944 2450 | 4c89 5c24 
  0x00000249ecd06ad0: 2049 83fa | 240f 8c29 | 1700 004c | 8b54 2448 | 4d8b 4210 | 488b 5424 | 4841 b920 | 0000 0090 
  0x00000249ecd06af0: ;   {static_call}
  0x00000249ecd06af0: c5f8 77e8 

  0x00000249ecd06af4: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic getInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Pointer::getInt@6 (line 580)
                      ; - com.sun.jna.Memory::getInt@10 (line 517)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORD@2 (line 240)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@25 (line 210)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06af4: 68a2 8dff 

  0x00000249ecd06af8: ;   {other}
  0x00000249ecd06af8: 0f1f 8400 | e80c 0009 | 4c8b 5424 | 484d 8b52 | 1844 8bd8 | 4c89 5c24 | 5849 83fa | 280f 8c49 
  0x00000249ecd06b18: 1700 004c | 8b54 2448 | 4d8b 4210 | 488b 5424 | 4841 b924 | 0000 0090 

  0x00000249ecd06b30: ;   {static_call}
  0x00000249ecd06b30: c5f8 77e8 

  0x00000249ecd06b34: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic getInt {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Pointer::getInt@6 (line 580)
                      ; - com.sun.jna.Memory::getInt@10 (line 517)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@33 (line 211)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06b34: 28a2 8dff 

  0x00000249ecd06b38: ;   {other}
  0x00000249ecd06b38: 0f1f 8400 | 280d 000a | 8944 2460 | 4c8b 5424 | 484d 8b52 | 1841 b92c | 0000 0049 | 83fa 2c0f 
  0x00000249ecd06b58: 8c77 1700 | 004c 8b54 | 2448 4d8b | 4210 488b | 5424 4890 

  0x00000249ecd06b6c: ;   {static_call}
  0x00000249ecd06b6c: c5f8 77e8 

  0x00000249ecd06b70: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokestatic getWideString {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.Pointer::getWideString@6 (line 659)
                      ; - com.sun.jna.Memory::getWideString@8 (line 609)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@42 (line 212)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06b70: 6c09 8eff 

  0x00000249ecd06b74: ;   {other}
  0x00000249ecd06b74: 0f1f 8400 | 640d 000b | 4c8b 5424 | 2049 c1e2 | 204c 0354 | 2458 4885 | c00f 84b1 | 1700 0048 
  0x00000249ecd06b94: 8bf5 448b | 460c 458b | c841 81c9 | 0000 0100 | 4489 4e0c | 448b 5c24 | 3441 f6c3 | 1074 0d45 
  0x00000249ecd06bb4: 8bc1 4183 | c801 4489 | 460c eb0f | 4183 e0fe | 4181 c800 | 0001 0044 | 8946 0c41 | f6c3 2074 
  0x00000249ecd06bd4: 0a41 83c8 | 0844 8946 | 0ceb 0841 | 83e0 f744 | 8946 0c4c | 8956 184c | 8b54 2440 | 4c89 5610 
  0x00000249ecd06bf4: ;   {oop(a 'java/lang/Class'{0x00000000c13d8b90} = 'org/eclipse/core/internal/filesystem/local/LocalFileNativesManager')}
  0x00000249ecd06bf4: 49ba 908b | 3dc1 0000 | 0000 458b | 4a74 4c8b | c044 8946 | 244c 8bd6 | 49c1 ea09 | 49b8 0000 
  0x00000249ecd06c14: 37f3 4902 | 0000 4788 | 2410 458b | 4108 418b | eb83 e501 

  0x00000249ecd06c28: ;   {metadata('org/eclipse/core/internal/filesystem/local/Win32Handler')}
  0x00000249ecd06c28: 4181 f820 | 445c 010f | 8563 1100 | 0044 8b56 | 0c85 ed74 | 0a41 83ca | 0244 8956 | 0ceb 0841 
  0x00000249ecd06c48: 83e2 fd44 | 8956 0c41 | f6c3 0274 | 0a41 83ca | 1044 8956 | 0ceb 0841 | 83e2 ef44 | 8956 0c41 
  0x00000249ecd06c68: 81e3 0004 | 0000 4585 | db0f 8505 | 1700 004c | 8b54 2448 | 4d89 6210 | 418b 6a0c 

  0x00000249ecd06c84: ; implicit exception: dispatches to 0x00000249ecd08828
  0x00000249ecd06c84: 448b 5d08 

  0x00000249ecd06c88: ;   {metadata('com/sun/jna/internal/Cleaner$CleanerRef')}
  0x00000249ecd06c88: 4181 fb28 | d02d 010f | 8553 1100 | 0048 8bfd | 448b 5f1c | 4585 db0f | 84a8 1000 | 0049 8beb 
  0x00000249ecd06ca8: 488d 9c24 | 8000 0000 | 488b 4500 | a802 0f85 | 2400 0000 | 4883 c801 | 4889 03f0 | 480f b15d 
  0x00000249ecd06cc8: 000f 8437 | 0000 0048 | 2bc4 4825 | 07f0 ffff | 4889 03e9 | 2400 0000 | 4c8b d048 | 33c0 f04d 
  0x00000249ecd06ce8: 0fb1 7a3e | 48c7 0303 | 0000 0074 | 114c 3bf8 | 7515 49ff | 8286 0000 | 0048 33c0 | 7509 49ff 
  0x00000249ecd06d08: 8748 0500 | 0033 c00f | 85f3 1000 | 0045 8b43 | 0c45 85c0 | 0f84 4a10 | 0000 498b | d048 8d9c 
  0x00000249ecd06d28: 2488 0000 | 0048 8b02 | a802 0f85 | 2300 0000 | 4883 c801 | 4889 03f0 | 480f b11a | 0f84 3700 
  0x00000249ecd06d48: 0000 482b | c448 2507 | f0ff ff48 | 8903 e924 | 0000 004c | 8bd0 4833 | c0f0 4d0f | b17a 3e48 
  0x00000249ecd06d68: c703 0300 | 0000 7411 | 4c3b f875 | 1549 ff82 | 8600 0000 | 4833 c075 | 0949 ff87 

  0x00000249ecd06d84: ;   {no_reloc}
  0x00000249ecd06d84: 4805 0000 | 33c0 0f85 | bf10 0000 | 418b 5b14 | 448b 5728 | 448b 4f24 | 488b cb48 | 3bf9 0f85 
  0x00000249ecd06da4: f30a 0000 | 4589 5314 | 488b cd48 | c1e9 0948 | bb00 0037 | f349 0200 | 0044 8824 | 0bb9 0100 
  0x00000249ecd06dc4: 0000 4585 | c90f 85d3 | 0a00 0045 | 894a 2444 | 8967 2844 | 8967 2449 | c1ea 0949 | bb00 0037 
  0x00000249ecd06de4: f349 0200 | 0047 8824 | 1348 8d84 | 2488 0000 | 0048 8338 | 000f 8479 | 0000 004c | 8b12 41f6 
  0x00000249ecd06e04: c202 0f84 | 6200 0000 | 4983 ba86 | 0000 0000 | 7409 49ff | 8a86 0000 | 00eb 4b49 | 8b82 9600 
  0x00000249ecd06e24: 0000 490b | 828e 0000 | 0075 0a49 | c742 3e00 | 0000 00eb | 3d49 83ba | 9e00 0000 | 0074 2248 
  0x00000249ecd06e44: 33c0 49c7 | 423e 0000 | 0000 f083 | 0424 0049 | 83ba 9e00 | 0000 0075 | 0df0 4d0f | b17a 3e75 
  0x00000249ecd06e64: 0583 c801 | eb0c a800 | eb08 4c8b | 10f0 4c0f | b112 750a | 49ff 8f48 | 0500 0045 | 33d2 0f85 
  0x00000249ecd06e84: ;   {no_reloc}
  0x00000249ecd06e84: 1d10 0000 | 488d 8424 | 8000 0000 | 4883 3800 | 0f84 7b00 | 0000 4c8b | 5500 41f6 | c202 0f84 
  0x00000249ecd06ea4: 6200 0000 | 4983 ba86 | 0000 0000 | 7409 49ff | 8a86 0000 | 00eb 4b49 | 8b82 9600 | 0000 490b 
  0x00000249ecd06ec4: 828e 0000 | 0075 0a49 | c742 3e00 | 0000 00eb | 3e49 83ba | 9e00 0000 | 0074 2248 | 33c0 49c7 
  0x00000249ecd06ee4: 423e 0000 | 0000 f083 | 0424 0049 | 83ba 9e00 | 0000 0075 | 0df0 4d0f | b17a 3e75 | 0583 c801 
  0x00000249ecd06f04: eb0d a800 | eb09 4c8b | 10f0 4c0f | b155 0075 | 0a49 ff8f | 4805 0000 | 4533 d20f | 85ab 0f00 
  0x00000249ecd06f24: 008b 6f20 

  0x00000249ecd06f28: ; implicit exception: dispatches to 0x00000249ecd08808
  0x00000249ecd06f28: 448b 5508 

  0x00000249ecd06f2c: ;   {metadata('com/sun/jna/Memory$MemoryDisposer')}
  0x00000249ecd06f2c: 4181 fa50 | f52d 010f | 85c2 0f00 | 004c 8bdd | 488d 9c24 | 8000 0000 | 498b 03a8 | 020f 8523 
  0x00000249ecd06f4c: 0000 0048 | 83c8 0148 | 8903 f049 | 0fb1 1b0f | 8437 0000 | 0048 2bc4 | 4825 07f0 | ffff 4889 
  0x00000249ecd06f6c: 03e9 2400 | 0000 4c8b | d048 33c0 | f04d 0fb1 | 7a3e 48c7 | 0303 0000 | 0074 114c | 3bf8 7515 
  0x00000249ecd06f8c: 49ff 8286 | 0000 0048 | 33c0 7509 | 49ff 8748 | 0500 0033 | c00f 8579 | 0f00 0049 | 8b53 1048 
  0x00000249ecd06fac: 85d2 0f84 | 0814 0000 | 498b eb48 | 8974 2438 

  0x00000249ecd06fbc: ;   {static_call}
  0x00000249ecd06fbc: c5f8 77e8 

  0x00000249ecd06fc0: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop }
                      ;*invokestatic free {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Memory::free@7 (line 748)
                      ; - com.sun.jna.Memory$MemoryDisposer::run@4 (line 794)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd06fc0: 9cf5 8dff 

  0x00000249ecd06fc4: ;   {other}
  0x00000249ecd06fc4: 0f1f 8400 | b411 000c | 4c8b cd49 | 8b69 1048 | 83fd 807c | 3648 83fd | 7f7f 3044 | 8bd5 418d 
  0x00000249ecd06fe4: aa80 0000 | 0081 fd00 | 0100 000f | 8353 1400 | 004d 63d2 

  0x00000249ecd06ff8: ;   {oop(a 'java/lang/Long'[256] {0x00000000c0003e88})}
  0x00000249ecd06ff8: 49bb 883e | 00c0 0000 | 0000 478b | 9c93 1002 | 0000 4d8b | c3eb 514d | 8b87 b801 | 0000 4d8b 
  0x00000249ecd07018: d049 83c2 | 184d 3b97 | c801 0000 | 0f83 ed09 | 0000 4d89 | 97b8 0100 | 0041 0f0d | 8ac0 0000 
  0x00000249ecd07038: 0049 c700 | 0100 0000 | 4d8b d049 

  0x00000249ecd07044: ;   {metadata('java/lang/Long')}
  0x00000249ecd07044: 83c2 1041 | c740 0890 | 5d03 0045 | 8960 0cc5 | fdef c0c4 | c179 d602 | 4989 6810 

  0x00000249ecd07060: ;   {oop(a 'java/util/concurrent/ConcurrentHashMap'{0x00000000c0b6efd8})}
  0x00000249ecd07060: 498b e948 | bad8 efb6 | c000 0000 | 0045 33c9 | 33ff 6690 

  0x00000249ecd07074: ;   {optimized virtual_call}
  0x00000249ecd07074: c5f8 77e8 

  0x00000249ecd07078: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop }
                      ;*invokevirtual replaceNode {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.concurrent.ConcurrentHashMap::remove@4
                      ; - com.sun.jna.Memory$MemoryDisposer::run@17 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07078: 2458 7f00 

  0x00000249ecd0707c: ;   {other}
  0x00000249ecd0707c: 0f1f 8400 | 6c12 000d | 4c89 6510 | 488d 8424 | 8000 0000 | 4883 3800 | 0f84 7b00 | 0000 4c8b 
  0x00000249ecd0709c: 5500 41f6 | c202 0f84 | 6200 0000 | 4983 ba86 | 0000 0000 | 7409 49ff | 8a86 0000 | 00eb 4b49 
  0x00000249ecd070bc: 8b82 9600 | 0000 490b | 828e 0000 | 0075 0a49 | c742 3e00 | 0000 00eb | 3e49 83ba | 9e00 0000 
  0x00000249ecd070dc: 0074 2248 | 33c0 49c7 | 423e 0000 | 0000 f083 | 0424 0049 | 83ba 9e00 | 0000 0075 | 0df0 4d0f 
  0x00000249ecd070fc: b17a 3e75 | 0583 c801 | eb0d a800 | eb09 4c8b | 10f0 4c0f | b155 0075 | 0a49 ff8f | 4805 0000 
  0x00000249ecd0711c: 4533 d20f | 852c 0e00 | 004c 8b54 | 2438 458b | 5224 458b | 5a14 458b | 530c 4585 | d276 1e48 
  0x00000249ecd0713c: 8b44 2438 | c5f8 7748 | 81c4 a000 

  0x00000249ecd07148: ;   {poll_return}
  0x00000249ecd07148: 0000 5d49 | 3ba7 4804 | 0000 0f87 | 8e1c 0000 | c34c 8b54 | 2428 458b | 520c 4585 | d20f 840d 
  0x00000249ecd07168: 1300 0049 | 8bd2 6690 

  0x00000249ecd07170: ;   {optimized virtual_call}
  0x00000249ecd07170: c5f8 77e8 

  0x00000249ecd07174: ; ImmutableOopMap {[56]=Oop }
                      ;*invokevirtual getName {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@22 (line 231)
  0x00000249ecd07174: 2878 c3ff 

  0x00000249ecd07178: ;   {other}
  0x00000249ecd07178: 0f1f 8400 | 6813 000e | 4889 4424 | 2049 8baf | b801 0000 | 4c8b d549 | 83c2 184d | 3b97 c801 
  0x00000249ecd07198: 0000 0f83 | c309 0000 | 4d89 97b8 | 0100 0041 | 0f0d 8ac0 | 0000 0048 | c745 0001 | 0000 004c 
  0x00000249ecd071b8: 8bd5 4983 

  0x00000249ecd071bc: ;   {metadata('java/lang/String')}
  0x00000249ecd071bc: c210 c745 | 0840 e700 | 0044 8965 | 0cc5 fdef | c0c4 c179 | d602 4c8b | 5424 204d | 85d2 0f84 
  0x00000249ecd071dc: b012 0000 | 498b d290 

  0x00000249ecd071e4: ;   {optimized virtual_call}
  0x00000249ecd071e4: c5f8 77e8 

  0x00000249ecd071e8: ; ImmutableOopMap {rbp=Oop [56]=Oop }
                      ;*invokevirtual toCharArray {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@34 (line 233)
  0x00000249ecd071e8: 7486 8fff 

  0x00000249ecd071ec: ;   {other}
  0x00000249ecd071ec: 0f1f 8400 | dc13 000f | 4c8b c844 | 8b58 0c4c | 8bed 4585 | db0f 863e | 0800 004d | 63f3 4181 
  0x00000249ecd0720c: fb00 0010 | 000f 8771 | 0900 004d | 8b87 b801 | 0000 4d8d | 5617 4983 | e2f8 498b | c849 03ca 
  0x00000249ecd0722c: 493b 8fc8 | 0100 000f | 834f 0900 | 0049 898f | b801 0000 | 0f0d 89c0 | 0000 0049 | c700 0100 
  0x00000249ecd0724c: 0000 0f0d | 8900 0100 

  0x00000249ecd07254: ;   {metadata({type array byte})}
  0x00000249ecd07254: 0041 c740 | 0868 2200 | 0045 8958 | 0c0f 0d89 | 4001 0000 | 0f0d 8980 | 0100 004d | 8bd1 4983 
  0x00000249ecd07274: c210 498b | f848 83c7 | 1049 8bf2 | 418b d352 | 8bc2 b900 | ff00 ff83 | e2f0 83e0 | 0f85 d20f 
  0x00000249ecd07294: 8445 0000 | 00c5 f96e | d1c5 f970 | d200 c5e1 | efdb 488d | 3456 488d | 3c17 48f7 | dac5 fa6f 
  0x00000249ecd072b4: 0456 c5e1 | ebd8 c5fa | 6f4c 5610 | c5e1 ebd9 | c4e2 7917 | da0f 856a | 0000 00c5 | f967 c1c5 
  0x00000249ecd072d4: fa7f 0417 | 4883 c210 | 75d3 8bd0 | 83e2 f883 | e007 85d2 | 7428 c5f9 | 6ed1 c5f9 | 70d2 00c5 
  0x00000249ecd072f4: f1ef c9c5 | fa6f 06c4 | e279 17c2 | 7537 c5f9 | 67c1 c5f9 | d607 4883 | c610 4883 | c708 8bd0 
  0x00000249ecd07314: 85d2 741e | 488d 3456 | 488d 3c17 | 48f7 da0f | b704 56a9 | 00ff 0000 | 750b 8804 | 1748 ffc2 
  0x00000249ecd07334: 75ed 58eb | 0633 c048 | 83c4 0841 | 3bc3 0f85 | 4c07 0000 | 4488 6510 | 4c8b d54d | 8bd8 4489 
  0x00000249ecd07354: ;   {no_reloc}
  0x00000249ecd07354: 5d14 49c1 | ea09 49bb | 0000 37f3 | 4902 0000 | 4788 2413 | 4c8b 5424 | 3845 896a | 2449 c1ea 
  0x00000249ecd07374: 0949 bb00 | 0037 f349 | 0200 0047 | 8824 13e9 | b3fd ffff 

  0x00000249ecd07388: ;   {static_call}
  0x00000249ecd07388: c5f8 77e8 

  0x00000249ecd0738c: ; ImmutableOopMap {[40]=Oop [56]=Oop [72]=Oop }
                      ;*invokestatic getLastError {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@116 (line 70)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0738c: d058 d3ff 

  0x00000249ecd07390: ;   {other}
  0x00000249ecd07390: 0f1f 8400 | 8015 0010 | 83f8 020f | 85e4 0400 | 0048 8b74 | 2438 4c8b | 5424 484d | 8962 1041 
  0x00000249ecd073b0: 8b6a 0c44 

  0x00000249ecd073b4: ;   {metadata('com/sun/jna/internal/Cleaner$CleanerRef')}
  0x00000249ecd073b4: 8b5d 0841 | 81fb 28d0 | 2d01 0f85 | 3411 0000 | 488b fd44 | 8b57 1c45 | 85d2 0f84 | d410 0000 
  0x00000249ecd073d4: 498b ea48 | 8d9c 2480 | 0000 0048 | 8b45 00a8 | 020f 8524 | 0000 0048 | 83c8 0148 | 8903 f048 
  0x00000249ecd073f4: 0fb1 5d00 | 0f84 3700 | 0000 482b | c448 2507 | f0ff ff48 | 8903 e924 | 0000 004c | 8bd8 4833 
  0x00000249ecd07414: c0f0 4d0f | b17b 3e48 | c703 0300 | 0000 7411 | 4c3b f875 | 1549 ff83 | 8600 0000 | 4833 c075 
  0x00000249ecd07434: 0949 ff87 | 4805 0000 | 33c0 0f85 | d810 0000 | 458b 5a0c | 4585 db0f | 847b 1000 | 0049 8bd3 
  0x00000249ecd07454: 488d 9c24 | 8800 0000 | 488b 02a8 | 020f 8523 | 0000 0048 | 83c8 0148 | 8903 f048 | 0fb1 1a0f 
  0x00000249ecd07474: 8437 0000 | 0048 2bc4 | 4825 07f0 | ffff 4889 | 03e9 2400 | 0000 4c8b | c048 33c0 | f04d 0fb1 
  0x00000249ecd07494: 783e 48c7 | 0303 0000 | 0074 114c | 3bf8 7515 | 49ff 8086 | 0000 0048 | 33c0 7509 | 49ff 8748 
  0x00000249ecd074b4: ;   {no_reloc}
  0x00000249ecd074b4: 0500 0033 | c00f 85a4 | 1000 0041 | 8b5a 1444 | 8b4f 288b | 4f24 4c8b | c349 3bf8 | 0f85 9405 
  0x00000249ecd074d4: 0000 4589 | 4a14 4c8b | c549 c1e8 | 0948 bb00 | 0037 f349 | 0200 0046 | 8824 0341 | b801 0000 
  0x00000249ecd074f4: 0085 c90f | 8575 0500 | 0041 8949 | 2444 8967 | 2844 8967 | 244d 8bd1 | 49c1 ea09 | 49bb 0000 
  0x00000249ecd07514: 37f3 4902 | 0000 4788 | 2413 488d | 8424 8800 | 0000 4883 | 3800 0f84 | 7900 0000 | 4c8b 1241 
  0x00000249ecd07534: f6c2 020f | 8462 0000 | 0049 83ba | 8600 0000 | 0074 0949 | ff8a 8600 | 0000 eb4b | 498b 8296 
  0x00000249ecd07554: 0000 0049 | 0b82 8e00 | 0000 750a | 49c7 423e | 0000 0000 | eb3d 4983 | ba9e 0000 | 0000 7422 
  0x00000249ecd07574: 4833 c049 | c742 3e00 | 0000 00f0 | 8304 2400 | 4983 ba9e | 0000 0000 | 750d f04d | 0fb1 7a3e 
  0x00000249ecd07594: 7505 83c8 | 01eb 0ca8 | 00eb 084c | 8b10 f04c | 0fb1 1275 | 0a49 ff8f | 4805 0000 | 4533 d20f 
  0x00000249ecd075b4: ;   {no_reloc}
  0x00000249ecd075b4: 852b 1000 | 0048 8d84 | 2480 0000 | 0048 8338 | 000f 847b | 0000 004c | 8b55 0041 | f6c2 020f 
  0x00000249ecd075d4: 8462 0000 | 0049 83ba | 8600 0000 | 0074 0949 | ff8a 8600 | 0000 eb4b | 498b 8296 | 0000 0049 
  0x00000249ecd075f4: 0b82 8e00 | 0000 750a | 49c7 423e | 0000 0000 | eb3e 4983 | ba9e 0000 | 0000 7422 | 4833 c049 
  0x00000249ecd07614: c742 3e00 | 0000 00f0 | 8304 2400 | 4983 ba9e | 0000 0000 | 750d f04d | 0fb1 7a3e | 7505 83c8 
  0x00000249ecd07634: 01eb 0da8 | 00eb 094c | 8b10 f04c | 0fb1 5500 | 750a 49ff | 8f48 0500 | 0045 33d2 | 0f85 630f 
  0x00000249ecd07654: 0000 8b6f | 2044 8b55 

  0x00000249ecd0765c: ;   {metadata('com/sun/jna/Memory$MemoryDisposer')}
  0x00000249ecd0765c: 0841 81fa | 50f5 2d01 | 0f85 a50f | 0000 4c8b | dd48 8d9c | 2480 0000 | 0049 8b03 | a802 0f85 
  0x00000249ecd0767c: 2300 0000 | 4883 c801 | 4889 03f0 | 490f b11b | 0f84 3700 | 0000 482b | c448 2507 | f0ff ff48 
  0x00000249ecd0769c: 8903 e924 | 0000 004c | 8bd0 4833 | c0f0 4d0f | b17a 3e48 | c703 0300 | 0000 7411 | 4c3b f875 
  0x00000249ecd076bc: 1549 ff82 | 8600 0000 | 4833 c075 | 0949 ff87 | 4805 0000 | 33c0 0f85 | 5c0f 0000 | 498b 5310 
  0x00000249ecd076dc: 4885 d20f | 8480 0f00 | 0049 8beb | 4889 7424 | 3866 6690 

  0x00000249ecd076f0: ;   {static_call}
  0x00000249ecd076f0: c5f8 77e8 

  0x00000249ecd076f4: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop }
                      ;*invokestatic free {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Memory::free@7 (line 748)
                      ; - com.sun.jna.Memory$MemoryDisposer::run@4 (line 794)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd076f4: 68ee 8dff 

  0x00000249ecd076f8: ;   {other}
  0x00000249ecd076f8: 0f1f 8400 | e818 0011 | 4c8b cd49 | 8b69 1048 | 83fd 807c | 3648 83fd | 7f7f 3044 | 8bd5 418d 
  0x00000249ecd07718: aa80 0000 | 0081 fd00 | 0100 000f | 837b 0f00 | 004d 63d2 

  0x00000249ecd0772c: ;   {oop(a 'java/lang/Long'[256] {0x00000000c0003e88})}
  0x00000249ecd0772c: 49bb 883e | 00c0 0000 | 0000 478b | 9c93 1002 | 0000 4d8b | c3eb 514d | 8b87 b801 | 0000 4d8b 
  0x00000249ecd0774c: d049 83c2 | 184d 3b97 | c801 0000 | 0f83 7a04 | 0000 4d89 | 97b8 0100 | 0041 0f0d | 8ac0 0000 
  0x00000249ecd0776c: 0049 c700 | 0100 0000 | 4d8b d049 

  0x00000249ecd07778: ;   {metadata('java/lang/Long')}
  0x00000249ecd07778: 83c2 1041 | c740 0890 | 5d03 0045 | 8960 0cc5 | fdef c0c4 | c179 d602 | 4989 6810 

  0x00000249ecd07794: ;   {oop(a 'java/util/concurrent/ConcurrentHashMap'{0x00000000c0b6efd8})}
  0x00000249ecd07794: 498b e948 | bad8 efb6 | c000 0000 | 0045 33c9 | 33ff 6690 

  0x00000249ecd077a8: ;   {optimized virtual_call}
  0x00000249ecd077a8: c5f8 77e8 

  0x00000249ecd077ac: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop }
                      ;*invokevirtual replaceNode {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.concurrent.ConcurrentHashMap::remove@4
                      ; - com.sun.jna.Memory$MemoryDisposer::run@17 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd077ac: f050 7f00 

  0x00000249ecd077b0: ;   {other}
  0x00000249ecd077b0: 0f1f 8400 | a019 0012 | 4c8b dd4d | 8963 1048 | 8d84 2480 | 0000 0048 | 8338 000f | 8479 0000 
  0x00000249ecd077d0: 004d 8b13 | 41f6 c202 | 0f84 6200 | 0000 4983 | ba86 0000 | 0000 7409 | 49ff 8a86 | 0000 00eb 
  0x00000249ecd077f0: 4b49 8b82 | 9600 0000 | 490b 828e | 0000 0075 | 0a49 c742 | 3e00 0000 | 00eb 3d49 | 83ba 9e00 
  0x00000249ecd07810: 0000 0074 | 2248 33c0 | 49c7 423e | 0000 0000 | f083 0424 | 0049 83ba | 9e00 0000 | 0075 0df0 
  0x00000249ecd07830: 4d0f b17a | 3e75 0583 | c801 eb0c | a800 eb08 | 4c8b 10f0 | 4d0f b113 | 750a 49ff | 8f48 0500 
  0x00000249ecd07850: 0045 33d2 | 0f84 cbf8 | ffff 498b | cb48 8d94 | 2480 0000 | 004d 8bc7 | c5f8 7749 | bae0 3996 
  0x00000249ecd07870: f1fb 7f00 | 0041 ffd2 

  0x00000249ecd07878: ;   {other}
  0x00000249ecd07878: 0f1f 8400 | 0000 0000 | e9a0 f8ff | ff83 f803 | 743a 488b | 7424 38c7 | 4620 0500 | 0000 e90b 
  0x00000249ecd07898: fbff ff33 | c9e9 24f5 | ffff 4589 | 5128 448b | 5728 498b | d948 c1eb | 0948 b800 | 0037 f349 
  0x00000249ecd078b8: 0200 0044 | 8824 18e9 | 0bf5 ffff | 488b 7424 | 38e9 d8fa | ffff 4889 | 4424 f88b | 4424 3089 
  0x00000249ecd078d8: 4424 2048 | 8b44 24f8 

  0x00000249ecd078e0: ;   {metadata('org/eclipse/core/filesystem/provider/FileInfo')}
  0x00000249ecd078e0: 488b ea48 | ba40 485c | 8149 0200 | 0066 6690 

  0x00000249ecd078f0: ;   {runtime_call _new_instance_Java}
  0x00000249ecd078f0: c5f8 77e8 

  0x00000249ecd078f4: ; ImmutableOopMap {rbp=Oop [32]=NarrowOop [40]=Oop [48]=NarrowOop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@0 (line 51)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd078f4: 0821 0bff 

  0x00000249ecd078f8: ;   {other}
  0x00000249ecd078f8: 0f1f 8400 | e81a 0013 | e963 eaff | ff4c 894c 

  0x00000249ecd07908: ;   {metadata('com/sun/jna/Memory')}
  0x00000249ecd07908: 2440 48ba | 5090 2d81 | 4902 0000 | 488b 6c24 | 2866 6690 

  0x00000249ecd0791c: ;   {runtime_call _new_instance_Java}
  0x00000249ecd0791c: c5f8 77e8 

  0x00000249ecd07920: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@80 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07920: dc20 0bff 

  0x00000249ecd07924: ;   {other}
  0x00000249ecd07924: 0f1f 8400 | 141b 0014 | e9d2 eaff | ff48 895c | 2420 488b 

  0x00000249ecd07938: ;   {metadata('java/lang/ref/WeakReference')}
  0x00000249ecd07938: e948 ba80 | da03 8049 | 0200 0090 

  0x00000249ecd07944: ;   {runtime_call _new_instance_Java}
  0x00000249ecd07944: c5f8 77e8 

  0x00000249ecd07948: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.Memory::<init>@84 (line 121)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07948: b420 0bff 

  0x00000249ecd0794c: ;   {other}
  0x00000249ecd0794c: 0f1f 8400 | 3c1b 0015 | 4c8b c848 | 8bcd e9b0 | ebff ff4c | 894c 2448 | 4c89 5424 

  0x00000249ecd07968: ;   {metadata('com/sun/jna/internal/Cleaner$CleanerRef')}
  0x00000249ecd07968: 2048 ba28 | d02d 8149 | 0200 0090 

  0x00000249ecd07974: ;   {runtime_call _new_instance_Java}
  0x00000249ecd07974: c5f8 77e8 

  0x00000249ecd07978: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.internal.Cleaner::register@1 (line 58)
                      ; - com.sun.jna.Memory::<init>@114 (line 122)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07978: 8420 0bff 

  0x00000249ecd0797c: ;   {other}
  0x00000249ecd0797c: 0f1f 8400 | 6c1b 0016 | 4c8b 5424 | 204c 8b4c | 2448 4c8b | d8e9 d7ec 

  0x00000249ecd07994: ;   {metadata('com/sun/jna/Memory$MemoryDisposer')}
  0x00000249ecd07994: ffff 48ba | 50f5 2d81 | 4902 0000 

  0x00000249ecd079a0: ;   {runtime_call _new_instance_Java}
  0x00000249ecd079a0: c5f8 77e8 

  0x00000249ecd079a4: ; ImmutableOopMap {rbp=NarrowOop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.Memory::<init>@103 (line 122)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd079a4: 5820 0bff 

  0x00000249ecd079a8: ;   {other}
  0x00000249ecd079a8: 0f1f 8400 | 981b 0017 | 4c8b c8e9 | e9eb ffff | 4889 5c24 

  0x00000249ecd079bc: ;   {metadata('com/sun/jna/WString')}
  0x00000249ecd079bc: 4848 ba48 | ad2d 8149 | 0200 0048 | 8b6c 2428 

  0x00000249ecd079cc: ;   {runtime_call _new_instance_Java}
  0x00000249ecd079cc: c5f8 77e8 

  0x00000249ecd079d0: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@92 (line 68)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd079d0: 2c20 0bff 

  0x00000249ecd079d4: ;   {other}
  0x00000249ecd079d4: 0f1f 8400 | c41b 0018 | 488b d0e9 | 71ef ffff | 4889 4424 

  0x00000249ecd079e8: ;   {metadata('java/lang/Long')}
  0x00000249ecd079e8: 2048 ba90 | 5d03 8049 | 0200 0090 

  0x00000249ecd079f4: ;   {runtime_call _new_instance_Java}
  0x00000249ecd079f4: c5f8 77e8 

  0x00000249ecd079f8: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.lang.Long::valueOf@31
                      ; - com.sun.jna.Memory::<init>@81 (line 121)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd079f8: 0420 0bff 

  0x00000249ecd079fc: ;   {other}
  0x00000249ecd079fc: 0f1f 8400 | ec1b 0019 | 4c8b d048 | 8bcd 488b | 4424 2049 | 8bda e9a5 | eaff ff4c | 894c 2420 
  0x00000249ecd07a1c: ;   {metadata('java/lang/Long')}
  0x00000249ecd07a1c: 48ba 905d | 0380 4902 | 0000 6690 

  0x00000249ecd07a28: ;   {runtime_call _new_instance_Java}
  0x00000249ecd07a28: c5f8 77e8 

  0x00000249ecd07a2c: ; ImmutableOopMap {[32]=Oop [40]=Oop [56]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.lang.Long::valueOf@31
                      ; - com.sun.jna.Memory$MemoryDisposer::run@14 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07a2c: d01f 0bff 

  0x00000249ecd07a30: ;   {other}
  0x00000249ecd07a30: 0f1f 8400 | 201c 001a | 4c8b c04c | 8b4c 2420 | e917 f6ff | ff44 8865 

  0x00000249ecd07a48: ;   {oop([B{0x00000000c0697310})}
  0x00000249ecd07a48: 10c7 4514 | 1073 69c0 | 4c8b d549 | c1ea 0949 | bb00 0037 | f349 0200 | 0047 8824 | 13e9 fef8 
  0x00000249ecd07a68: ffff 4533 | c0e9 83fa | ffff 4489 | 4928 448b | 4f28 488b | d948 c1eb | 0948 b800 | 0037 f349 
  0x00000249ecd07a88: 0200 0044 | 8824 18e9 | 69fa ffff | c645 1001 | 4181 fbff | ffff 3f0f | 877a 0c00 | 0047 8d04 
  0x00000249ecd07aa8: 1b41 81f8 | 0000 1000 | 0f87 920c | 0000 4963 | c841 81f8 | 0000 1000 | 0f87 0e0c | 0000 498b 
  0x00000249ecd07ac8: 9fb8 0100 | 0048 83c1 | 1748 83e1 | f848 8bfb | 4803 f949 | 3bbf c801 | 0000 0f83 | ec0b 0000 
  0x00000249ecd07ae8: 4989 bfb8 | 0100 000f | 0d8f c000 | 0000 48c7 | 0301 0000 | 000f 0d8f | 0001 0000 

  0x00000249ecd07b04: ;   {metadata({type array byte})}
  0x00000249ecd07b04: c743 0868 | 2200 0044 | 8943 0c0f | 0d8f 4001 | 0000 0f0d | 8f80 0100 | 0048 8bd3 | 4883 c210 
  0x00000249ecd07b24: 498b ca4d | 8bc6 c5f8 | 7749 ba00 | 85ce eb49 | 0200 0041 

  0x00000249ecd07b38: ;   {other}
  0x00000249ecd07b38: ffd2 0f1f | 8400 0000 | 0000 4c8b | d344 8955 | 144c 8bd5 | 49c1 ea09 | 49bb 0000 | 37f3 4902 
  0x00000249ecd07b58: 0000 4788 | 2413 e905 

  0x00000249ecd07b60: ;   {metadata('java/lang/String')}
  0x00000249ecd07b60: f8ff ff48 | ba40 e700 | 8049 0200 | 0066 6690 

  0x00000249ecd07b70: ;   {runtime_call _new_instance_Java}
  0x00000249ecd07b70: c5f8 77e8 

  0x00000249ecd07b74: ; ImmutableOopMap {[32]=Oop [56]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@28 (line 233)
  0x00000249ecd07b74: 881e 0bff 

  0x00000249ecd07b78: ;   {other}
  0x00000249ecd07b78: 0f1f 8400 | 681d 001b | 488b e8e9 | 4af6 ffff | 4489 6c24 | 504c 8974 | 2440 4489 | 5c24 3048 
  0x00000249ecd07b98: 8944 2428 

  0x00000249ecd07b9c: ;   {metadata({type array byte})}
  0x00000249ecd07b9c: 48ba 6822 | 0080 4902 | 0000 458b | c366 6690 

  0x00000249ecd07bac: ;   {runtime_call _new_array_nozero_Java}
  0x00000249ecd07bac: c5f8 77e8 

  0x00000249ecd07bb0: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop [80]=NarrowOop }
                      ;*newarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.lang.StringUTF16::compress@1
                      ; - java.lang.String::<init>@36
                      ; - java.lang.String::<init>@6
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@37 (line 233)
  0x00000249ecd07bb0: 4c5d 0bff 

  0x00000249ecd07bb4: ;   {other}
  0x00000249ecd07bb4: 0f1f 8400 | a41d 001c | 4c8b 4c24 | 2844 8b5c | 2430 4c8b | 7424 404c | 8bc0 448b | 6c24 50e9 
  0x00000249ecd07bd4: 97f6 ffff | 4c89 4c24 

  0x00000249ecd07bdc: ;   {metadata('java/lang/Long')}
  0x00000249ecd07bdc: 2048 ba90 | 5d03 8049 | 0200 0048 | ff74 2438 | 488f 4424 | 3066 6690 

  0x00000249ecd07bf4: ;   {runtime_call _new_instance_Java}
  0x00000249ecd07bf4: c5f8 77e8 

  0x00000249ecd07bf8: ; ImmutableOopMap {[32]=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.lang.Long::valueOf@31
                      ; - com.sun.jna.Memory$MemoryDisposer::run@14 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07bf8: 041e 0bff 

  0x00000249ecd07bfc: ;   {other}
  0x00000249ecd07bfc: 0f1f 8400 | ec1d 001d | 4c8b c04c | 8b4c 2420 | e97f fbff | ffba f6ff | ffff 488b | 6c24 2844 
  0x00000249ecd07c1c: 8b44 2430 | 4489 4424 | 204c 8b44 | 2438 4c89 | 4424 284c | 8b44 2440 | 4c89 4424 | 304c 8954 
  0x00000249ecd07c3c: 2440 4c89 | 5c24 4890 

  0x00000249ecd07c44: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07c44: c5f8 77e8 

  0x00000249ecd07c48: ; ImmutableOopMap {rbp=Oop [32]=NarrowOop [40]=Oop [48]=Oop [64]=Oop [72]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::add@6 (line 62)
                      ; - com.sun.jna.internal.Cleaner::register@15 (line 58)
                      ; - com.sun.jna.Memory::<init>@114 (line 122)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07c48: b4f0 fffe 

  0x00000249ecd07c4c: ;   {other}
  0x00000249ecd07c4c: 0f1f 8400 | 3c1e 001e | ba37 ffff | ff48 8b6c | 2428 4489 | 5c24 2090 

  0x00000249ecd07c64: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07c64: c5f8 77e8 

  0x00000249ecd07c68: ; ImmutableOopMap {rbp=Oop [32]=NarrowOop [48]=NarrowOop }
                      ;*invokevirtual fetchFileInfo {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07c68: 94f0 fffe 

  0x00000249ecd07c6c: ;   {other}
  0x00000249ecd07c6c: 0f1f 8400 | 5c1e 001f | 4c89 4c24 | 484c 8954 

  0x00000249ecd07c7c: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd07c7c: 2420 48ba | 48d3 cbc0 | 0000 0000 | 4c8d 8424 | 8000 0000 

  0x00000249ecd07c90: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd07c90: c5f8 77e8 

  0x00000249ecd07c94: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [40]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*synchronization entry
                      ; - com.sun.jna.internal.Cleaner::register@-1 (line 58)
                      ; - com.sun.jna.Memory::<init>@114 (line 122)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07c94: 687a 0bff 

  0x00000249ecd07c98: ;   {other}
  0x00000249ecd07c98: 0f1f 8400 | 881e 0020 | 4c8b 5424 | 204c 8b4c | 2448 e974 | e9ff ff48 | 894c 2448 | 4489 4424 
  0x00000249ecd07cb8: 344c 895c | 2420 498b | ea48 8bd1 | 4c8d 8424 | 9000 0000 

  0x00000249ecd07ccc: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd07ccc: c5f8 77e8 

  0x00000249ecd07cd0: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=NarrowOop [52]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::add@6 (line 62)
                      ; - com.sun.jna.internal.Cleaner::register@15 (line 58)
                      ; - com.sun.jna.Memory::<init>@114 (line 122)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07cd0: 2c7a 0bff 

  0x00000249ecd07cd4: ;   {other}
  0x00000249ecd07cd4: 0f1f 8400 | c41e 0021 | 4c8b d54c | 8b5c 2420 | 448b 4424 | 3448 8b4c | 2448 e956 

  0x00000249ecd07cf0: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd07cf0: eaff ff48 | b948 d3cb | c000 0000 | 0048 8d94 | 2480 0000 | 004d 8bc7 | c5f8 7749 | bae0 3996 
  0x00000249ecd07d10: f1fb 7f00 | 0041 ffd2 

  0x00000249ecd07d18: ;   {other}
  0x00000249ecd07d18: 0f1f 8400 | 0000 0000 | e9db ebff | ff48 8d94 | 2490 0000 | 004d 8bc7 | c5f8 7749 | bae0 3996 
  0x00000249ecd07d38: f1fb 7f00 | 0041 ffd2 

  0x00000249ecd07d40: ;   {other}
  0x00000249ecd07d40: 0f1f 8400 | 0000 0000 | e90e ebff | ffba f6ff | ffff 488b | 6c24 2848 | 897c 2428 

  0x00000249ecd07d5c: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07d5c: c5f8 77e8 

  0x00000249ecd07d60: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop }
                      ;*invokespecial remove {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07d60: 9cef fffe 

  0x00000249ecd07d64: ;   {other}
  0x00000249ecd07d64: 0f1f 8400 | 541f 0022 | baf6 ffff | ff48 8b6c | 2428 4889 | 7424 2048 | 897c 2438 | 4489 5c24 
  0x00000249ecd07d84: 3466 6690 

  0x00000249ecd07d88: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07d88: c5f8 77e8 

  0x00000249ecd07d8c: ; ImmutableOopMap {rbp=Oop [32]=Oop [52]=NarrowOop [56]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::remove@6 (line 80)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07d8c: 70ef fffe 

  0x00000249ecd07d90: ;   {other}
  0x00000249ecd07d90: 0f1f 8400 | 801f 0023 | ba37 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 
  0x00000249ecd07db0: 8944 2428 | 488b 4424 | f84c 8b54 | 2448 4c89 | 5424 3848 | 8974 2440 | 4489 5c24 | 2c48 8974 
  0x00000249ecd07dd0: 2450 4489 | 4c24 4c90 

  0x00000249ecd07dd8: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07dd8: c5f8 77e8 

  0x00000249ecd07ddc: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [64]=Oop [76]=NarrowOop [80]=Oop }
                      ;*invokevirtual getSupportedAttributes {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::getSupportedAttributes@3 (line 79)
                      ; - org.eclipse.core.filesystem.provider.FileInfo::isAttributeSuported@0 (line 193)
                      ; - org.eclipse.core.filesystem.provider.FileInfo::setAttribute@7 (line 171)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@116 (line 222)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07ddc: 20ef fffe 

  0x00000249ecd07de0: ;   {other}
  0x00000249ecd07de0: 0f1f 8400 | d01f 0024 | bade ffff | ff48 ff74 | 2428 488f | 4424 2090 

  0x00000249ecd07df8: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07df8: c5f8 77e8 

  0x00000249ecd07dfc: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [56]=Oop }
                      ;*invokeinterface clean {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07dfc: 00ef fffe 

  0x00000249ecd07e00: ;   {other}
  0x00000249ecd07e00: 0f1f 8400 | f01f 0025 | 4889 6c24 | 3844 895c | 2430 4889 | 7c24 2048 | 8bee 488b | 5424 384c 
  0x00000249ecd07e20: 8d84 2480 | 0000 0090 

  0x00000249ecd07e28: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd07e28: c5f8 77e8 

  0x00000249ecd07e2c: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=NarrowOop [56]=Oop }
                      ;*synchronization entry
                      ; - com.sun.jna.internal.Cleaner::remove@-1 (line 80)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07e2c: d078 0bff 

  0x00000249ecd07e30: ;   {other}
  0x00000249ecd07e30: 0f1f 8400 | 2020 0026 | 488b f548 | 8b7c 2420 | 448b 5c24 | 3048 8b6c | 2438 e9c6 | eeff ff48 
  0x00000249ecd07e50: 8954 2440 | 4489 4424 | 3448 896c | 2438 4489 | 5c24 3048 | 897c 2420 | 488b ee4c | 8d84 2488 
  0x00000249ecd07e70: 0000 0090 

  0x00000249ecd07e74: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd07e74: c5f8 77e8 

  0x00000249ecd07e78: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=NarrowOop [52]=NarrowOop [56]=Oop [64]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::remove@6 (line 80)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07e78: 8478 0bff 

  0x00000249ecd07e7c: ;   {other}
  0x00000249ecd07e7c: 0f1f 8400 | 6c20 0027 | 488b f548 | 8b7c 2420 | 448b 5c24 | 3048 8b6c | 2438 448b | 4424 3448 
  0x00000249ecd07e9c: 8b54 2440 | e9eb eeff | ff48 8bca | 488d 9424 | 8800 0000 | 4d8b c7c5 | f877 49ba | e039 96f1 
  0x00000249ecd07ebc: fb7f 0000 

  0x00000249ecd07ec0: ;   {other}
  0x00000249ecd07ec0: 41ff d20f | 1f84 0000 | 0000 00e9 | b8ef ffff | 488b cd48 | 8d94 2480 | 0000 004d | 8bc7 c5f8 
  0x00000249ecd07ee0: 7749 bae0 | 3996 f1fb | 7f00 0041 

  0x00000249ecd07eec: ;   {other}
  0x00000249ecd07eec: ffd2 0f1f | 8400 0000 | 0000 e92a | f0ff ffba | deff ffff | 48ff 7424 | 2848 8f44 | 2420 4889 
  0x00000249ecd07f0c: 7424 2890 

  0x00000249ecd07f10: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07f10: c5f8 77e8 

  0x00000249ecd07f14: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07f14: e8ed fffe 

  0x00000249ecd07f18: ;   {other}
  0x00000249ecd07f18: 0f1f 8400 | 0821 0028 | 4c89 5c24 | 2048 8bee | 498b d34c | 8d84 2480 | 0000 0090 

  0x00000249ecd07f34: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd07f34: c5f8 77e8 

  0x00000249ecd07f38: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop }
                      ;*synchronization entry
                      ; - com.sun.jna.Memory$MemoryDisposer::run@-1 (line 794)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07f38: c477 0bff 

  0x00000249ecd07f3c: ;   {other}
  0x00000249ecd07f3c: 0f1f 8400 | 2c21 0029 | 488b f54c | 8b5c 2420 | e956 f0ff | ff48 8bcd | 488d 9424 | 8000 0000 
  0x00000249ecd07f5c: 4d8b c7c5 | f877 49ba | e039 96f1 | fb7f 0000 

  0x00000249ecd07f6c: ;   {other}
  0x00000249ecd07f6c: 41ff d20f | 1f84 0000 | 0000 00e9 | a9f1 ffff | ba45 ffff | ff48 ff74 | 2428 488f | 4424 2048 
  0x00000249ecd07f8c: 8944 24f8 | 8b44 2430 | 8944 2428 | 488b 4424 | f848 ff74 | 2438 488f | 4424 3048 | 8944 2438 
  0x00000249ecd07fac: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd07fac: c5f8 77e8 

  0x00000249ecd07fb0: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [48]=Oop [56]=Oop }
                      ;*if_icmpne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@19 (line 55)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd07fb0: 4ced fffe 

  0x00000249ecd07fb4: ;   {other}
  0x00000249ecd07fb4: 0f1f 8400 | a421 002a | 4533 d249 | 3bc2 bdff | ffff ff7c | 0840 0f95 | c540 0fb6 | edba 45ff 
  0x00000249ecd07fd4: ffff 48ff | 7424 2848 | 8f44 2420 | 4889 4424 | f88b 4424 | 3089 4424 | 2848 8b44 | 24f8 48ff 
  0x00000249ecd07ff4: 7424 3848 | 8f44 2430 | 48ff 7424 | 4048 8f44 | 2438 4889 | 4c24 4090 

  0x00000249ecd0800c: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd0800c: c5f8 77e8 

  0x00000249ecd08010: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [48]=Oop [56]=Oop [64]=Oop }
                      ;*ifne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::<init>@39 (line 118)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08010: ecec fffe 

  0x00000249ecd08014: ;   {other}
  0x00000249ecd08014: 0f1f 8400 | 0422 002b | ba45 ffff | ff48 8b6c | 2428 8b5c | 2430 895c | 2420 488b | 4c24 3848 
  0x00000249ecd08034: 894c 2428 | 488b 4c24 | 4048 894c | 2430 4c89 | 5424 404c | 895c 2448 | 4489 4c24 | 5044 8944 
  0x00000249ecd08054: 2454 6690 

  0x00000249ecd08058: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08058: c5f8 77e8 

  0x00000249ecd0805c: ; ImmutableOopMap {rbp=Oop [32]=NarrowOop [40]=Oop [48]=Oop [64]=Oop [72]=Oop [80]=NarrowOop [84]=NarrowOop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.internal.Cleaner::add@11 (line 63)
                      ; - com.sun.jna.internal.Cleaner::register@15 (line 58)
                      ; - com.sun.jna.Memory::<init>@114 (line 122)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0805c: a0ec fffe 

  0x00000249ecd08060: ;   {other}
  0x00000249ecd08060: 0f1f 8400 | 5022 002c | ba45 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 
  0x00000249ecd08080: 8944 2428 | 488b 4424 | f848 ff74 | 2438 488f | 4424 3048 | ff74 2440 | 488f 4424 | 3848 895c 
  0x00000249ecd080a0: 2448 4c89 | 5c24 5044 | 8944 2458 

  0x00000249ecd080ac: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd080ac: c5f8 77e8 

  0x00000249ecd080b0: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [40]=NarrowOop [48]=Oop [56]=Oop [72]=Oop [80]=Oop [88]=NarrowOop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.internal.Cleaner::add@47 (line 70)
                      ; - com.sun.jna.internal.Cleaner::register@15 (line 58)
                      ; - com.sun.jna.Memory::<init>@114 (line 122)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd080b0: 4cec fffe 

  0x00000249ecd080b4: ;   {other}
  0x00000249ecd080b4: 0f1f 8400 | a422 002d | ba45 ffff | ff41 bb04 | 0000 004d | 3bda bdff | ffff ff7c | 0840 0f95 
  0x00000249ecd080d4: c540 0fb6 | ed48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 | 8944 2428 | 488b 4424 
  0x00000249ecd080f4: f866 6690 

  0x00000249ecd080f8: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd080f8: c5f8 77e8 

  0x00000249ecd080fc: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [72]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::boundsCheck@41 (line 221)
                      ; - com.sun.jna.Memory::getInt@5 (line 516)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@2 (line 207)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd080fc: 00ec fffe 

  0x00000249ecd08100: ;   {other}
  0x00000249ecd08100: 0f1f 8400 | f022 002e | 41b8 1800 | 0000 4d3b | c2bd ffff | ffff 7c08 | 400f 95c5 | 400f b6ed 
  0x00000249ecd08120: ba45 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 | 8944 2428 | 488b 4424 
  0x00000249ecd08140: f889 4424 | 2c66 6690 

  0x00000249ecd08148: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08148: c5f8 77e8 

  0x00000249ecd0814c: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [72]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::boundsCheck@41 (line 221)
                      ; - com.sun.jna.Memory::getInt@5 (line 516)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readFILETIME@2 (line 244)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@9 (line 208)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0814c: b0eb fffe 

  0x00000249ecd08150: ;   {other}
  0x00000249ecd08150: 0f1f 8400 | 4023 002f | 41bb 1c00 | 0000 4d3b | dabd ffff | ffff 7c08 | 400f 95c5 | 400f b6ed 
  0x00000249ecd08170: ba45 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 | 8944 2428 | 488b 4424 
  0x00000249ecd08190: f889 4424 | 3066 6690 

  0x00000249ecd08198: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08198: c5f8 77e8 

  0x00000249ecd0819c: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [72]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::boundsCheck@41 (line 221)
                      ; - com.sun.jna.Memory::getInt@5 (line 516)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readFILETIME@10 (line 245)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@9 (line 208)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0819c: 60eb fffe 

  0x00000249ecd081a0: ;   {other}
  0x00000249ecd081a0: 0f1f 8400 | 9023 0030 | 41bb 2000 | 0000 4d3b | dabd ffff | ffff 7c08 | 400f 95c5 | 400f b6ed 
  0x00000249ecd081c0: ba45 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 | 8944 2428 | 488b 4424 
  0x00000249ecd081e0: f848 8944 | 24f8 8b44 | 2434 8944 | 242c 488b | 4424 f890 

  0x00000249ecd081f4: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd081f4: c5f8 77e8 

  0x00000249ecd081f8: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [72]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::boundsCheck@41 (line 221)
                      ; - com.sun.jna.Memory::getInt@5 (line 516)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORD@2 (line 240)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@17 (line 209)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd081f8: 04eb fffe 

  0x00000249ecd081fc: ;   {other}
  0x00000249ecd081fc: 0f1f 8400 | ec23 0031 | 41bb 2400 | 0000 4d3b | dabd ffff | ffff 7c08 | 400f 95c5 | 400f b6ed 
  0x00000249ecd0821c: ba45 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 | 8944 2428 | 488b 4424 
  0x00000249ecd0823c: f848 8944 | 24f8 8b44 | 2434 8944 | 242c 488b | 4424 f84c | 8944 2430 

  0x00000249ecd08254: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08254: c5f8 77e8 

  0x00000249ecd08258: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [72]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::boundsCheck@41 (line 221)
                      ; - com.sun.jna.Memory::getInt@5 (line 516)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORD@2 (line 240)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@25 (line 210)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08258: a4ea fffe 

  0x00000249ecd0825c: ;   {other}
  0x00000249ecd0825c: 0f1f 8400 | 4c24 0032 | ba45 ffff | ff41 bb28 | 0000 004d | 3bda bdff | ffff ff7c | 0840 0f95 
  0x00000249ecd0827c: c540 0fb6 | ed48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 | 8944 2428 | 488b 4424 
  0x00000249ecd0829c: f848 8944 | 24f8 8b44 | 2434 8944 | 242c 488b | 4424 f848 | ff74 2450 | 488f 4424 | 3048 ff74 
  0x00000249ecd082bc: 2458 488f | 4424 5090 

  0x00000249ecd082c4: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd082c4: c5f8 77e8 

  0x00000249ecd082c8: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [72]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::boundsCheck@41 (line 221)
                      ; - com.sun.jna.Memory::getInt@5 (line 516)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::readDWORDAsSignedInt@3 (line 234)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@33 (line 211)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd082c8: 34ea fffe 

  0x00000249ecd082cc: ;   {other}
  0x00000249ecd082cc: 0f1f 8400 | bc24 0033 | 4d3b cabd | ffff ffff | 7c08 400f | 95c5 400f | b6ed ba45 | ffff ff48 
  0x00000249ecd082ec: ff74 2428 | 488f 4424 | 2048 8944 | 24f8 8b44 | 2430 8944 | 2428 488b | 4424 f848 | 8944 24f8 
  0x00000249ecd0830c: 8b44 2434 | 8944 242c | 488b 4424 | f848 ff74 | 2450 488f | 4424 3048 | ff74 2458 | 488f 4424 
  0x00000249ecd0832c: 5089 4424 | 5866 6690 

  0x00000249ecd08334: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08334: c5f8 77e8 

  0x00000249ecd08338: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [56]=Oop [72]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::boundsCheck@41 (line 221)
                      ; - com.sun.jna.Memory::getWideString@3 (line 608)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@42 (line 212)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08338: c4e9 fffe 

  0x00000249ecd0833c: ;   {other}
  0x00000249ecd0833c: 0f1f 8400 | 2c25 0034 | ba45 ffff | ff48 8b6c | 2428 448b | 4424 3044 | 8944 2420 | 448b 4424 
  0x00000249ecd0835c: 6044 8944 | 2430 4c89 | 5424 5048 | 8944 2460 

  0x00000249ecd0836c: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd0836c: c5f8 77e8 

  0x00000249ecd08370: ; ImmutableOopMap {rbp=Oop [32]=NarrowOop [56]=Oop [72]=Oop [96]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.core.filesystem.provider.FileInfo::setName@1 (line 254)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@61 (line 216)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08370: 8ce9 fffe 

  0x00000249ecd08374: ;   {other}
  0x00000249ecd08374: 0f1f 8400 | 6425 0035 | 33ed 4585 | db40 0f95 | c5ba 45ff | ffff 48ff | 7424 2848 | 8f44 2420 
  0x00000249ecd08394: 4889 4424 | f88b 4424 | 3089 4424 | 2848 8b44 | 24f8 4889 | 7424 4090 

  0x00000249ecd083ac: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd083ac: c5f8 77e8 

  0x00000249ecd083b0: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [64]=Oop [72]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@143 (line 226)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd083b0: 4ce9 fffe 

  0x00000249ecd083b4: ;   {other}
  0x00000249ecd083b4: 0f1f 8400 | a425 0036 | 4533 d249 | 3bd2 bdff | ffff ff7c | 0840 0f95 | c540 0fb6 | edba 45ff 
  0x00000249ecd083d4: ffff 48ff | 7424 2848 | 8f44 2420 | 4889 7424 | 284c 895c | 2438 6690 

  0x00000249ecd083ec: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd083ec: c5f8 77e8 

  0x00000249ecd083f0: ; ImmutableOopMap {[32]=Oop [40]=Oop [56]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::free@3 (line 747)
                      ; - com.sun.jna.Memory$MemoryDisposer::run@4 (line 794)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd083f0: 0ce9 fffe 

  0x00000249ecd083f4: ;   {other}
  0x00000249ecd083f4: 0f1f 8400 | e425 0037 | bae4 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8944 24f8 | 8b44 2430 
  0x00000249ecd08414: 8944 2428 | 488b 4424 | f848 ff74 | 2438 488f | 4424 3048 | ff74 2440 | 488f 4424 | 3848 894c 
  0x00000249ecd08434: 2448 6690 

  0x00000249ecd08438: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08438: c5f8 77e8 

  0x00000249ecd0843c: ; ImmutableOopMap {[32]=Oop [40]=NarrowOop [48]=Oop [56]=Oop [72]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Long::valueOf@29
                      ; - com.sun.jna.Memory::<init>@81 (line 121)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@87 (line 63)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0843c: c0e8 fffe 

  0x00000249ecd08440: ;   {other}
  0x00000249ecd08440: 0f1f 8400 | 3026 0038 | bae4 ffff | ff48 ff74 | 2428 488f | 4424 204c | 8b54 2438 | 4c89 5424 
  0x00000249ecd08460: 284c 894c | 2438 6690 

  0x00000249ecd08468: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08468: c5f8 77e8 

  0x00000249ecd0846c: ; ImmutableOopMap {[32]=Oop [40]=Oop [56]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Long::valueOf@29
                      ; - com.sun.jna.Memory$MemoryDisposer::run@14 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0846c: 90e8 fffe 

  0x00000249ecd08470: ;   {other}
  0x00000249ecd08470: 0f1f 8400 | 6026 0039 | baf6 ffff | ff66 6690 

  0x00000249ecd08480: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08480: c5f8 77e8 

  0x00000249ecd08484: ; ImmutableOopMap {}
                      ;*invokevirtual getName {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@22 (line 231)
  0x00000249ecd08484: 78e8 fffe 

  0x00000249ecd08488: ;   {other}
  0x00000249ecd08488: 0f1f 8400 | 7826 003a | baf6 ffff | ff66 6690 

  0x00000249ecd08498: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08498: c5f8 77e8 

  0x00000249ecd0849c: ; ImmutableOopMap {}
                      ;*invokevirtual toCharArray {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@34 (line 233)
  0x00000249ecd0849c: 60e8 fffe 

  0x00000249ecd084a0: ;   {other}
  0x00000249ecd084a0: 0f1f 8400 | 9026 003b | baf6 ffff | ff48 8b6c | 2428 4889 | 7424 2848 | 897c 2430 

  0x00000249ecd084bc: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd084bc: c5f8 77e8 

  0x00000249ecd084c0: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=Oop }
                      ;*invokespecial remove {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd084c0: 3ce8 fffe 

  0x00000249ecd084c4: ;   {other}
  0x00000249ecd084c4: 0f1f 8400 | b426 003c | baf6 ffff | ff48 8b6c | 2428 4889 | 7424 2848 | 897c 2440 | 4489 5424 
  0x00000249ecd084e4: 3c66 6690 

  0x00000249ecd084e8: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd084e8: c5f8 77e8 

  0x00000249ecd084ec: ; ImmutableOopMap {rbp=Oop [40]=Oop [60]=NarrowOop [64]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::remove@6 (line 80)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd084ec: 10e8 fffe 

  0x00000249ecd084f0: ;   {other}
  0x00000249ecd084f0: 0f1f 8400 | e026 003d | bade ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8974 2430 

  0x00000249ecd0850c: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd0850c: c5f8 77e8 

  0x00000249ecd08510: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [48]=Oop }
                      ;*invokeinterface clean {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08510: ece7 fffe 

  0x00000249ecd08514: ;   {other}
  0x00000249ecd08514: 0f1f 8400 | 0427 003e | 4889 6c24 | 3844 8954 | 2430 4889 | 7c24 2048 | 8bee 488b | 5424 384c 
  0x00000249ecd08534: 8d84 2480 | 0000 0090 

  0x00000249ecd0853c: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd0853c: c5f8 77e8 

  0x00000249ecd08540: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=NarrowOop [56]=Oop }
                      ;*synchronization entry
                      ; - com.sun.jna.internal.Cleaner::remove@-1 (line 80)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08540: bc71 0bff 

  0x00000249ecd08544: ;   {other}
  0x00000249ecd08544: 0f1f 8400 | 3427 003f | 488b f548 | 8b7c 2420 | 448b 5424 | 3048 8b6c | 2438 e9e1 | eeff ff48 
  0x00000249ecd08564: 8954 2440 | 4489 5c24 | 3448 896c | 2438 4489 | 5424 3048 | 897c 2420 | 488b ee4c | 8d84 2488 
  0x00000249ecd08584: 0000 0090 

  0x00000249ecd08588: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd08588: c5f8 77e8 

  0x00000249ecd0858c: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=NarrowOop [52]=NarrowOop [56]=Oop [64]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner::remove@6 (line 80)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0858c: 7071 0bff 

  0x00000249ecd08590: ;   {other}
  0x00000249ecd08590: 0f1f 8400 | 8027 0040 | 488b f548 | 8b7c 2420 | 448b 5424 | 3048 8b6c | 2438 448b | 5c24 3448 
  0x00000249ecd085b0: 8b54 2440 | e906 efff | ff48 8bcd | 488d 9424 | 8000 0000 | 4d8b c7c5 | f877 49ba | e039 96f1 
  0x00000249ecd085d0: fb7f 0000 

  0x00000249ecd085d4: ;   {other}
  0x00000249ecd085d4: 41ff d20f | 1f84 0000 | 0000 00e9 | 72f0 ffff | 488b ca48 | 8d94 2488 | 0000 004d | 8bc7 c5f8 
  0x00000249ecd085f4: 7749 bae0 | 3996 f1fb | 7f00 0041 

  0x00000249ecd08600: ;   {other}
  0x00000249ecd08600: ffd2 0f1f | 8400 0000 | 0000 e9aa | efff ffba | deff ffff | 48ff 7424 | 2848 8f44 | 2420 4889 
  0x00000249ecd08620: 7424 3090 

  0x00000249ecd08624: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08624: c5f8 77e8 

  0x00000249ecd08628: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [48]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08628: d4e6 fffe 

  0x00000249ecd0862c: ;   {other}
  0x00000249ecd0862c: 0f1f 8400 | 1c28 0041 | 4c89 5c24 | 2048 8bee | 498b d34c | 8d84 2480 | 0000 0090 

  0x00000249ecd08648: ;   {runtime_call _complete_monitor_locking_Java}
  0x00000249ecd08648: c5f8 77e8 

  0x00000249ecd0864c: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop }
                      ;*synchronization entry
                      ; - com.sun.jna.Memory$MemoryDisposer::run@-1 (line 794)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0864c: b070 0bff 

  0x00000249ecd08650: ;   {other}
  0x00000249ecd08650: 0f1f 8400 | 4028 0042 | 488b f54c | 8b5c 2420 | e973 f0ff | ff45 33d2 | 493b d2bd | ffff ffff 
  0x00000249ecd08670: 7c08 400f | 95c5 400f | b6ed ba45 | ffff ff48 | ff74 2428 | 488f 4424 | 2048 8974 | 2430 4c89 
  0x00000249ecd08690: 5c24 4090 

  0x00000249ecd08694: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08694: c5f8 77e8 

  0x00000249ecd08698: ; ImmutableOopMap {[32]=Oop [48]=Oop [64]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::free@3 (line 747)
                      ; - com.sun.jna.Memory$MemoryDisposer::run@4 (line 794)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08698: 64e6 fffe 

  0x00000249ecd0869c: ;   {other}
  0x00000249ecd0869c: 0f1f 8400 | 8c28 0043 | bae4 ffff | ff48 ff74 | 2428 488f | 4424 204c | 8b54 2438 | 4c89 5424 
  0x00000249ecd086bc: 304c 894c | 2440 6690 

  0x00000249ecd086c4: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd086c4: c5f8 77e8 

  0x00000249ecd086c8: ; ImmutableOopMap {[32]=Oop [48]=Oop [64]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Long::valueOf@29
                      ; - com.sun.jna.Memory$MemoryDisposer::run@14 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd086c8: 34e6 fffe 

  0x00000249ecd086cc: ;   {other}
  0x00000249ecd086cc: 0f1f 8400 | bc28 0044 | 4489 6c24 | 4c4c 8954 | 2440 4c89 | 7424 304c | 894c 2428 

  0x00000249ecd086e8: ;   {metadata({type array byte})}
  0x00000249ecd086e8: 48ba 6822 | 0080 4902 | 0000 4489 | 5c24 4890 

  0x00000249ecd086f8: ;   {runtime_call _new_array_nozero_Java}
  0x00000249ecd086f8: c5f8 77e8 

  0x00000249ecd086fc: ; ImmutableOopMap {rbp=Oop [40]=Oop [64]=Derived_oop_[40] [56]=Oop [76]=NarrowOop }
                      ;*invokestatic toBytes {reexecute=1 rethrow=0 return_oop=1}
                      ; - (reexecute) java.lang.String::<init>@67
                      ; - java.lang.String::<init>@6
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@37 (line 233)
  0x00000249ecd086fc: 0052 0bff 

  0x00000249ecd08700: ;   {other}
  0x00000249ecd08700: 0f1f 8400 | f028 0045 | 4c8b 7424 | 304c 8b54 | 2440 488b | d844 8b6c | 244c e9fe | f3ff ffba 
  0x00000249ecd08720: ceff ffff | 48ff 7424 | 3848 8f44 | 2428 4c89 | 4c24 3044 | 895c 2438 

  0x00000249ecd08738: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08738: c5f8 77e8 

  0x00000249ecd0873c: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=Oop }
                      ;*invokestatic toBytes {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.String::<init>@67
                      ; - java.lang.String::<init>@6
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@37 (line 233)
  0x00000249ecd0873c: c0e5 fffe 

  0x00000249ecd08740: ;   {other}
  0x00000249ecd08740: 0f1f 8400 | 3029 0046 | 4963 c8e9 | 69f3 ffff | baf6 ffff | ff8b 6c24 | 3066 6690 

  0x00000249ecd0875c: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd0875c: c5f8 77e8 

  0x00000249ecd08760: ; ImmutableOopMap {rbp=NarrowOop }
                      ;*invokevirtual fetchFileInfo {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08760: 9ce5 fffe 

  0x00000249ecd08764: ;   {other}
  0x00000249ecd08764: 0f1f 8400 | 5429 0047 | baf6 ffff | ff48 8b6c | 2428 6690 

  0x00000249ecd08778: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08778: c5f8 77e8 

  0x00000249ecd0877c: ; ImmutableOopMap {rbp=Oop [48]=NarrowOop [56]=Oop }
                      ;*invokevirtual length {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@14 (line 55)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0877c: 80e5 fffe 

  0x00000249ecd08780: ;   {other}
  0x00000249ecd08780: 0f1f 8400 | 7029 0048 | baf6 ffff | ff48 8b6c | 2428 4889 | 4424 f88b | 4424 3089 | 4424 2048 
  0x00000249ecd087a0: 8b44 24f8 | 4889 4424 | 3066 6690 

  0x00000249ecd087ac: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd087ac: c5f8 77e8 

  0x00000249ecd087b0: ; ImmutableOopMap {rbp=Oop [32]=NarrowOop [48]=Oop [56]=Oop }
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::length@4
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@14 (line 55)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd087b0: 4ce5 fffe 

  0x00000249ecd087b4: ;   {other}
  0x00000249ecd087b4: 0f1f 8400 | a429 0049 | baf6 ffff | ff66 6690 

  0x00000249ecd087c4: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd087c4: c5f8 77e8 

  0x00000249ecd087c8: ; ImmutableOopMap {}
                      ;*invokevirtual isEmpty {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@12 (line 230)
  0x00000249ecd087c8: 34e5 fffe 

  0x00000249ecd087cc: ;   {other}
  0x00000249ecd087cc: 0f1f 8400 | bc29 004a | baf6 ffff | ff66 6690 

  0x00000249ecd087dc: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd087dc: c5f8 77e8 

  0x00000249ecd087e0: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::isEmpty@4
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@12 (line 230)
  0x00000249ecd087e0: 1ce5 fffe 

  0x00000249ecd087e4: ;   {other}
  0x00000249ecd087e4: 0f1f 8400 | d429 004b | baf6 ffff | ff48 8b6c | 2428 6690 

  0x00000249ecd087f8: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd087f8: c5f8 77e8 

  0x00000249ecd087fc: ; ImmutableOopMap {rbp=Oop [56]=Oop [72]=Oop }
                      ;*invokevirtual getSupportedAttributes {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::getSupportedAttributes@3 (line 79)
                      ; - org.eclipse.core.filesystem.provider.FileInfo::isAttributeSuported@0 (line 193)
                      ; - org.eclipse.core.filesystem.provider.FileInfo::setAttribute@7 (line 171)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::convertFindDataWToFileInfo@116 (line 222)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@160 (line 79)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd087fc: 00e5 fffe 

  0x00000249ecd08800: ;   {other}
  0x00000249ecd08800: 0f1f 8400 | f029 004c | baf6 ffff | ff48 8b6c | 2428 4889 | 7424 2090 

  0x00000249ecd08818: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08818: c5f8 77e8 

  0x00000249ecd0881c: ; ImmutableOopMap {rbp=Oop [32]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0881c: e0e4 fffe 

  0x00000249ecd08820: ;   {other}
  0x00000249ecd08820: 0f1f 8400 | 102a 004d | ba45 ffff | ff48 ff74 | 2428 488f | 4424 2090 

  0x00000249ecd08838: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08838: c5f8 77e8 

  0x00000249ecd0883c: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [56]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::close@9 (line 188)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd0883c: c0e4 fffe 

  0x00000249ecd08840: ;   {other}
  0x00000249ecd08840: 0f1f 8400 | 302a 004e | ba45 ffff | ff48 8b6c | 2428 4889 | 7424 2048 | 897c 2430 | 894c 243c 
  0x00000249ecd08860: 4489 5424 | 4044 895c | 2444 4489 | 4424 4890 

  0x00000249ecd08870: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08870: c5f8 77e8 

  0x00000249ecd08874: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=NarrowOop [68]=NarrowOop [72]=NarrowOop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.internal.Cleaner::remove@49 (line 89)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08874: 88e4 fffe 

  0x00000249ecd08878: ;   {other}
  0x00000249ecd08878: 0f1f 8400 | 682a 004f | baf6 ffff | ff66 6690 

  0x00000249ecd08888: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08888: c5f8 77e8 

  0x00000249ecd0888c: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@4
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@37 (line 233)
  0x00000249ecd0888c: 70e4 fffe 

  0x00000249ecd08890: ;   {other}
  0x00000249ecd08890: 0f1f 8400 | 802a 0050 | baf6 ffff | ff48 8b6c | 2428 4889 | 7424 2890 

  0x00000249ecd088a8: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd088a8: c5f8 77e8 

  0x00000249ecd088ac: ; ImmutableOopMap {rbp=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd088ac: 50e4 fffe 

  0x00000249ecd088b0: ;   {other}
  0x00000249ecd088b0: 0f1f 8400 | a02a 0051 | ba45 ffff | ff48 ff74 | 2428 488f | 4424 2048 | 8974 2430 

  0x00000249ecd088cc: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd088cc: c5f8 77e8 

  0x00000249ecd088d0: ; ImmutableOopMap {rbp=NarrowOop [32]=Oop [48]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.Memory::close@9 (line 188)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd088d0: 2ce4 fffe 

  0x00000249ecd088d4: ;   {other}
  0x00000249ecd088d4: 0f1f 8400 | c42a 0052 | ba45 ffff | ff48 8b6c | 2428 4889 | 7424 2848 | 897c 2438 | 4489 4424 
  0x00000249ecd088f4: 4444 894c | 2448 4489 | 5424 4c44 | 895c 2450 

  0x00000249ecd08904: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecd08904: c5f8 77e8 

  0x00000249ecd08908: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop [72]=NarrowOop [76]=NarrowOop [80]=NarrowOop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) com.sun.jna.internal.Cleaner::remove@49 (line 89)
                      ; - com.sun.jna.internal.Cleaner::access$000@2 (line 40)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@5 (line 115)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08908: f4e3 fffe 

  0x00000249ecd0890c: ;   {other}
  0x00000249ecd0890c: 0f1f 8400 | fc2a 0053 | eb2b 488b | d84c 8bdd | e903 0200 | 0048 8bd8 | 4c8b dde9 | f801 0000 
  0x00000249ecd0892c: 488b d84c | 8b5c 2420 | e9eb 0100 | 0048 8bd0 | e995 0400 | 0048 8bd0 | e98d 0400 | 0048 8bd8 
  0x00000249ecd0894c: 4c8b dde9 | 3003 0000 | 488b d84c | 8bdd e925 | 0300 004c | 8bc0 448b | 5008 49bb | 0000 0080 
  0x00000249ecd0896c: 4902 0000 | 4d03 da4d 

  0x00000249ecd08974: ;   {metadata('java/io/IOException')}
  0x00000249ecd08974: 8b53 4849 | bb68 6004 | 8149 0200 | 004d 3bd3 | 0f84 3404 | 0000 e944 | 0400 0048 | 8bd8 4c8b 
  0x00000249ecd08994: 5c24 20e9 | e802 0000 | 488b d8e9 | f303 0000 | 4c8b c0e9 | bd03 0000 | 4889 4424 | 30e9 9203 
  0x00000249ecd089b4: 0000 488b 

  0x00000249ecd089b8: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd089b8: d849 bb48 | d3cb c000 | 0000 0048 | 8d84 2480 | 0000 0048 | 8338 000f | 8479 0000 | 004d 8b13 
  0x00000249ecd089d8: 41f6 c202 | 0f84 6200 | 0000 4983 | ba86 0000 | 0000 7409 | 49ff 8a86 | 0000 00eb | 4b49 8b82 
  0x00000249ecd089f8: 9600 0000 | 490b 828e | 0000 0075 | 0a49 c742 | 3e00 0000 | 00eb 3d49 | 83ba 9e00 | 0000 0074 
  0x00000249ecd08a18: 2248 33c0 | 49c7 423e | 0000 0000 | f083 0424 | 0049 83ba | 9e00 0000 | 0075 0df0 | 4d0f b17a 
  0x00000249ecd08a38: 3e75 0583 | c801 eb0c | a800 eb08 | 4c8b 10f0 | 4d0f b113 | 750a 49ff | 8f48 0500 | 0045 33d2 
  0x00000249ecd08a58: 0f84 3903 

  0x00000249ecd08a5c: ;   {oop(a 'com/sun/jna/internal/Cleaner'{0x00000000c0cbd348})}
  0x00000249ecd08a5c: 0000 48b9 | 48d3 cbc0 | 0000 0000 | 488d 9424 | 8000 0000 | 4d8b c7c5 | f877 49ba | e039 96f1 
  0x00000249ecd08a7c: fb7f 0000 

  0x00000249ecd08a80: ;   {other}
  0x00000249ecd08a80: 41ff d20f | 1f84 0000 | 0000 00e9 | 0703 0000 | 488b d8e9 | ff02 0000 | 488b d8e9 | f702 0000 
  0x00000249ecd08aa0: 488b d8e9 | ef02 0000 | 4c8b c0e9 | 2303 0000 | 4889 4424 | 30e9 8e02 | 0000 488b | d0e9 1403 
  0x00000249ecd08ac0: 0000 488b | d0e9 0c03 | 0000 488b | d84c 8bdd | eb52 4889 | 4424 2048 | 8b55 1048 | ff74 2438 
  0x00000249ecd08ae0: 488f 4424 | 3066 6690 

  0x00000249ecd08ae8: ;   {static_call}
  0x00000249ecd08ae8: c5f8 77e8 

  0x00000249ecd08aec: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*invokestatic valueOf {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.Memory$MemoryDisposer::run@39 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08aec: 906a fffe 

  0x00000249ecd08af0: ;   {other}
  0x00000249ecd08af0: 0f1f 8400 | e02c 0054 

  0x00000249ecd08af8: ;   {oop(a 'java/util/concurrent/ConcurrentHashMap'{0x00000000c0b6efd8})}
  0x00000249ecd08af8: 48ba d8ef | b6c0 0000 | 0000 4c8b | c066 6690 

  0x00000249ecd08b08: ;   {optimized virtual_call}
  0x00000249ecd08b08: c5f8 77e8 

  0x00000249ecd08b0c: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*invokeinterface remove {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.Memory$MemoryDisposer::run@42 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@146 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08b0c: 7060 fffe 

  0x00000249ecd08b10: ;   {other}
  0x00000249ecd08b10: 0f1f 8400 | 002d 0055 | 4c8b dd4d | 8963 1048 | 8b5c 2420 | 488d 8424 | 8000 0000 | 4883 3800 
  0x00000249ecd08b30: 0f84 7900 | 0000 4d8b | 1341 f6c2 | 020f 8462 | 0000 0049 | 83ba 8600 | 0000 0074 | 0949 ff8a 
  0x00000249ecd08b50: 8600 0000 | eb4b 498b | 8296 0000 | 0049 0b82 | 8e00 0000 | 750a 49c7 | 423e 0000 | 0000 eb3d 
  0x00000249ecd08b70: 4983 ba9e | 0000 0000 | 7422 4833 | c049 c742 | 3e00 0000 | 00f0 8304 | 2400 4983 | ba9e 0000 
  0x00000249ecd08b90: 0000 750d | f04d 0fb1 | 7a3e 7505 | 83c8 01eb | 0ca8 00eb | 084c 8b10 | f04d 0fb1 | 1375 0a49 
  0x00000249ecd08bb0: ff8f 4805 | 0000 4533 | d20f 84d8 | 0100 0049 | 8bcb 488d | 9424 8000 | 0000 4d8b | c7c5 f877 
  0x00000249ecd08bd0: 49ba e039 | 96f1 fb7f | 0000 41ff 

  0x00000249ecd08bdc: ;   {other}
  0x00000249ecd08bdc: d20f 1f84 | 0000 0000 | 00e9 ad01 | 0000 488b | 5c24 30e9 | a301 0000 | e94a 0100 | 00e9 4501 
  0x00000249ecd08bfc: 0000 e940 | 0100 00e9 | 3b01 0000 | e936 0100 | 00e9 3101 | 0000 4889 | 4424 30e9 | 2c01 0000 
  0x00000249ecd08c1c: 4889 4424 | 30e9 2201 | 0000 488b | d8e9 6901 | 0000 488b | d8e9 6101 | 0000 488b | d84c 8bdd 
  0x00000249ecd08c3c: eb46 4889 | 4424 2048 | 8b55 1090 

  0x00000249ecd08c48: ;   {static_call}
  0x00000249ecd08c48: c5f8 77e8 

  0x00000249ecd08c4c: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [56]=Oop }
                      ;*invokestatic valueOf {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.Memory$MemoryDisposer::run@39 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08c4c: 3069 fffe 

  0x00000249ecd08c50: ;   {other}
  0x00000249ecd08c50: 0f1f 8400 | 402e 0056 

  0x00000249ecd08c58: ;   {oop(a 'java/util/concurrent/ConcurrentHashMap'{0x00000000c0b6efd8})}
  0x00000249ecd08c58: 48ba d8ef | b6c0 0000 | 0000 4c8b | c066 6690 

  0x00000249ecd08c68: ;   {optimized virtual_call}
  0x00000249ecd08c68: c5f8 77e8 

  0x00000249ecd08c6c: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [56]=Oop }
                      ;*invokeinterface remove {reexecute=0 rethrow=0 return_oop=1}
                      ; - com.sun.jna.Memory$MemoryDisposer::run@42 (line 796)
                      ; - com.sun.jna.internal.Cleaner$CleanerRef::clean@15 (line 116)
                      ; - com.sun.jna.Memory::close@16 (line 189)
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@170 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08c6c: 105f fffe 

  0x00000249ecd08c70: ;   {other}
  0x00000249ecd08c70: 0f1f 8400 | 602e 0057 | 4c8b dd4d | 8963 1048 | 8b5c 2420 | 488d 8424 | 8000 0000 | 4883 3800 
  0x00000249ecd08c90: 0f84 7900 | 0000 4d8b | 1341 f6c2 | 020f 8462 | 0000 0049 | 83ba 8600 | 0000 0074 | 0949 ff8a 
  0x00000249ecd08cb0: 8600 0000 | eb4b 498b | 8296 0000 | 0049 0b82 | 8e00 0000 | 750a 49c7 | 423e 0000 | 0000 eb3d 
  0x00000249ecd08cd0: 4983 ba9e | 0000 0000 | 7422 4833 | c049 c742 | 3e00 0000 | 00f0 8304 | 2400 4983 | ba9e 0000 
  0x00000249ecd08cf0: 0000 750d | f04d 0fb1 | 7a3e 7505 | 83c8 01eb | 0ca8 00eb | 084c 8b10 | f04d 0fb1 | 1375 0a49 
  0x00000249ecd08d10: ff8f 4805 | 0000 4533 | d274 7c49 | 8bcb 488d | 9424 8000 | 0000 4d8b | c7c5 f877 | 49ba e039 
  0x00000249ecd08d30: 96f1 fb7f | 0000 41ff 

  0x00000249ecd08d38: ;   {other}
  0x00000249ecd08d38: d20f 1f84 | 0000 0000 | 00eb 5448 | 8944 2430 | 488b 5424 | 4848 8b6c | 2428 6690 

  0x00000249ecd08d54: ;   {optimized virtual_call}
  0x00000249ecd08d54: c5f8 77e8 

  0x00000249ecd08d58: ; ImmutableOopMap {rbp=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*invokevirtual close {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@185 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08d58: 245e fffe 

  0x00000249ecd08d5c: ;   {other}
  0x00000249ecd08d5c: 0f1f 8400 | 4c2f 0058 | 4c8b 4424 | 304c 8b54 | 2430 4d3b | d00f 8473 | feff ff48 | 8b6c 2430 
  0x00000249ecd08d7c: 488b 5424 | 3066 6690 

  0x00000249ecd08d84: ;   {optimized virtual_call}
  0x00000249ecd08d84: c5f8 77e8 

  0x00000249ecd08d88: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop }
                      ;*invokevirtual addSuppressed {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.internal.filesystem.local.Win32Handler::fetchFileInfo@216 (line 80)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFileNativesManager::fetchFileInfo@4 (line 83)
                      ; - org.eclipse.core.internal.filesystem.local.LocalFile::fetchInfo@4 (line 228)
  0x00000249ecd08d88: f45d fffe 

  0x00000249ecd08d8c: ;   {other}
  0x00000249ecd08d8c: 0f1f 8400 | 7c2f 0059 | 488b dd4c | 8bc3 448b | 5b08 49ba | 0000 0080 | 4902 0000 | 4d03 d34d 
  0x00000249ecd08dac: ;   {metadata('java/io/IOException')}
  0x00000249ecd08dac: 8b52 4849 | bb68 6004 | 8149 0200 | 004d 3bd3 | 7515 4c8b | 5424 3841 | c742 2005 | 0000 00e9 
  0x00000249ecd08dcc: 55e3 ffff | 4c8b c049 | 8bd0 c5f8 | 7748 81c4 | a000 0000 

  0x00000249ecd08de0: ;   {runtime_call _rethrow_Java}
  0x00000249ecd08de0: 5de9 1a72 

  0x00000249ecd08de4: ;   {internal_word}
  0x00000249ecd08de4: 0bff 49ba | 4b71 d0ec | 4902 0000 | 4d89 9760 

  0x00000249ecd08df4: ;   {runtime_call SafepointBlob}
  0x00000249ecd08df4: 0400 00e9 | 84cd fffe 

  0x00000249ecd08dfc: ;   {runtime_call StubRoutines (final stubs)}
  0x00000249ecd08dfc: e8ff 49fe | fee9 d8d4 | ffff f4f4 
[Stub Code]
  0x00000249ecd08e08: ;   {no_reloc}
  0x00000249ecd08e08: 48bb 0000 | 0000 0000 

  0x00000249ecd08e10: ;   {runtime_call nmethod}
  0x00000249ecd08e10: 0000 e9fb 

  0x00000249ecd08e14: ;   {static_stub}
  0x00000249ecd08e14: ffff ff48 | bb00 0000 | 0000 0000 

  0x00000249ecd08e20: ;   {runtime_call nmethod}
  0x00000249ecd08e20: 00e9 fbff 

  0x00000249ecd08e24: ;   {static_stub}
  0x00000249ecd08e24: ffff 48bb | 0000 0000 | 0000 0000 

  0x00000249ecd08e30: ;   {runtime_call nmethod}
  0x00000249ecd08e30: e9fb ffff 

  0x00000249ecd08e34: ;   {static_stub}
  0x00000249ecd08e34: ff48 bb00 | 0000 0000 

  0x00000249ecd08e3c: ;   {runtime_call nmethod}
  0x00000249ecd08e3c: 0000 00e9 | fbff ffff 

  0x00000249ecd08e44: ;   {static_stub}
  0x00000249ecd08e44: 48bb 0000 | 0000 0000 

  0x00000249ecd08e4c: ;   {runtime_call nmethod}
  0x00000249ecd08e4c: 0000 e9fb 

  0x00000249ecd08e50: ;   {static_stub}
  0x00000249ecd08e50: ffff ff48 | bb00 0000 | 0000 0000 

  0x00000249ecd08e5c: ;   {runtime_call nmethod}
  0x00000249ecd08e5c: 00e9 fbff 

  0x00000249ecd08e60: ;   {static_stub}
  0x00000249ecd08e60: ffff 48bb | 0000 0000 | 0000 0000 

  0x00000249ecd08e6c: ;   {runtime_call nmethod}
  0x00000249ecd08e6c: e9fb ffff 

  0x00000249ecd08e70: ;   {static_stub}
  0x00000249ecd08e70: ff48 bb00 | 0000 0000 

  0x00000249ecd08e78: ;   {runtime_call nmethod}
  0x00000249ecd08e78: 0000 00e9 | fbff ffff 

  0x00000249ecd08e80: ;   {static_stub}
  0x00000249ecd08e80: 48bb 0000 | 0000 0000 

  0x00000249ecd08e88: ;   {runtime_call nmethod}
  0x00000249ecd08e88: 0000 e9fb 

  0x00000249ecd08e8c: ;   {static_stub}
  0x00000249ecd08e8c: ffff ff48 | bb00 0000 | 0000 0000 

  0x00000249ecd08e98: ;   {runtime_call nmethod}
  0x00000249ecd08e98: 00e9 fbff 

  0x00000249ecd08e9c: ;   {static_stub}
  0x00000249ecd08e9c: ffff 48bb | 0000 0000 | 0000 0000 

  0x00000249ecd08ea8: ;   {runtime_call nmethod}
  0x00000249ecd08ea8: e9fb ffff 

  0x00000249ecd08eac: ;   {static_stub}
  0x00000249ecd08eac: ff48 bb00 | 0000 0000 

  0x00000249ecd08eb4: ;   {runtime_call nmethod}
  0x00000249ecd08eb4: 0000 00e9 | fbff ffff 

  0x00000249ecd08ebc: ;   {static_stub}
  0x00000249ecd08ebc: 48bb 0000 | 0000 0000 

  0x00000249ecd08ec4: ;   {runtime_call nmethod}
  0x00000249ecd08ec4: 0000 e9fb 

  0x00000249ecd08ec8: ;   {static_stub}
  0x00000249ecd08ec8: ffff ff48 | bb30 2640 | 8049 0200 

  0x00000249ecd08ed4: ;   {runtime_call I2C/C2I adapters}
  0x00000249ecd08ed4: 00e9 940c 

  0x00000249ecd08ed8: ;   {static_stub}
  0x00000249ecd08ed8: fdfe 48bb | 0000 0000 | 0000 0000 

  0x00000249ecd08ee4: ;   {runtime_call nmethod}
  0x00000249ecd08ee4: e9fb ffff 

  0x00000249ecd08ee8: ;   {static_stub}
  0x00000249ecd08ee8: ff48 bb00 | 0000 0000 

  0x00000249ecd08ef0: ;   {runtime_call nmethod}
  0x00000249ecd08ef0: 0000 00e9 | fbff ffff 

  0x00000249ecd08ef8: ;   {static_stub}
  0x00000249ecd08ef8: 48bb 0000 | 0000 0000 

  0x00000249ecd08f00: ;   {runtime_call nmethod}
  0x00000249ecd08f00: 0000 e9fb 

  0x00000249ecd08f04: ;   {runtime_call ExceptionBlob}
  0x00000249ecd08f04: ffff ffe9 | f410 0bff 
[Deopt Handler Code]
  0x00000249ecd08f0c: e800 0000 | 0048 832c 

  0x00000249ecd08f14: ;   {runtime_call DeoptimizationBlob}
  0x00000249ecd08f14: 2405 e905 | e1ff fef4 | f4f4 f4f4 
[/MachCode]


Compiled method (c2) 18408941 13516   !   4       org.eclipse.core.filesystem.provider.FileStore::fetchInfo (27 bytes)
 total in heap  [0x00000249ecde0790,0x00000249ecde0ac8] = 824
 relocation     [0x00000249ecde08f0,0x00000249ecde0918] = 40
 main code      [0x00000249ecde0920,0x00000249ecde09e8] = 200
 stub code      [0x00000249ecde09e8,0x00000249ecde0a10] = 40
 oops           [0x00000249ecde0a10,0x00000249ecde0a20] = 16
 metadata       [0x00000249ecde0a20,0x00000249ecde0a48] = 40
 scopes data    [0x00000249ecde0a48,0x00000249ecde0a60] = 24
 scopes pcs     [0x00000249ecde0a60,0x00000249ecde0aa0] = 64
 dependencies   [0x00000249ecde0aa0,0x00000249ecde0ab0] = 16
 handler table  [0x00000249ecde0ab0,0x00000249ecde0ac8] = 24

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000249fbcb94a0} 'fetchInfo' '()Lorg/eclipse/core/filesystem/IFileInfo;' in 'org/eclipse/core/filesystem/provider/FileStore'
  #           [sp+0x20]  (sp of caller)
  0x00000249ecde0920: 448b 5208 | 49bb 0000 | 0080 4902 | 0000 4d03 | d349 3bc2 

  0x00000249ecde0934: ;   {runtime_call ic_miss_stub}
  0x00000249ecde0934: 0f85 46dd | f1fe 6690 | 0f1f 4000 
[Verified Entry Point]
  0x00000249ecde0940: 8984 2400 | 80ff ff55 | 4883 ec10 | 4181 7f20 | 0c00 0000 | 0f85 8400 | 0000 488b | ea45 33c9 
  0x00000249ecde0960: ;   {optimized virtual_call}
  0x00000249ecde0960: 4533 c0e8 

  0x00000249ecde0964: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual fetchInfo {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.core.filesystem.provider.FileStore::fetchInfo@3 (line 231)
  0x00000249ecde0964: 5859 f2ff 

  0x00000249ecde0968: ;   {other}
  0x00000249ecde0968: 0f1f 8400 | d801 0000 | 4883 c410 

  0x00000249ecde0974: ;   {poll_return}
  0x00000249ecde0974: 5d49 3ba7 | 4804 0000 | 0f87 4600 | 0000 c344 

  0x00000249ecde0984: ;   {metadata('org/eclipse/core/runtime/CoreException')}
  0x00000249ecde0984: 8b50 0849 | bb00 c014 | 8149 0200 | 0049 b800 | 0000 8049 | 0200 004d | 03c2 4d8b | 5048 4d3b 
  0x00000249ecde09a4: d374 0d48 | 8bd0 4883 

  0x00000249ecde09ac: ;   {runtime_call _rethrow_Java}
  0x00000249ecde09ac: c410 5de9 | 4cf6 fdfe | bad9 0000 

  0x00000249ecde09b8: ;   {runtime_call UncommonTrapBlob}
  0x00000249ecde09b8: 0066 90e8 

  0x00000249ecde09bc: ; ImmutableOopMap {rbp=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.core.filesystem.provider.FileStore::fetchInfo@8 (line 234)
  0x00000249ecde09bc: 4063 f2fe 

  0x00000249ecde09c0: ;   {other}
  0x00000249ecde09c0: 0f1f 8400 | 3002 0001 

  0x00000249ecde09c8: ;   {internal_word}
  0x00000249ecde09c8: 49ba 7509 | deec 4902 | 0000 4d89 | 9760 0400 

  0x00000249ecde09d8: ;   {runtime_call SafepointBlob}
  0x00000249ecde09d8: 00e9 a251 

  0x00000249ecde09dc: ;   {runtime_call StubRoutines (final stubs)}
  0x00000249ecde09dc: f2fe e81d | cef0 fee9 | 72ff ffff 
[Stub Code]
  0x00000249ecde09e8: ;   {no_reloc}
  0x00000249ecde09e8: 48bb 0000 | 0000 0000 

  0x00000249ecde09f0: ;   {runtime_call nmethod}
  0x00000249ecde09f0: 0000 e9fb 

  0x00000249ecde09f4: ;   {runtime_call ExceptionBlob}
  0x00000249ecde09f4: ffff ffe9 | 0496 fdfe 
[Deopt Handler Code]
  0x00000249ecde09fc: e800 0000 | 0048 832c 

  0x00000249ecde0a04: ;   {runtime_call DeoptimizationBlob}
  0x00000249ecde0a04: 2405 e915 | 66f2 fef4 | f4f4 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000249c1a3e2a0, length=38, elements={
0x00000249de8f8cb0, 0x00000249f6f64fa0, 0x00000249f6f659c0, 0x00000249f6f67350,
0x00000249f6f68390, 0x00000249f6f6b1b0, 0x00000249f6f6ec20, 0x00000249f6f6f9a0,
0x00000249f6f751e0, 0x00000249f7066880, 0x00000249f7290690, 0x00000249f8c8a550,
0x00000249fd17b5f0, 0x00000249fd15fed0, 0x00000249fd160b60, 0x00000249f8bb49f0,
0x00000249fd69eb50, 0x00000249fd612c30, 0x00000249fd6132c0, 0x00000249fd613950,
0x00000249c1bcaaf0, 0x00000249c1bcb180, 0x00000249c1bcb810, 0x00000249c1bcd250,
0x00000249c1bcbea0, 0x00000249c1bcdf70, 0x00000249c1bc7670, 0x00000249c1bc9dd0,
0x00000249c1bcd8e0, 0x00000249c1bca460, 0x00000249c1bc6fe0, 0x00000249c1bcc530,
0x00000249c1bccbc0, 0x00000249c1bce600, 0x00000249c1bc8a20, 0x00000249c1bc90b0,
0x00000249c52c8cc0, 0x00000249c52c9350
}

Java Threads: ( => current thread )
  0x00000249de8f8cb0 JavaThread "main"                              [_thread_blocked, id=29204, stack(0x000000e888100000,0x000000e888200000) (1024K)]
  0x00000249f6f64fa0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=30160, stack(0x000000e888700000,0x000000e888800000) (1024K)]
  0x00000249f6f659c0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=30768, stack(0x000000e888800000,0x000000e888900000) (1024K)]
  0x00000249f6f67350 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=33420, stack(0x000000e888900000,0x000000e888a00000) (1024K)]
  0x00000249f6f68390 JavaThread "Attach Listener"            daemon [_thread_blocked, id=29676, stack(0x000000e888a00000,0x000000e888b00000) (1024K)]
  0x00000249f6f6b1b0 JavaThread "Service Thread"             daemon [_thread_blocked, id=32512, stack(0x000000e888b00000,0x000000e888c00000) (1024K)]
  0x00000249f6f6ec20 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=37652, stack(0x000000e888c00000,0x000000e888d00000) (1024K)]
  0x00000249f6f6f9a0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=19428, stack(0x000000e888d00000,0x000000e888e00000) (1024K)]
  0x00000249f6f751e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=39504, stack(0x000000e888e00000,0x000000e888f00000) (1024K)]
  0x00000249f7066880 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=31204, stack(0x000000e888f00000,0x000000e889000000) (1024K)]
  0x00000249f7290690 JavaThread "Notification Thread"        daemon [_thread_blocked, id=36704, stack(0x000000e889000000,0x000000e889100000) (1024K)]
  0x00000249f8c8a550 JavaThread "Active Thread: Equinox Container: 5579f523-6286-4e8d-9a6c-6f187c8e9274"        [_thread_blocked, id=29232, stack(0x000000e889100000,0x000000e889200000) (1024K)]
  0x00000249fd17b5f0 JavaThread "Refresh Thread: Equinox Container: 5579f523-6286-4e8d-9a6c-6f187c8e9274" daemon [_thread_blocked, id=34184, stack(0x000000e889200000,0x000000e889300000) (1024K)]
  0x00000249fd15fed0 JavaThread "Framework Event Dispatcher: Equinox Container: 5579f523-6286-4e8d-9a6c-6f187c8e9274" daemon [_thread_blocked, id=16472, stack(0x000000e889a00000,0x000000e889b00000) (1024K)]
  0x00000249fd160b60 JavaThread "Start Level: Equinox Container: 5579f523-6286-4e8d-9a6c-6f187c8e9274" daemon [_thread_blocked, id=16928, stack(0x000000e889b00000,0x000000e889c00000) (1024K)]
  0x00000249f8bb49f0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=16808, stack(0x000000e889d00000,0x000000e889e00000) (1024K)]
  0x00000249fd69eb50 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=20884, stack(0x000000e88a000000,0x000000e88a100000) (1024K)]
  0x00000249fd612c30 JavaThread "Worker-JM"                         [_thread_blocked, id=31732, stack(0x000000e88a200000,0x000000e88a300000) (1024K)]
  0x00000249fd6132c0 JavaThread "Java indexing"              daemon [_thread_blocked, id=26700, stack(0x000000e88a600000,0x000000e88a700000) (1024K)]
  0x00000249fd613950 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=38964, stack(0x000000e88a800000,0x000000e88a900000) (1024K)]
  0x00000249c1bcaaf0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=32268, stack(0x000000e88aa00000,0x000000e88ab00000) (1024K)]
  0x00000249c1bcb180 JavaThread "Thread-3"                   daemon [_thread_in_native, id=30320, stack(0x000000e88ab00000,0x000000e88ac00000) (1024K)]
  0x00000249c1bcb810 JavaThread "Thread-4"                   daemon [_thread_in_native, id=33436, stack(0x000000e88ac00000,0x000000e88ad00000) (1024K)]
  0x00000249c1bcd250 JavaThread "Thread-5"                   daemon [_thread_in_native, id=26664, stack(0x000000e88ad00000,0x000000e88ae00000) (1024K)]
  0x00000249c1bcbea0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=26148, stack(0x000000e88ae00000,0x000000e88af00000) (1024K)]
  0x00000249c1bcdf70 JavaThread "Thread-7"                   daemon [_thread_in_native, id=32140, stack(0x000000e88af00000,0x000000e88b000000) (1024K)]
  0x00000249c1bc7670 JavaThread "Thread-8"                   daemon [_thread_in_native, id=39680, stack(0x000000e88b000000,0x000000e88b100000) (1024K)]
  0x00000249c1bc9dd0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=37976, stack(0x000000e88b100000,0x000000e88b200000) (1024K)]
  0x00000249c1bcd8e0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=8960, stack(0x000000e88b200000,0x000000e88b300000) (1024K)]
  0x00000249c1bca460 JavaThread "Thread-11"                  daemon [_thread_in_native, id=23120, stack(0x000000e88b300000,0x000000e88b400000) (1024K)]
  0x00000249c1bc6fe0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=26756, stack(0x000000e88b400000,0x000000e88b500000) (1024K)]
  0x00000249c1bcc530 JavaThread "Thread-13"                  daemon [_thread_in_native, id=39208, stack(0x000000e88b500000,0x000000e88b600000) (1024K)]
  0x00000249c1bccbc0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=28276, stack(0x000000e88b600000,0x000000e88b700000) (1024K)]
  0x00000249c1bce600 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=30340, stack(0x000000e88b700000,0x000000e88b800000) (1024K)]
=>0x00000249c1bc8a20 JavaThread "WorkspaceEventsHandler"            [_thread_in_native, id=20412, stack(0x000000e88b900000,0x000000e88ba00000) (1024K)]
  0x00000249c1bc90b0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=31176, stack(0x000000e88ba00000,0x000000e88bb00000) (1024K)]
  0x00000249c52c8cc0 JavaThread "Worker-27"                         [_thread_blocked, id=41408, stack(0x000000e887d00000,0x000000e887e00000) (1024K)]
  0x00000249c52c9350 JavaThread "Worker-28"                         [_thread_blocked, id=44096, stack(0x000000e887e00000,0x000000e887f00000) (1024K)]
Total: 38

Other Threads:
  0x00000249f60e2c80 VMThread "VM Thread"                           [id=7948, stack(0x000000e888600000,0x000000e888700000) (1024K)]
  0x00000249f601d960 WatcherThread "VM Periodic Task Thread"        [id=34944, stack(0x000000e888500000,0x000000e888600000) (1024K)]
  0x00000249de989bf0 WorkerThread "GC Thread#0"                     [id=31040, stack(0x000000e888400000,0x000000e888500000) (1024K)]
  0x00000249f868ae70 WorkerThread "GC Thread#1"                     [id=9948, stack(0x000000e889300000,0x000000e889400000) (1024K)]
  0x00000249f868b210 WorkerThread "GC Thread#2"                     [id=28852, stack(0x000000e889400000,0x000000e889500000) (1024K)]
  0x00000249f868b5b0 WorkerThread "GC Thread#3"                     [id=30360, stack(0x000000e889500000,0x000000e889600000) (1024K)]
  0x00000249f8647b60 WorkerThread "GC Thread#4"                     [id=30804, stack(0x000000e889600000,0x000000e889700000) (1024K)]
  0x00000249f8648310 WorkerThread "GC Thread#5"                     [id=15328, stack(0x000000e889700000,0x000000e889800000) (1024K)]
  0x00000249f86486b0 WorkerThread "GC Thread#6"                     [id=15676, stack(0x000000e889800000,0x000000e889900000) (1024K)]
  0x00000249fd259210 WorkerThread "GC Thread#7"                     [id=26580, stack(0x000000e889900000,0x000000e889a00000) (1024K)]
  0x00000249fd715fe0 WorkerThread "GC Thread#8"                     [id=16428, stack(0x000000e889e00000,0x000000e889f00000) (1024K)]
  0x00000249fd714680 WorkerThread "GC Thread#9"                     [id=20348, stack(0x000000e889f00000,0x000000e88a000000) (1024K)]
Total: 12

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000024980000000-0x0000024980ba0000-0x0000024980ba0000), size 12189696, SharedBaseAddress: 0x0000024980000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000024981000000-0x00000249c1000000, reserved size: 1073741824
Narrow klass base: 0x0000024980000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 24301M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 10240K, used 1397K [0x00000000eab00000, 0x00000000ec180000, 0x0000000100000000)
  eden space 8704K, 2% used [0x00000000eab00000,0x00000000eab3d608,0x00000000eb380000)
  from space 1536K, 75% used [0x00000000ec000000,0x00000000ec120000,0x00000000ec180000)
  to   space 1536K, 0% used [0x00000000ebe80000,0x00000000ebe80000,0x00000000ec000000)
 ParOldGen       total 240128K, used 239967K [0x00000000c0000000, 0x00000000cea80000, 0x00000000eab00000)
  object space 240128K, 99% used [0x00000000c0000000,0x00000000cea57df8,0x00000000cea80000)
 Metaspace       used 83025K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K

Card table byte_map: [0x00000249f3970000,0x00000249f3b80000] _byte_map_base: 0x00000249f3370000

Marking Bits: (ParMarkBitMap*) 0x00007ffbf1ee3260
 Begin Bits: [0x00000249f3ce0000, 0x00000249f4ce0000)
 End Bits:   [0x00000249f4ce0000, 0x00000249f5ce0000)

Polling page: 0x00000249e13c0000

Metaspace:

Usage:
  Non-class:     73.34 MB used.
      Class:      7.74 MB used.
       Both:     81.08 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      74.06 MB ( 58%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       8.38 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      82.44 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  5.51 MB
       Class:  7.63 MB
        Both:  13.14 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 137.44 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1670.
num_arena_deaths: 452.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1319.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 547.
num_chunks_taken_from_freelist: 5980.
num_chunk_merges: 136.
num_chunk_splits: 3344.
num_chunks_enlarged: 1845.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=17124Kb max_used=21945Kb free=102875Kb
 bounds [0x00000249ec250000, 0x00000249ed7c0000, 0x00000249f3780000]
CodeHeap 'profiled nmethods': size=120000Kb used=22539Kb max_used=43456Kb free=97461Kb
 bounds [0x00000249e4780000, 0x00000249e7200000, 0x00000249ebcb0000]
CodeHeap 'non-nmethods': size=5760Kb used=1518Kb max_used=1598Kb free=4241Kb
 bounds [0x00000249ebcb0000, 0x00000249ebf20000, 0x00000249ec250000]
 total_blobs=11455 nmethods=10634 adapters=724
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 18157.493 Thread 0x00000249f6f751e0 25502       3       org.eclipse.core.internal.resources.SaveManager$1::<init> (38 bytes)
Event: 18157.493 Thread 0x00000249f6f751e0 nmethod 25502 0x00000249e5750590 code [0x00000249e5750740, 0x00000249e5750918]
Event: 18157.493 Thread 0x00000249f6f751e0 25503       3       org.eclipse.core.internal.resources.SaveManager$1::run (20 bytes)
Event: 18157.494 Thread 0x00000249f6f751e0 nmethod 25503 0x00000249e5d55190 code [0x00000249e5d55340, 0x00000249e5d554b0]
Event: 18157.494 Thread 0x00000249f6f751e0 25504       1       jdk.xml.internal.JdkXmlFeatures$XmlFeature::defaultValue (5 bytes)
Event: 18157.494 Thread 0x00000249f6f751e0 nmethod 25504 0x00000249ec511b10 code [0x00000249ec511ca0, 0x00000249ec511d70]
Event: 18157.494 Thread 0x00000249f6f751e0 25506   !   3       jdk.xml.internal.XMLSecurityManager::getPropertyConfig (68 bytes)
Event: 18157.496 Thread 0x00000249f6f751e0 nmethod 25506 0x00000249e4947910 code [0x00000249e4947b40, 0x00000249e49481e8]
Event: 18157.496 Thread 0x00000249f6f751e0 25505       1       jdk.xml.internal.JdkXmlFeatures$XmlFeature::systemProperty (5 bytes)
Event: 18157.496 Thread 0x00000249f6f751e0 nmethod 25505 0x00000249ec534e90 code [0x00000249ec535020, 0x00000249ec5350e8]
Event: 18157.496 Thread 0x00000249f6f751e0 25507       1       javax.xml.catalog.CatalogFeatures$Feature::defaultValue (5 bytes)
Event: 18157.496 Thread 0x00000249f6f751e0 nmethod 25507 0x00000249ec540a90 code [0x00000249ec540c20, 0x00000249ec540ce8]
Event: 18157.496 Thread 0x00000249f6f751e0 25508       1       javax.xml.catalog.CatalogFeatures$Feature::hasSystemProperty (5 bytes)
Event: 18157.496 Thread 0x00000249f6f751e0 nmethod 25508 0x00000249ec566610 code [0x00000249ec5667a0, 0x00000249ec566870]
Event: 18157.511 Thread 0x00000249f6f751e0 25509   !   3       org.eclipse.core.internal.events.BuildCommand::getBuilder (45 bytes)
Event: 18157.512 Thread 0x00000249f6f751e0 nmethod 25509 0x00000249e5743290 code [0x00000249e5743480, 0x00000249e5743970]
Event: 18157.513 Thread 0x00000249f6f751e0 25510       3       java.util.concurrent.ConcurrentHashMap$MapEntry::hashCode (16 bytes)
Event: 18157.513 Thread 0x00000249f6f751e0 nmethod 25510 0x00000249e4bce790 code [0x00000249e4bce960, 0x00000249e4bcec20]
Event: 18408.673 Thread 0x00000249f6f751e0 25511       3       java.util.Optional::get (22 bytes)
Event: 18408.674 Thread 0x00000249f6f751e0 nmethod 25511 0x00000249e510e310 code [0x00000249e510e4c0, 0x00000249e510e6e0]

GC Heap History (20 events):
Event: 18132.974 GC heap before
{Heap before GC invocations=4115 (full 11):
 PSYoungGen      total 17408K, used 16995K [0x00000000eab00000, 0x00000000ebe80000, 0x0000000100000000)
  eden space 14848K, 100% used [0x00000000eab00000,0x00000000eb980000,0x00000000eb980000)
  from space 2560K, 83% used [0x00000000ebc00000,0x00000000ebe18c78,0x00000000ebe80000)
  to   space 2560K, 0% used [0x00000000eb980000,0x00000000eb980000,0x00000000ebc00000)
 ParOldGen       total 190976K, used 190761K [0x00000000c0000000, 0x00000000cba80000, 0x00000000eab00000)
  object space 190976K, 99% used [0x00000000c0000000,0x00000000cba4a708,0x00000000cba80000)
 Metaspace       used 83011K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18132.975 GC heap after
{Heap after GC invocations=4115 (full 11):
 PSYoungGen      total 16384K, used 1344K [0x00000000eab00000, 0x00000000ebd80000, 0x0000000100000000)
  eden space 14848K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb980000)
  from space 1536K, 87% used [0x00000000eb980000,0x00000000ebad0040,0x00000000ebb00000)
  to   space 2048K, 0% used [0x00000000ebb80000,0x00000000ebb80000,0x00000000ebd80000)
 ParOldGen       total 193024K, used 192711K [0x00000000c0000000, 0x00000000cbc80000, 0x00000000eab00000)
  object space 193024K, 99% used [0x00000000c0000000,0x00000000cbc31c30,0x00000000cbc80000)
 Metaspace       used 83011K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.834 GC heap before
{Heap before GC invocations=4116 (full 11):
 PSYoungGen      total 16384K, used 16192K [0x00000000eab00000, 0x00000000ebd80000, 0x0000000100000000)
  eden space 14848K, 100% used [0x00000000eab00000,0x00000000eb980000,0x00000000eb980000)
  from space 1536K, 87% used [0x00000000eb980000,0x00000000ebad0040,0x00000000ebb00000)
  to   space 2048K, 0% used [0x00000000ebb80000,0x00000000ebb80000,0x00000000ebd80000)
 ParOldGen       total 193024K, used 192711K [0x00000000c0000000, 0x00000000cbc80000, 0x00000000eab00000)
  object space 193024K, 99% used [0x00000000c0000000,0x00000000cbc31c30,0x00000000cbc80000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.837 GC heap after
{Heap after GC invocations=4116 (full 11):
 PSYoungGen      total 10752K, used 2034K [0x00000000eab00000, 0x00000000ec980000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 2048K, 99% used [0x00000000ebb80000,0x00000000ebd7c930,0x00000000ebd80000)
  to   space 8192K, 0% used [0x00000000eb380000,0x00000000eb380000,0x00000000ebb80000)
 ParOldGen       total 198144K, used 197823K [0x00000000c0000000, 0x00000000cc180000, 0x00000000eab00000)
  object space 198144K, 99% used [0x00000000c0000000,0x00000000cc12fc70,0x00000000cc180000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.853 GC heap before
{Heap before GC invocations=4117 (full 11):
 PSYoungGen      total 10752K, used 10738K [0x00000000eab00000, 0x00000000ec980000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 2048K, 99% used [0x00000000ebb80000,0x00000000ebd7c930,0x00000000ebd80000)
  to   space 8192K, 0% used [0x00000000eb380000,0x00000000eb380000,0x00000000ebb80000)
 ParOldGen       total 198144K, used 197823K [0x00000000c0000000, 0x00000000cc180000, 0x00000000eab00000)
  object space 198144K, 99% used [0x00000000c0000000,0x00000000cc12fc70,0x00000000cc180000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.856 GC heap after
{Heap after GC invocations=4117 (full 11):
 PSYoungGen      total 16896K, used 8192K [0x00000000eab00000, 0x00000000eca80000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 8192K, 100% used [0x00000000eb380000,0x00000000ebb80000,0x00000000ebb80000)
  to   space 11776K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000eca80000)
 ParOldGen       total 200704K, used 200379K [0x00000000c0000000, 0x00000000cc400000, 0x00000000eab00000)
  object space 200704K, 99% used [0x00000000c0000000,0x00000000cc3aee20,0x00000000cc400000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.919 GC heap before
{Heap before GC invocations=4118 (full 11):
 PSYoungGen      total 16896K, used 16896K [0x00000000eab00000, 0x00000000eca80000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 8192K, 100% used [0x00000000eb380000,0x00000000ebb80000,0x00000000ebb80000)
  to   space 11776K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000eca80000)
 ParOldGen       total 200704K, used 200379K [0x00000000c0000000, 0x00000000cc400000, 0x00000000eab00000)
  object space 200704K, 99% used [0x00000000c0000000,0x00000000cc3aee20,0x00000000cc400000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.926 GC heap after
{Heap after GC invocations=4118 (full 11):
 PSYoungGen      total 16384K, used 7366K [0x00000000eab00000, 0x00000000ec680000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 7680K, 95% used [0x00000000ebf00000,0x00000000ec631a70,0x00000000ec680000)
  to   space 8704K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000ebe00000)
 ParOldGen       total 208896K, used 208675K [0x00000000c0000000, 0x00000000ccc00000, 0x00000000eab00000)
  object space 208896K, 99% used [0x00000000c0000000,0x00000000ccbc8e20,0x00000000ccc00000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.943 GC heap before
{Heap before GC invocations=4119 (full 11):
 PSYoungGen      total 16384K, used 16070K [0x00000000eab00000, 0x00000000ec680000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 7680K, 95% used [0x00000000ebf00000,0x00000000ec631a70,0x00000000ec680000)
  to   space 8704K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000ebe00000)
 ParOldGen       total 208896K, used 208675K [0x00000000c0000000, 0x00000000ccc00000, 0x00000000eab00000)
  object space 208896K, 99% used [0x00000000c0000000,0x00000000ccbc8e20,0x00000000ccc00000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18134.949 GC heap after
{Heap after GC invocations=4119 (full 11):
 PSYoungGen      total 17408K, used 8616K [0x00000000eab00000, 0x00000000ec580000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 8704K, 98% used [0x00000000eb580000,0x00000000ebdea000,0x00000000ebe00000)
  to   space 7680K, 0% used [0x00000000ebe00000,0x00000000ebe00000,0x00000000ec580000)
 ParOldGen       total 217088K, used 216717K [0x00000000c0000000, 0x00000000cd400000, 0x00000000eab00000)
  object space 217088K, 99% used [0x00000000c0000000,0x00000000cd3a3700,0x00000000cd400000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.010 GC heap before
{Heap before GC invocations=4120 (full 11):
 PSYoungGen      total 17408K, used 16777K [0x00000000eab00000, 0x00000000ec580000, 0x0000000100000000)
  eden space 8704K, 93% used [0x00000000eab00000,0x00000000eb2f86f8,0x00000000eb380000)
  from space 8704K, 98% used [0x00000000eb580000,0x00000000ebdea000,0x00000000ebe00000)
  to   space 7680K, 0% used [0x00000000ebe00000,0x00000000ebe00000,0x00000000ec580000)
 ParOldGen       total 217088K, used 216717K [0x00000000c0000000, 0x00000000cd400000, 0x00000000eab00000)
  object space 217088K, 99% used [0x00000000c0000000,0x00000000cd3a3700,0x00000000cd400000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.015 GC heap after
{Heap after GC invocations=4120 (full 11):
 PSYoungGen      total 14848K, used 5872K [0x00000000eab00000, 0x00000000ec400000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 6144K, 95% used [0x00000000ebe00000,0x00000000ec3bc090,0x00000000ec400000)
  to   space 7168K, 0% used [0x00000000eb600000,0x00000000eb600000,0x00000000ebd00000)
 ParOldGen       total 225280K, used 225109K [0x00000000c0000000, 0x00000000cdc00000, 0x00000000eab00000)
  object space 225280K, 99% used [0x00000000c0000000,0x00000000cdbd5700,0x00000000cdc00000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.050 GC heap before
{Heap before GC invocations=4121 (full 11):
 PSYoungGen      total 14848K, used 14576K [0x00000000eab00000, 0x00000000ec400000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 6144K, 95% used [0x00000000ebe00000,0x00000000ec3bc090,0x00000000ec400000)
  to   space 7168K, 0% used [0x00000000eb600000,0x00000000eb600000,0x00000000ebd00000)
 ParOldGen       total 225280K, used 225109K [0x00000000c0000000, 0x00000000cdc00000, 0x00000000eab00000)
  object space 225280K, 99% used [0x00000000c0000000,0x00000000cdbd5700,0x00000000cdc00000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.052 GC heap after
{Heap after GC invocations=4121 (full 11):
 PSYoungGen      total 15872K, used 7162K [0x00000000eab00000, 0x00000000ecf80000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 7168K, 99% used [0x00000000eb600000,0x00000000ebcfe9d0,0x00000000ebd00000)
  to   space 14336K, 0% used [0x00000000ec180000,0x00000000ec180000,0x00000000ecf80000)
 ParOldGen       total 230912K, used 230848K [0x00000000c0000000, 0x00000000ce180000, 0x00000000eab00000)
  object space 230912K, 99% used [0x00000000c0000000,0x00000000ce170198,0x00000000ce180000)
 Metaspace       used 83019K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.063 GC heap before
{Heap before GC invocations=4122 (full 11):
 PSYoungGen      total 15872K, used 15866K [0x00000000eab00000, 0x00000000ecf80000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 7168K, 99% used [0x00000000eb600000,0x00000000ebcfe9d0,0x00000000ebd00000)
  to   space 14336K, 0% used [0x00000000ec180000,0x00000000ec180000,0x00000000ecf80000)
 ParOldGen       total 230912K, used 230848K [0x00000000c0000000, 0x00000000ce180000, 0x00000000eab00000)
  object space 230912K, 99% used [0x00000000c0000000,0x00000000ce170198,0x00000000ce180000)
 Metaspace       used 83020K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.065 GC heap after
{Heap after GC invocations=4122 (full 11):
 PSYoungGen      total 10240K, used 1437K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 1536K, 93% used [0x00000000ec180000,0x00000000ec2e75a0,0x00000000ec300000)
  to   space 5632K, 0% used [0x00000000eb800000,0x00000000eb800000,0x00000000ebd80000)
 ParOldGen       total 238080K, used 237862K [0x00000000c0000000, 0x00000000ce880000, 0x00000000eab00000)
  object space 238080K, 99% used [0x00000000c0000000,0x00000000ce849810,0x00000000ce880000)
 Metaspace       used 83020K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.074 GC heap before
{Heap before GC invocations=4123 (full 11):
 PSYoungGen      total 10240K, used 10141K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 1536K, 93% used [0x00000000ec180000,0x00000000ec2e75a0,0x00000000ec300000)
  to   space 5632K, 0% used [0x00000000eb800000,0x00000000eb800000,0x00000000ebd80000)
 ParOldGen       total 238080K, used 237862K [0x00000000c0000000, 0x00000000ce880000, 0x00000000eab00000)
  object space 238080K, 99% used [0x00000000c0000000,0x00000000ce849810,0x00000000ce880000)
 Metaspace       used 83020K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18135.075 GC heap after
{Heap after GC invocations=4123 (full 11):
 PSYoungGen      total 14336K, used 1040K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 5632K, 18% used [0x00000000eb800000,0x00000000eb904030,0x00000000ebd80000)
  to   space 2048K, 0% used [0x00000000ec000000,0x00000000ec000000,0x00000000ec200000)
 ParOldGen       total 239104K, used 239095K [0x00000000c0000000, 0x00000000ce980000, 0x00000000eab00000)
  object space 239104K, 99% used [0x00000000c0000000,0x00000000ce97dea0,0x00000000ce980000)
 Metaspace       used 83020K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18408.666 GC heap before
{Heap before GC invocations=4124 (full 11):
 PSYoungGen      total 14336K, used 9740K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 8704K, 99% used [0x00000000eab00000,0x00000000eb37f298,0x00000000eb380000)
  from space 5632K, 18% used [0x00000000eb800000,0x00000000eb904030,0x00000000ebd80000)
  to   space 2048K, 0% used [0x00000000ec000000,0x00000000ec000000,0x00000000ec200000)
 ParOldGen       total 239104K, used 239095K [0x00000000c0000000, 0x00000000ce980000, 0x00000000eab00000)
  object space 239104K, 99% used [0x00000000c0000000,0x00000000ce97dea0,0x00000000ce980000)
 Metaspace       used 83025K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}
Event: 18408.667 GC heap after
{Heap after GC invocations=4124 (full 11):
 PSYoungGen      total 10240K, used 1152K [0x00000000eab00000, 0x00000000ec180000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 1536K, 75% used [0x00000000ec000000,0x00000000ec120000,0x00000000ec180000)
  to   space 1536K, 0% used [0x00000000ebe80000,0x00000000ebe80000,0x00000000ec000000)
 ParOldGen       total 240128K, used 239967K [0x00000000c0000000, 0x00000000cea80000, 0x00000000eab00000)
  object space 240128K, 99% used [0x00000000c0000000,0x00000000cea57df8,0x00000000cea80000)
 Metaspace       used 83025K, committed 84416K, reserved 1179648K
  class space    used 7928K, committed 8576K, reserved 1048576K
}

Dll operation events (14 events):
Event: 0.018 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 0.072 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.198 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 0.205 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 0.209 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 0.214 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.239 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 0.351 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 2.319 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.440 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
Event: 4.445 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll
Event: 5.647 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-91090918\jna11171974750513244952.dll
Event: 5.769 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\sunmscapi.dll
Event: 6.003 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\extnet.dll

Deoptimization events (20 events):
Event: 18117.819 Thread 0x00000249fdd11750 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000249ed2f34a8 relative=0x0000000000003448
Event: 18117.819 Thread 0x00000249fdd11750 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000249ed2f34a8 method=org.eclipse.jdt.internal.core.util.Util.getUnresolvedJavaElement(Lorg/eclipse/jdt/internal/compiler/lookup/MethodBinding;Lorg/eclipse/jdt/core/WorkingCopyOwner;Lorg/ecli
Event: 18117.819 Thread 0x00000249fdd11750 DEOPT PACKING pc=0x00000249ed2f34a8 sp=0x000000e88a9fe5a0
Event: 18117.819 Thread 0x00000249fdd11750 DEOPT UNPACKING pc=0x00000249ebd06da2 sp=0x000000e88a9fe560 mode 2
Event: 18117.819 Thread 0x00000249fdd11750 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000249ed2f34a8 relative=0x0000000000003448
Event: 18117.819 Thread 0x00000249fdd11750 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000249ed2f34a8 method=org.eclipse.jdt.internal.core.util.Util.getUnresolvedJavaElement(Lorg/eclipse/jdt/internal/compiler/lookup/MethodBinding;Lorg/eclipse/jdt/core/WorkingCopyOwner;Lorg/ecli
Event: 18117.819 Thread 0x00000249fdd11750 DEOPT PACKING pc=0x00000249ed2f34a8 sp=0x000000e88a9fe650
Event: 18117.819 Thread 0x00000249fdd11750 DEOPT UNPACKING pc=0x00000249ebd06da2 sp=0x000000e88a9fe610 mode 2
Event: 18118.213 Thread 0x00000249c52c9350 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000249ec54da1c relative=0x000000000000051c
Event: 18118.213 Thread 0x00000249c52c9350 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000249ec54da1c method=org.eclipse.jdt.internal.compiler.ast.Expression.checkNPE(Lorg/eclipse/jdt/internal/compiler/lookup/BlockScope;Lorg/eclipse/jdt/internal/compiler/f
Event: 18118.213 Thread 0x00000249c52c9350 DEOPT PACKING pc=0x00000249ec54da1c sp=0x000000e887efd230
Event: 18118.213 Thread 0x00000249c52c9350 DEOPT UNPACKING pc=0x00000249ebd06da2 sp=0x000000e887efd1a0 mode 2
Event: 18118.415 Thread 0x00000249c52c6560 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000249ed2f34a8 relative=0x0000000000003448
Event: 18118.415 Thread 0x00000249c52c6560 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000249ed2f34a8 method=org.eclipse.jdt.internal.core.util.Util.getUnresolvedJavaElement(Lorg/eclipse/jdt/internal/compiler/lookup/MethodBinding;Lorg/eclipse/jdt/core/WorkingCopyOwner;Lorg/ecli
Event: 18118.415 Thread 0x00000249c52c6560 DEOPT PACKING pc=0x00000249ed2f34a8 sp=0x000000e88a1fe870
Event: 18118.415 Thread 0x00000249c52c6560 DEOPT UNPACKING pc=0x00000249ebd06da2 sp=0x000000e88a1fe830 mode 2
Event: 18118.415 Thread 0x00000249c52c6560 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000249ed2f34a8 relative=0x0000000000003448
Event: 18118.415 Thread 0x00000249c52c6560 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000249ed2f34a8 method=org.eclipse.jdt.internal.core.util.Util.getUnresolvedJavaElement(Lorg/eclipse/jdt/internal/compiler/lookup/MethodBinding;Lorg/eclipse/jdt/core/WorkingCopyOwner;Lorg/ecli
Event: 18118.415 Thread 0x00000249c52c6560 DEOPT PACKING pc=0x00000249ed2f34a8 sp=0x000000e88a1fe920
Event: 18118.415 Thread 0x00000249c52c6560 DEOPT UNPACKING pc=0x00000249ebd06da2 sp=0x000000e88a1fe8e0 mode 2

Classes loaded (20 events):
Event: 892.172 Loading class java/util/stream/DistinctOps$1$2
Event: 892.172 Loading class java/util/stream/DistinctOps$1$2 done
Event: 1078.613 Loading class jdk/internal/icu/text/NormalizerBase$NFCModeImpl
Event: 1078.613 Loading class jdk/internal/icu/text/NormalizerBase$NFCModeImpl done
Event: 1078.613 Loading class jdk/internal/icu/impl/Norm2AllModes$NFCSingleton
Event: 1078.613 Loading class jdk/internal/icu/impl/Norm2AllModes$NFCSingleton done
Event: 1078.614 Loading class java/nio/StringCharBuffer
Event: 1078.614 Loading class java/nio/StringCharBuffer done
Event: 1078.632 Loading class java/io/CharArrayReader
Event: 1078.632 Loading class java/io/CharArrayReader done
Event: 1078.632 Loading class java/util/regex/Pattern$Bound
Event: 1078.632 Loading class java/util/regex/Pattern$Bound done
Event: 1482.129 Loading class java/util/Collections$EmptyListIterator
Event: 1482.129 Loading class java/util/Collections$EmptyListIterator done
Event: 13451.696 Loading class java/nio/channels/CompletionHandler
Event: 13451.700 Loading class java/nio/channels/CompletionHandler done
Event: 17498.818 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode
Event: 17498.820 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode done
Event: 17498.821 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin
Event: 17498.822 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin done

Classes unloaded (20 events):
Event: 5103.572 Thread 0x00000249f60e2c80 Unloading class 0x000002498179e800 'java/lang/invoke/LambdaForm$MH+0x000002498179e800'
Event: 5103.572 Thread 0x00000249f60e2c80 Unloading class 0x0000024981808c00 'java/lang/invoke/LambdaForm$MH+0x0000024981808c00'
Event: 5103.572 Thread 0x00000249f60e2c80 Unloading class 0x00000249817d8800 'java/lang/invoke/LambdaForm$MH+0x00000249817d8800'
Event: 5103.572 Thread 0x00000249f60e2c80 Unloading class 0x00000249817d8000 'java/lang/invoke/LambdaForm$MH+0x00000249817d8000'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981500400 'java/lang/invoke/LambdaForm$MH+0x0000024981500400'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981306c00 'java/lang/invoke/LambdaForm$BMH+0x0000024981306c00'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x00000249811b1c00 'java/lang/invoke/LambdaForm$MH+0x00000249811b1c00'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x00000249811b1800 'java/lang/invoke/LambdaForm$MH+0x00000249811b1800'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x00000249811b1000 'java/lang/invoke/LambdaForm$MH+0x00000249811b1000'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981163000 'java/lang/invoke/LambdaForm$MH+0x0000024981163000'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981162800 'java/lang/invoke/LambdaForm$MH+0x0000024981162800'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981162000 'java/lang/invoke/LambdaForm$MH+0x0000024981162000'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981161c00 'java/lang/invoke/LambdaForm$MH+0x0000024981161c00'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981161800 'java/lang/invoke/LambdaForm$MH+0x0000024981161800'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981019000 'java/lang/invoke/LambdaForm$MH+0x0000024981019000'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981018000 'java/lang/invoke/LambdaForm$MH+0x0000024981018000'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981017c00 'java/lang/invoke/LambdaForm$MH+0x0000024981017c00'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981017400 'java/lang/invoke/LambdaForm$MH+0x0000024981017400'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981016400 'java/lang/invoke/LambdaForm$MH+0x0000024981016400'
Event: 5103.573 Thread 0x00000249f60e2c80 Unloading class 0x0000024981000400 'java/lang/invoke/LambdaForm$MH+0x0000024981000400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 17262.351 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb47da20}> (0x00000000eb47da20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17262.546 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae14278}> (0x00000000eae14278) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17273.585 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb0fc500}> (0x00000000eb0fc500) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17273.755 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaf70fc8}> (0x00000000eaf70fc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17492.217 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead694b0}> (0x00000000ead694b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17492.563 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb182e70}> (0x00000000eb182e70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17498.799 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabe5f28}> (0x00000000eabe5f28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17499.382 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eacbae70}> (0x00000000eacbae70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17857.451 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab7a608}> (0x00000000eab7a608) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17857.709 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac990c0}> (0x00000000eac990c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17905.898 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabd4280}> (0x00000000eabd4280) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 17906.071 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabf0a18}> (0x00000000eabf0a18) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 18114.815 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab271b8}> (0x00000000eab271b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 18114.849 Thread 0x00000249c52c6bf0 Implicit null exception at 0x00000249ed6866c5 to 0x00000249ed688084
Event: 18114.852 Thread 0x00000249c52c6bf0 Implicit null exception at 0x00000249ed6866c5 to 0x00000249ed688084
Event: 18114.852 Thread 0x00000249c52c6bf0 Implicit null exception at 0x00000249ed6866c5 to 0x00000249ed688084
Event: 18114.853 Thread 0x00000249c52c6bf0 Implicit null exception at 0x00000249eca7b821 to 0x00000249eca7da47
Event: 18115.017 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb0a0a40}> (0x00000000eb0a0a40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 18134.821 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb2febe0}> (0x00000000eb2febe0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 18134.997 Thread 0x00000249fd6132c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae732e0}> (0x00000000eae732e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 18135.074 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 18135.075 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 18136.075 Executing VM operation: Cleanup
Event: 18136.075 Executing VM operation: Cleanup done
Event: 18158.302 Executing VM operation: Cleanup
Event: 18158.302 Executing VM operation: Cleanup done
Event: 18188.016 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 18188.016 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 18188.017 Executing VM operation: RendezvousGCThreads
Event: 18188.017 Executing VM operation: RendezvousGCThreads done
Event: 18248.202 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 18248.202 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 18248.202 Executing VM operation: RendezvousGCThreads
Event: 18248.202 Executing VM operation: RendezvousGCThreads done
Event: 18308.206 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 18308.206 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 18308.206 Executing VM operation: RendezvousGCThreads
Event: 18308.206 Executing VM operation: RendezvousGCThreads done
Event: 18408.666 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 18408.667 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e6fc8610
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e7001010
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e7011b10
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e7018210
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e701df10
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e702be90
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e704aa10
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e70aa210
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e70b9790
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e7104610
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e7119e10
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e713c110
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e718de10
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e71aed90
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e71b0310
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e71b2990
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e71b3390
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e71b3a10
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e71b7f10
Event: 18115.147 Thread 0x00000249f60e2c80 flushing  nmethod 0x00000249e71b8790

Events (20 events):
Event: 17792.385 Thread 0x00000249c52c9350 Thread added: 0x00000249c52c7fa0
Event: 17821.253 Thread 0x00000249fdd13eb0 Thread exited: 0x00000249fdd13eb0
Event: 17852.397 Thread 0x00000249c52c9350 Thread exited: 0x00000249c52c9350
Event: 17856.545 Thread 0x00000249c52c99e0 Thread added: 0x00000249c52c8cc0
Event: 17965.902 Thread 0x00000249c52c6560 Thread exited: 0x00000249c52c6560
Event: 17966.013 Thread 0x00000249c52c99e0 Thread exited: 0x00000249c52c99e0
Event: 18025.915 Thread 0x00000249fdd14540 Thread exited: 0x00000249fdd14540
Event: 18085.923 Thread 0x00000249fdd0f680 Thread exited: 0x00000249fdd0f680
Event: 18114.308 Thread 0x00000249c52c7fa0 Thread added: 0x00000249c52c9350
Event: 18114.351 Thread 0x00000249f6f6f9a0 Thread added: 0x00000249fe8a1330
Event: 18114.641 Thread 0x00000249fe8a1330 Thread exited: 0x00000249fe8a1330
Event: 18115.274 Thread 0x00000249f6f751e0 Thread added: 0x00000249c2ca0620
Event: 18117.728 Thread 0x00000249c1bc90b0 Thread added: 0x00000249c52c6560
Event: 18119.242 Thread 0x00000249c2ca0620 Thread exited: 0x00000249c2ca0620
Event: 18157.496 Thread 0x00000249c52c7fa0 Thread added: 0x00000249fdd10a30
Event: 18157.496 Thread 0x00000249fdd10a30 Thread exited: 0x00000249fdd10a30
Event: 18194.820 Thread 0x00000249fdd11750 Thread exited: 0x00000249fdd11750
Event: 18219.312 Thread 0x00000249c52c7fa0 Thread exited: 0x00000249c52c7fa0
Event: 18254.835 Thread 0x00000249c52c6560 Thread exited: 0x00000249c52c6560
Event: 18314.842 Thread 0x00000249c52c6bf0 Thread exited: 0x00000249c52c6bf0


Dynamic libraries:
0x00007ff7b84e0000 - 0x00007ff7b84ee000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffcfadb0000 - 0x00007ffcfafc7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffcf9680000 - 0x00007ffcf9744000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffcf80d0000 - 0x00007ffcf847d000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffcf8990000 - 0x00007ffcf8aa1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffce7130000 - 0x00007ffce7239000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ffcf9fc0000 - 0x00007ffcfa16e000 	C:\Windows\System32\USER32.dll
0x00007ffcf8ab0000 - 0x00007ffcf8ad6000 	C:\Windows\System32\win32u.dll
0x00007ffcf02e0000 - 0x00007ffcf02fe000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffcf9650000 - 0x00007ffcf9679000 	C:\Windows\System32\GDI32.dll
0x00007ffcf8870000 - 0x00007ffcf8989000 	C:\Windows\System32\gdi32full.dll
0x00007ffcf8570000 - 0x00007ffcf860a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffcf0950000 - 0x00007ffcf0968000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffcfab20000 - 0x00007ffcfabd2000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffcf8d80000 - 0x00007ffcf8e27000 	C:\Windows\System32\msvcrt.dll
0x00007ffcf8f50000 - 0x00007ffcf8ff9000 	C:\Windows\System32\sechost.dll
0x00007ffcf8480000 - 0x00007ffcf84a8000 	C:\Windows\System32\bcrypt.dll
0x00007ffce7d30000 - 0x00007ffce7fc3000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955\COMCTL32.dll
0x00007ffcfaa00000 - 0x00007ffcfab14000 	C:\Windows\System32\RPCRT4.dll
0x00007ffceee80000 - 0x00007ffceee8a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffcfa9b0000 - 0x00007ffcfa9e1000 	C:\Windows\System32\IMM32.DLL
0x00007ffce0160000 - 0x00007ffce0813000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ffcf9170000 - 0x00007ffcf91ce000 	C:\Windows\System32\SHLWAPI.dll
0x00007ffcf9750000 - 0x00007ffcf9fb7000 	C:\Windows\System32\SHELL32.dll
0x00007ffce7050000 - 0x00007ffce706e000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ffcfa800000 - 0x00007ffcfa9a5000 	C:\Windows\System32\ole32.dll
0x00007ffcfa270000 - 0x00007ffcfa5fe000 	C:\Windows\System32\combase.dll
0x00007ffcfac90000 - 0x00007ffcfad67000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffcf8e50000 - 0x00007ffcf8ec1000 	C:\Windows\System32\WS2_32.dll
0x00007ffcf1de0000 - 0x00007ffcf1e14000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffcf87f0000 - 0x00007ffcf886b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffcdfe90000 - 0x00007ffce00c7000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ffcdfab0000 - 0x00007ffcdfae2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ffce7fd0000 - 0x00007ffce8077000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ffcf9000000 - 0x00007ffcf90f9000 	C:\Windows\System32\shcore.dll
0x00007ffcdf980000 - 0x00007ffcdfaa3000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ffcf7680000 - 0x00007ffcf76e9000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ffcfa9f0000 - 0x00007ffcfa9f8000 	C:\Windows\System32\psapi.dll
0x00007ffcdf510000 - 0x00007ffcdf51c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ffcf91d0000 - 0x00007ffcf9644000 	C:\Windows\System32\setupapi.dll
0x00007ffcdf3f0000 - 0x00007ffcdf50b000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ffcddb90000 - 0x00007ffcddda0000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ffcf0980000 - 0x00007ffcf098c000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffc9a600000 - 0x00007ffc9a68d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ffbf1230000 - 0x00007ffbf1fc0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffcf6f80000 - 0x00007ffcf6fcd000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffcf6f60000 - 0x00007ffcf6f73000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffce08c0000 - 0x00007ffce0efc000 	C:\Windows\SYSTEM32\TSafeDoc64.dll
0x00007ffcf71b0000 - 0x00007ffcf71c8000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffcf0930000 - 0x00007ffcf093a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffcf5780000 - 0x00007ffcf59b2000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffcc65a0000 - 0x00007ffcc65d2000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffcd46c0000 - 0x00007ffcd46cf000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffcc44e0000 - 0x00007ffcc44ff000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffcf6060000 - 0x00007ffcf695f000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffcf5f20000 - 0x00007ffcf605f000 	C:\Windows\SYSTEM32\wintypes.dll
0x00007ffcf8000000 - 0x00007ffcf8027000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffcbf410000 - 0x00007ffcbf428000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffcc5090000 - 0x00007ffcc50a0000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffcf1ca0000 - 0x00007ffcf1dd6000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffcbeff0000 - 0x00007ffcbf006000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffcc4470000 - 0x00007ffcc4480000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffcf6ff0000 - 0x00007ffcf6ffc000 	C:\Windows\SYSTEM32\secur32.dll
0x00007ffcf7470000 - 0x00007ffcf74b3000 	C:\Windows\SYSTEM32\SSPICLI.DLL
0x00007ffc99eb0000 - 0x00007ffc99ef5000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffcbc760000 - 0x00007ffcbc76a000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
0x00007ffcb6ea0000 - 0x00007ffcb6eab000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll
0x00007ffcf78d0000 - 0x00007ffcf78eb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffcf7170000 - 0x00007ffcf71a5000 	C:\Windows\system32\rsaenh.dll
0x00007ffcf7770000 - 0x00007ffcf7798000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffcf78f0000 - 0x00007ffcf78fc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffcf6c30000 - 0x00007ffcf6c5d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffcf8ae0000 - 0x00007ffcf8ae9000 	C:\Windows\System32\NSI.dll
0x00007ffc9ac00000 - 0x00007ffc9ac49000 	C:\Users\<USER>\AppData\Local\Temp\jna-91090918\jna11171974750513244952.dll
0x00007ffcf1c80000 - 0x00007ffcf1c99000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffcf1ae0000 - 0x00007ffcf1aff000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffcb6e90000 - 0x00007ffcb6e9e000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\sunmscapi.dll
0x00007ffcf8680000 - 0x00007ffcf87e6000 	C:\Windows\System32\CRYPT32.dll
0x00007ffcf7a70000 - 0x00007ffcf7a9e000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffcf7a30000 - 0x00007ffcf7a67000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffcc4ff0000 - 0x00007ffcc503b000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\SangforNspX64.dll
0x00007ffcf6ca0000 - 0x00007ffcf6d98000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffceb560000 - 0x00007ffceb56a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffcf03d0000 - 0x00007ffcf0453000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffcb33a0000 - 0x00007ffcb33a9000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955;c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-91090918;C:\Program Files (x86)\Sangfor\SSL\ClientComponent

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\25046f62500b28466155dbc8db461daf\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\25046f62500b28466155dbc8db461daf\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4b6665279886199cd0b9ff7636b3154c-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\25046f62500b28466155dbc8db461daf\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Java\jdk1.8.0_333
PATH=E:\Program Files\Python313\Scripts\;E:\Program Files\Python313\;e:\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Common Files\Siemens\Automation\Simatic OAM\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;D:\Program Files\Java\jdk1.8.0_333\bin;D:\Program Files\Java\jdk1.8.0_333\jre\bin;D:\soft\NetSarang\Xshell 7\;D:\soft\NetSarang\Xftp 7\;D:\tools\apache-maven-3.6.3\bin;D:\soft\mysql-8.0.29-winx64\bin;D:\soft\nginx-1.20.2;D:\tools\nvmnoinstall;D:\tools\nvmnoinstall\nodejs;D:\tools\nvmnoinstall\node_global;D:\MinGW\bin;C:\Program Files (x86)\Git\cmd;D:\Program Files (x86)\pcsuite\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\cpolar\;E:\cursor\resources\app\bin;e:\cursor\resources\app\bin;e:\cursor\resources\app\bin;D:\Program Files (x86)\NetSarang\Xshell 8\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\cpolar\;E:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts
USERNAME=a1215
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
OS uptime: 1 days 6:45 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x32, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, pku, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 24301M (8090M free)
TotalPageFile size 44441M (AvailPageFile size 6177M)
current process WorkingSet (physical memory assigned to process): 448M, peak: 1020M
current process commit charge ("private bytes"): 629M, peak: 1123M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
