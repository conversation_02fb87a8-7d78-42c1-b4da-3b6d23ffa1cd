# 项目相关配置
ruoyi:
  # 名称
  name: control-system
  # 版本
  version: 3.1.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/web_file）
#  profile: /home/<USER>/web_file
  profile: D:/ruoyi/uploadPath2
#  dxfFile: C:/home/<USER>/control_system/dxf_file
  dxfFile: /home/<USER>/control_system/dxf_file
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  #任务控制反馈地址
  stateFeedbackUrl: http://127.0.0.1:8080/common/task/stateFeedback
  #任务信息上报地址
  reportPlateInfoUrl: http://127.0.0.1:8080/common/task/reportPlateInfo
  #接口配置地址
  sysUrl: /speedbot/v1/sort/manageSystem
  reportUrl: /speedbot/v1/sort/reportSystem
  #需要检测的工位
  checkStationProductId: 2
  #需要提前手动结束任务的工位
  needDealStationProductId: 1
  #线程执行时限，单位：分钟
  threadRunMinute: 10


# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 7855
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.ruoyi: info
    mapper: info
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  40MB
      # 设置总上传的文件大小
      max-request-size:  50MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
#    host: redis
    # 端口，默认为6379S
    port: 6379
    # 密码
    password:
    # 连接超时时间 s
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

## MyBatis配置
##mybatis:
##  # 搜索指定包别名
##  typeAliasesPackage: com.ruoyi.**.domain,com.speedbot.**.domain.**
##  # 配置mapper的扫描，找到所有的mapper.xml映射文件
##  mapperLocations: classpath*:mapper/**/*Mapper.xml
##  # 加载全局的配置文件
##  configLocation: classpath:mybatis/mybatis-config.xml
##  configuration:
##    map-underscore-to-camel-case: true
#
#mybatis-plus:
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  typeAliasesPackage: com.ruoyi.**.domain
#  configuration:
#    map-underscore-to-camel-case: false

# PageHelper分页插件
pagehelper:
  # helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  autoRuntimeDialect: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 条码设置
print:
  barCodeWidth: 300
  barCodeheight: 400
  barCodeAndWordHeight: 20